{"\"REGULAR.SUBMIT_AND_SAVE": "Submit & Save Business", "ADVANCED.CAROUSEL.BEST.DEALS.SUBHEADING": "Best deals", "ADVANCED.CAROUSEL.BEST.DEALS.TITLE": "Shop smart with our amazing deals", "ADVANCED.CAROUSEL.DEAL_20_OVER.TITLE": "Deals Over {percentage}% Off!", "ADVANCED.CAROUSEL.NEW.PRODUCTS.SUBHEADING": "New arrivals", "ADVANCED.CAROUSEL.NEW.PRODUCTS.TITLE": "Latest additions ready to be discovered", "ADVANCED.CAROUSEL.TOP.SELLERS.SUBHEADING": "Top Sellers", "ADVANCED.CAROUSEL.TOP.SELLERS.TITLE": "Our tried and true customer favourites", "APP.USER-ACCOUNT.BUSINESSTAG.AUTHORITIES": " Authorities", "APP.USER-ACCOUNT.BUSINESSTAG.DEALER": " Dealer", "APP.USER-ACCOUNT.BUSINESSTAG.HANDCRAFT": " Handcraft", "APP.USER-ACCOUNT.BUSINESSTAG.HORECA": "Restaurant, Hotel, Caterer", "APP.USER-ACCOUNT.BUSINESSTAG.SERVICE_PROVIDER": " Service provider", "APP.USER-ACCOUNT.BUSINESSTAG.SOCIAL_SERVICES-EDUCATION-HEALTH": " Social services, education, health", "APP.USER-ACCOUNT.COMMON.ADDRESS_ALREADY_EXISTS": " Address already exists", "APP.USER-ACCOUNT.COMMON.ADDRESS_BATCH_CREATION.HAS_INVALID_ADDRESS": " One of the addresses is invalid and was not saved.", "APP.USER-ACCOUNT.COMMON.ADDRESS_DOES_NOT_EXISTS": " Address does not exist", "APP.USER-ACCOUNT.COMMON.ADDRESS.DEFAULT_ADDRESS_DOES_NOT_EXISTS": " No Default address", "APP.USER-ACCOUNT.COMMON.CHANGE_PASSWORD_DIFFERENT_PASSWORDS_ERROR": " New passwords don't match", "APP.USER-ACCOUNT.COMMON.COUNTRY_NOT_FOUND_ERROR": " Country not found", "APP.USER-ACCOUNT.COMMON.EMAIL_ALREADY_IN_USE": " This email is currently used.", "APP.USER-ACCOUNT.COMMON.IDAM_ACCOUNT_LOCKED": " User with such email does not exists", "APP.USER-ACCOUNT.COMMON.INVALID_CREDS_PASSWORD_CHANGE": " Sorry due to security reasons you cannot change your password now. Please try again in <lock.minutes> minutes.", "APP.USER-ACCOUNT.COMMON.INVALID_CREDS_PASSWORD_CHANGE_1ST_TRY": " Current password is incorrect.", "APP.USER-ACCOUNT.COMMON.INVALID_CREDS_PASSWORD_CHANGE_2ND_TRY": " Current password is incorrect. You can try 3 more times.", "APP.USER-ACCOUNT.COMMON.INVALID_CREDS_PASSWORD_CHANGE_3RD_TRY": " Current password is incorrect. You can try 2 more times.", "APP.USER-ACCOUNT.COMMON.INVALID_CREDS_PASSWORD_CHANGE_4TH_TRY": " Current password is incorrect. You can try 1 more time.", "APP.USER-ACCOUNT.COMMON.NOT_UNIQUE_METRO_CARD_ERROR": " This METRO card is already in use.", "APP.USER-ACCOUNT.COMMON.TRY_AGAIN_LATER": " An error has occurred. Try again later.", "APP.USER-ACCOUNT.COMMON.VALIDATION.ADDRESS_LINE_ERROR": " Only letters, digits, symbols . , - ' / and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.ADDRESSLINE2_MAX_LENGTH": " Max length for field 'addressLine2' is { limit }", "APP.USER-ACCOUNT.COMMON.VALIDATION.CITY_FORMAT_ERROR": " Only letters, symbols . , - ' / and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.CITY_IS_REQUIRED": " Field 'city' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.CITY_MAX_LENGTH": " Max length for field 'city' is { limit }", "APP.USER-ACCOUNT.COMMON.VALIDATION.COUNTRY_INVALID": " Country format is invalid.", "APP.USER-ACCOUNT.COMMON.VALIDATION.COUNTRY_IS_REQUIRED": " Field 'country' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.COUNTRY_NOT_FOUND_ERROR": " Country not found", "APP.USER-ACCOUNT.COMMON.VALIDATION.COUNTRY_REQUIRED_LENGTH": " Length for field 'country' is 2", "APP.USER-ACCOUNT.COMMON.VALIDATION.DEFAULTBILLING_IS_BOOLEAN": " Field 'defaultBilling' should be boolean", "APP.USER-ACCOUNT.COMMON.VALIDATION.DEFAULTSHIPPING_IS_BOOLEAN": " Field 'defaultShipping' should be boolean", "APP.USER-ACCOUNT.COMMON.VALIDATION.EMAIL_ALREADY_IN_USE": " This email is currently used.", "APP.USER-ACCOUNT.COMMON.VALIDATION.FIELD_IS_REQUIRED": " The field can not be empty", "APP.USER-ACCOUNT.COMMON.VALIDATION.FIRSTNAME_IS_REQUIRED": " Field 'firstName' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.FIRSTNAME_MAX_LENGTH": " Max length for field 'firstName' is { limit } characters", "APP.USER-ACCOUNT.COMMON.VALIDATION.HOUSE_NUMBER_IS_REQUIRED": " Field 'House Number' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.INVALID_METRO_CARD": " Card number format is invalid.", "APP.USER-ACCOUNT.COMMON.VALIDATION.LASTNAME_IS_REQUIRED": " Field 'lastName' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.LASTNAME_MAX_LENGTH": " Max length for field 'lastName' is { limit } characters", "APP.USER-ACCOUNT.COMMON.VALIDATION.LATIN_ERROR": " Only letters, symbols . , - ' and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.LATIN_WITH_AMP": " Only letters, digits, symbols . , - ' & and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.LATIN_WITH_DIGITS_ERROR": " Only letters, digits, symbols . , - ' and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.LATIN_WITH_SLASH": " Only letters, digits, symbols . , - ' / and spaces are available here", "APP.USER-ACCOUNT.COMMON.VALIDATION.LEGAL_FORM_TYPE_INVALID": " Legal form type format is invalid", "APP.USER-ACCOUNT.COMMON.VALIDATION.NOT_VALID": " Not valid", "APP.USER-ACCOUNT.COMMON.VALIDATION.NOT_VALID_FORMAT_DATETIME": " This is not a valid format for datetime.", "APP.USER-ACCOUNT.COMMON.VALIDATION.ONLY_DIGITS_ALLOWED": " Only digits are allowed.", "APP.USER-ACCOUNT.COMMON.VALIDATION.PASSWORD_REQUIREMENTS_ERROR": " The password must meet security requirements below.", "APP.USER-ACCOUNT.COMMON.VALIDATION.POSTALCODE_INVALID": " Postal code format is invalid.", "APP.USER-ACCOUNT.COMMON.VALIDATION.POSTALCODE_IS_REQUIRED": " Field 'postal code' is required", "APP.USER-ACCOUNT.COMMON.VALIDATION.POSTALCODE_LENGTH": " Length for field 'postal code' is { limit }", "APP.USER-ACCOUNT.COMMON.VALIDATION.RESTRICTED_POSTAL_CODE_ERROR": "Invalid zip code", "APP.USER-ACCOUNT.COMMON.VALIDATION.SHOULD_BE_GREATER_THAN_OR_EQUAL_ZERO": " This value should be greater than or equal to 0.", "APP.USER-ACCOUNT.COMMON.VALIDATION.SHOULD_BE_INTEGER_ONLY": " The value should be integer.", "APP.USER-ACCOUNT.COMMON.VALIDATION.STREET_IS_REQUIRED": " Field 'street' is required", "APP.USER-ACCOUNT.CONSUMER.UNEXPECTED_RESPONSE": "Unexpected response", "APP.USER-ACCOUNT.ENTREPRENEUR.ACCOUNT_IS_BLOCKED_ERROR": " Account is blocked", "APP.USER-ACCOUNT.ENTREPRENEUR.ADDRESS_BATCH_CREATION.HAS_INVALID_ADDRESS": " One of the addresses is invalid and was not saved.", "APP.USER-ACCOUNT.ENTREPRENEUR.CARD_HOLDER_NOT_FOUND_ERROR": " Card number or Post code is incorrect. Please try again.", "APP.USER-ACCOUNT.ENTREPRENEUR.METRONOM_SERVICE_ERROR": " An error has occurred. Please, check the card number and try again.", "APP.USER-ACCOUNT.ENTREPRENEUR.REGISTRATION_METRO_CARD.CUSTOMER_STATUS_NOT_ACTIVE": " Your Metro account is blocked.", "APP.USER-ACCOUNT.ENTREPRENEUR.REGISTRATION_METRO_CARD.LEGAL_ADDRESS_NOT_FOUND": " Legal address not found.", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.CARDNUMBER_IS_REQUIRED": " Field 'Metro card number' is required.", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.COMPANY_IS_REQUIRED": " Field 'Company Name' is required", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.COMPANY_MAX_LENGTH": " Max length for field 'Company Name' is { limit }", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.COMPANY_NAME_IS_REQUIRED": " Company name is required", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.MAX_LENGTH_ERROR": " This value is too long. It should have { limit } character or less.|This value is too long. It should have { limit }Ωcharacters or less.", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.METROCARDID_ALREADY_IN_USE": " This Metro card is currently used.", "APP.USER-ACCOUNT.ENTREPRENEUR.VALIDATION.PHONE_IS_REQUIRED": " Phone number is required", "APP.USER-ACCOUNT.MAIL.METRO_SUPPORT_TEAM": "APP.USER-ACCOUNT.MAIL.METRO_SUPPORT_TEAM", "APP.USER-ACCOUNT.MAIL.REGARDS": " <PERSON><PERSON>,", "APP.USER-ACCOUNT.MAIL.SIR_MADAM": " Dear Sir/<PERSON>am!", "APP.USER-ACCOUNT.MAIL.WELCOME_CONSUMER_TEXT": " We are happy to welcome you as registered customer in Metro Market.", "APP.USER-ACCOUNT.MAIL.WELCOME_ENTREPRENEUR_TEXT": " We are happy to welcome you as registered Entrepreneur in Metromarket.", "APP.USER-ACCOUNT.MAIL.WELCOME_FILL_MANDATORY": " To access all system features, please fill mandatory Account information.", "APP.USER-ACCOUNT.MAIL.WELCOME_SUBJECT": " Metro Marketplace account registration completed", "APPROVALS_NAV_TITLE": "Approvals", "BRAND_PAGE.ACCORDION.BACK": "Back to", "BRAND_PAGE.ACCORDION.TITLE": "Categories", "BUY_AGAIN_NAV_TITLE": "Buy Again", "CART_NAV_TITLE": "<PERSON><PERSON>", "CART.ACCOUNT_PENDING_VALIDATION_CUSTOMER_SUPPORT.LINK": "contact customer support for more information.", "CART.ACCOUNT_PENDING_VALIDATION.MESSAGE": "Your account is still in the validation process. Please try again later or ", "CART.ACCOUNT_PENDING_VALIDATION.TITLE": "Pending Validation", "CART.ALERT.REGION_NOT_AVAILABLE": "<strong>Some products</strong> in your cart are <strong>not available</strong> for your <strong>location.</strong> Please look for alternative products or update your account address <a className=\"text-info-main underline\" href=\"{addressesLink}\">here.</a>", "CART.BUTTON.CHECKOUT": "Proceed to Checkout", "CART.BUTTON.OUR_TOP_OFFERS": "Our top offers", "CART.BUY_AGAIN.CAROUSAL.ADD_TO_CART.MESSAGE": "Added", "CART.BUY_AGAIN.CAROUSAL.ADD_TO_CART.PLACEHOLDER": "Add to Basket", "CART.BUY_AGAIN.CAROUSAL.ADDED_TO_CART.MESSAGE": "Added to cart", "CART.CART_PAGE.ERROR.SERVER_ERROR": "Sorry, we are currently experiencing a problem. Please, try again or refresh the page.", "CART.CART_PAGE.TEXT.DISCOUNT_CODE_TOOLTIP": "Only one voucher can be used per order on the METRO marketplace. Currently, discounts only apply to products from the seller <strong className=\"text-blue-shade-60\">METRO</strong>.", "CART.CART_PAGE.TOOLTIP_TITLE.SUBSIDIARY_MINIMUM_THRESHOLD": "Minimum order value required", "CART.CART_PAGE.TOOLTIP.SUBSIDIARY_MINIMUM_THRESHOLD": "As you are using a subsidiary account, your order must exceed a certain amount to qualify.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.CURBSIDE_DELIVERY": "Your item will be delivered to the curb of your selected address.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.EXPLANATION_PART_1": "When choosing Curbside and/or Place of use, the carrier will notify you of the delivery date by email prior to delivery.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.EXPLANATION_PART_2": "Delivery costs may vary depending on your final order. If you order multiple products, they may be delivered in separate shipments. Estimated delivery dates can be found for your products in the cart.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.FAQ_LINK_TEXT": "FAQ for Delivery & Tracking.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.FAQ_TEXT": "Still have questions, view our", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.PARCEL_DELIVERY": "Delivery of smaller items by parcel courier services.", "CART.DELIVERY_OPTIONS_DETAILS.TEXT.PLACE_OF_USE": "Your item will be delivered to the desired location.", "CART.DELIVERY_OPTIONS_DETAILS.TITLE.CURBSIDE_DELIVERY": "Curbside delivery", "CART.DELIVERY_OPTIONS_DETAILS.TITLE.PARCEL_DELIVERY": "Parcel delivery", "CART.DELIVERY_OPTIONS_DETAILS.TITLE.PLACE_OF_USE": "Delivery to Place of use", "CART.DELIVERY_OPTIONS.TEXT.BULKY_PRODUCTS": "Delivery for these bulky products", "CART.DELIVERY_OPTIONS.TEXT.CURBSIDE": "Curbside delivery", "CART.DELIVERY_OPTIONS.TEXT.EXTRA_PRODUCT": "extra unit", "CART.DELIVERY_OPTIONS.TEXT.FREE": "Free", "CART.DELIVERY_OPTIONS.TEXT.PARCEL": "Parcel delivery", "CART.DELIVERY_OPTIONS.TEXT.PARCEL_AND_FREIGHT_FORWARDING": "Parcel and freight forwarding delivery", "CART.DELIVERY_OPTIONS.TEXT.PLACE_OF_USE": "Place of use delivery", "CART.DELIVERY_OPTIONS.TEXT.SHIPPING_COST": "Shipping cost", "CART.DELIVERY_OPTIONS.TEXT.SHIPPING_ESTIMATION": "Shipping estimation", "CART.DELIVERY_OPTIONS.TEXT.TOOLTIP": "Products shown here require similar shipping restrictions and are calculated as a group. Not all products will arrive in the same shipment.", "CART.DIALOG.B2B_OFFERS.BODY_1": "If you want to proceed with your personal account, the offers valid for only businesses will be removed from your cart.", "CART.DIALOG.B2B_OFFERS.BODY_2": "Please go back to switch to your business account.", "CART.DIALOG.B2B_OFFERS.CHECKOUT": "Proceed to checkout", "CART.DIALOG.B2B_OFFERS.GO_BACK": "Go back", "CART.DIALOG.B2B_OFFERS.TITLE": "Please switch to your business account to buy the offers that are valid for only businesses.", "CART.DIALOG.FAKE.DOOR.TEST.POPUP.CHECKOUT": "Proceed to checkout", "CART.DIALOG.FAKE.DOOR.TEST.POPUP.MESSAGE": "At the moment it’s not possible to use this option. We’re just evaluating your interest in this option.", "CART.DIALOG.FAKE.DOOR.TEST.POPUP.TITLE": "Thank you for your interest!", "CART.DIALOG.FORCE_CHECKOUT.CANCEL": "Return to cart", "CART.DIALOG.FORCE_CHECKOUT.CHECKOUT": "Proceed to checkout", "CART.DIALOG.FORCE_CHECKOUT.MESSAGE": "Your are about to proceed to the checkout with unavailable products in your cart. Any unavailable product will be removed from your order. ", "CART.DIALOG.FORCE_CHECKOUT.TITLE": "Your cart is incomplete", "CART.EMPTY_CART.BUTTON.TITLE": "Go to Promotions", "CART.EMPTY_CART.LINK": "continue shopping", "CART.EMPTY_CART.MESSAGE": "Don’t know where to start? ", "CART.EMPTY_CART.SUBTITLE": "There are no items in your cart, you can add items to your cart, or you can ", "CART.EMPTY_CART.TITLE": "Your shopping cart is empty", "CART.EMPTY_CART.UNDO.TITLE": "Undo", "CART.ERROR.OUT_OF_STOCK": "Out of stock", "CART.ERROR.OUT_OF_STOCK_ALL_OFFERS": "Your cart contains products that are unavailable, please check the product page for other available offers", "CART.HEADING.ITEMS_IN_CART": "Items in Your Cart:", "CART.ITEMS_SECTION.ARIA_LABEL": "<PERSON><PERSON>ufswagen", "CART.MERGE.ALERT_PREFIX": "Now you can order them all together.", "CART.MERGE.ALERT_SUFFIX": "We’ve added items that were left in the cart from your previous visit.", "CART.MESSAGE.WILL_REMOVE": "will be removed from your shopping cart.", "CART.MINI_CART.PRODUCT_ADDED.MOBILE": "was added to your shopping cart", "CART.NOTIFICATION.ORDER_UPDATED.MESSAGE": "This may be a result of your location being updated during the checkout and might affect the total price. ", "CART.NOTIFICATION.ORDER_UPDATED.TITLE": "Order Updated", "CART.NOTIFICATION.PRICE_CHANGE.MESSAGE": "Prices of one or more products in your cart have been updated.", "CART.NOTIFICATION.PRICE_CHANGE.TITLE": "Price change:", "CART.NOTIFICATION.REMOVE_SUCCESS": "was removed from your Cart", "CART.OFFER_CHECKOUT.TEXT.AGE_CHECK": "18+ Age check required", "CART.OFFER_CHECKOUT.TOOLTIP.AGE_CHECK_TOOLTIP": "Sale of alcohol to persons under 18 is prohibited. Alcohol abuse is harmful to health. Use responsibly and moderately. This product is not recommended for pregnant women.", "CART.OFFER_LINE.ALERT.VAS_PRODUCT_MODIFIED": " You’ve added another unit of a product already in your cart. Please note that the same number of additional services (i.e. Installation service) will be automatically included for each unit of this product in your cart.", "CART.OFFER_LINE.ALERT.VAS_REMOVED": "We’re sorry, but the additional service(s) you selected for installation are no longer available. They have been removed from your cart.", "CART.OFFER_LINE.RETURN_OF_OLD_DEVICE": "Return of old device", "CART.OFFER_LINE.VALIDATION.REGION_NOT_AVAILABLE": "This product is unavailable in your location", "CART.OFFER_LINE.VALUE_ADDED_SERVICES.DETAILS": "Details", "CART.OFFER_LINE.VALUE_ADDED_SERVICES.TITLE": "Additional services", "CART.OFFER.A11Y.PRICE": "Price", "CART.OFFER.BUTTON.REMOVE": "Remove", "CART.OFFER.CHECH_PRODUCT_PAGE": "Check our product page for other offers", "CART.OFFER.DISCOUNT": "Discount", "CART.OFFER.ERROR.INACTIVE_OFFER": " Not available anymore", "CART.OFFER.ERROR.INCOMPATIBLE_BUSINESS_MODEL": "Dieses <PERSON> gilt exklusiv nur für Geschäftskunden", "CART.OFFER.ERROR.INVALID_DESTINATION_REGION": "This product is unavailable in your location", "CART.OFFER.ERROR.NOT_ENOUGH_STOCK": " Offer is out of stock", "CART.OFFER.ERROR.OFFER_NOT_FOUND": " Not available anymore", "CART.OFFER.ERROR.STOCK_LIMIT": "Stock limit", "CART.OFFER.ERROR.UNDELIVERABLE_REGION": "This product is unavailable in your location", "CART.OFFER.HAS_FREIGHT": "freight", "CART.OFFER.LABEL.B2B_ONLY": "Only for business customers", "CART.OFFER.LABEL.MSRP": "MSRP", "CART.OFFER.LINK.PRODUCT_DATA_SHEET": "EU product data sheet", "CART.OFFER.NEW-PRICE": "New Price", "CART.OFFER.PARCEL_SHIPPING": "Parcel Shipping", "CART.OFFER.PRODUCT_ID": "ID: ", "CART.OFFER.SHIPPING.DELIVERY_DAYS": "Delivery in {min}-{max} business days", "CART.OFFER.TITLE.PRICE_PER_UNIT": "Unit price", "CART.OFFER.TITLE.PRICE_UNIT": "Unit", "CART.OFFER.UNAVAILABLE_PRODUCT": "Product is currently unavailable", "CART.OFFER.VAT_INCL": "VAT Incl.", "CART.RECOMMENDATION.VAT_INCL": "VAT incl.", "CART.RECOMMENDATIONS.TEXT.MIGHT_ALSO_LIKE": "You might also like:", "CART.REGIONS.BALEARIC_ISLANDS": "Balearic Islands", "CART.REVIEW_PAGE.TITLE.ADDITIONAL_SERVICES": "Additional services:", "CART.SELLER_CURBSIDE.TITLE": "Curbside products sold by", "CART.SELLER_PLACE_OF_USE.TITLE": "Place of use products sold by", "CART.SELLER.FREE_SHIPPING": "Free Shipping", "CART.SELLER.SHIPPING": "Shipping", "CART.SELLER.SUBTOTAL": "Subtotal", "CART.SELLER.TITLE": "Sold by", "CART.SELLER.TITLE.SOLD_PROVIDED": "Sold/Provided by", "CART.SHOPPING_CART.TITLE.OVERVIEW": "Overview", "CART.SIDEBAR_DETAILS.WEEE.FREE_SERVICE": "Free service", "CART.SIDEBAR_DETAILS.WEEE.IMPORTANT_INFORMATION": "<h4>Important Information</h4>\n<ul>\n  <li>You can return up to as many devices as you have ordered (1:1 return).</li>\n  <li>The device you return has to be from the same category and perform functions similar to those of the device you purchased.</li>\n  <li>The device you return has to be of similar dimensions and weight as the device you purchased.</li>\n  <li>The old device must be disconnected and stand freely to be picked up.</li>\n  <li>Remove all contents, water residue, easily removable batteries, and light bulbs from the appliance. Recycle them separately.</li>\n  <li>Defrost refrigerators and freezers.</li>\n</ul>", "CART.SIDEBAR_DETAILS.WEEE.INTRO_TEXT": "When you order devices from certain categories, you are entitled to return an old device of the same type. The device you return will be recycled and/or disposed of.", "CART.SIDEBAR_DETAILS.WEEE.MIDDLE_TEXT": "For items not sold by METRO seller, a 3rd party seller will contact you to arrange the return of your old device.", "CART.SIDEBAR_DETAILS.WEEE.RETURN_BULKY_PRODUCTS": "Return of bulky products", "CART.SIDEBAR_DETAILS.WEEE.RETURN_BULKY_PRODUCTS.OPTION_1": "Select “return of an old device” during your order.", "CART.SIDEBAR_DETAILS.WEEE.RETURN_BULKY_PRODUCTS.OPTION_2": "Parcel vs Bulky", "CART.SIDEBAR_DETAILS.WEEE.RETURN_BULKY_PRODUCTS.OPTION_2.1": "The carrier will contact you for items shipped as bulky goods.", "CART.SIDEBAR_DETAILS.WEEE.RETURN_BULKY_PRODUCTS.OPTION_2.2": "You will receive a return label for items shipped as a parcel.", "CART.TEXT.REMOVED": "Removed", "CART.TITLE": "<PERSON><PERSON>", "CART.TITLE.PRODUCT_COUNT": "{count} {count, plural, one{product} other{products}}", "CART.TITLE.PRODUCT_COUNT_one": "Product", "CART.TITLE.PRODUCT_COUNT_other": "Products", "CART.TOTALS.ADDITIONAL_SERVICES": "Additional services", "CART.TOTALS.DISCOUNT": "Discount", "CART.TOTALS.INSTALLATION_SERVICE.TITLE": "Installation at the place of use", "CART.TOTALS.SHIPPING": "Shipping", "CART.TOTALS.SUBTOTAL": "Subtotal", "CART.TOTALS.SUBTOTAL_AND_SHIPPING": "Subtotal and shipping", "CART.TOTALS.SUBTOTAL.VAT_EXCL": "(VAT Excl.)", "CART.TOTALS.SUBTOTAL.VAT_INCL": "(VAT Incl.)", "CART.TOTALS.TAXES": "VAT", "CART.TOTALS.TEXT.SUMMARY_TITLE": "Summary", "CART.TOTALS.TOTAL": "Total", "CART.TOTALS.TOTAL.VAT_EXCL": "(VAT Excl.)", "CART.TOTALS.TOTAL.VAT_INCL": "(VAT Incl.)", "CART.TOTALS.TOTAL.VAT_INFO": "(incl. VAT)", "CART.TOTALS.VALUE_ADDED_SERVICES.TITLE": "Additional services", "CART.UPDATE_QUANTITY.ERROR.QUANTITY_NOT_CHANGE": "We are unable to change the quantity, please try again in a few minutes.", "CART.WARNING.HAS_B2B_OFFER": "You are currently using your personal account, there are offers in your cart that are valid only for businesses. <b>Please go to your profile to switch to your business account.</b>", "CART.WARNING.HAS_INVALID_OFFER": "Some offers in your Cart are not valid. Please check. You are able to proceed Checkout with only available offers", "CART.WARNING.HAS_INVALID_VALUE_ADDED_SERVICE": "Some additional services in your Cart are not valid. Please check them.", "CART.WARNING.OUT_OF_STOCK": "Your cart contains products that are unavailable. These products will be removed from your cart when you proceed to checkout.", "CART.WARNING.QUANTITY_INFO_TOOLTIP": "Please note that the same number of <strong>additional services</strong> (i.e. Installation service) will be <strong>automatically included</strong> for <strong>each unit added of this product</strong> in your cart.", "CART.WARNING.QUANTITY_INFO.MESSAGE": "Adding more units also increases installation services.", "CART.WARNING.STOCK_LIMIT": "Stock limit", "CART.WARNING.STOCK_LIMIT_MESSAGE": "One, or more, of the products you have selected has reached it’s available stock limit.", "CART.WARNNING.QUANTITY_ERROR_LINK": "check product page", "CART.WARNNING.QUANTITY_ERROR_PREFIX": "Please", "CART.WARNNING.QUANTITY_ERROR.SUFFIX": "for other available offers", "CATALOG.CATALOG_PAGE.ADD_TO_CART.SHIPPING_DISCLAIMER": "Plus Ship costs", "CATALOG.CATALOG_PAGE.AI_INFO_TOOLBAR": "METRO AI Explorer is a beta feature that generates content suggestions and product recommendations based on your search, with a focus on HoReCa needs.", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER": "AI Explorer", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER_EXPLANATION": "This page is part of a test of our new AI-powered explorer.", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER.LINK": "Switch to classic search results", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.CATEGORY_CHILDREN_TITLE": "Shop by category", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.FILTERS_BUTTON.TEXT": "Filters", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.PRODUCTS_COUNT": "{count} {count, plural, =1{product} other{products}}", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.PRODUCTS_COUNT.PLURAL": "{productCount} products", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.PRODUCTS_COUNT.SINGULAR": "{productCount} products", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.PROMOTION_TITLE": "Top sales", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SEARCH.CORRECTION": "No result for \"{searchTerm}\"", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SEARCH.IMAGE": "Results for image search", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SEARCH.PRODUCED_BY_": "Our brands", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SEARCH.TEXT": "Search result for ", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.BEST_MATCH.TOOLTIP": "Products are ranked based on price, level of detail of the product description, latest update and number of views and purchases.", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.BEST_MATCH.TOOLTIP_INFO": "Results ranking", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.LABEL": "Product list sort selector", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.LABEL.BEST_MATCH_DESC": "Best results", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.LABEL.NEW_ARRIVALS": "Newest Arrivals", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.LABEL.PRICE_HIGH": "Price: Low to High", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.SORT_TOGGLER.LABEL.PRICE_LOW": "Price: High to Low", "CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.TITLE": "Catalog page", "CATALOG.CATALOG_PAGE.CATEGORY_HUB.SHOW_ALL": "Show all", "CATALOG.CATALOG_PAGE.FILTERS.BUTTON.SHOW": "Show {count} Products", "CATALOG.CATALOG_PAGE.FILTERS.PRICE.MAX": "max", "CATALOG.CATALOG_PAGE.FILTERS.PRICE.MIN": "min", "CATALOG.CATALOG_PAGE.FILTERS.PRICE.UNITS.DISCLAIMER": "Filter by price per single unit.", "CATALOG.CATALOG_PAGE.FILTERS.RESET": "Reset to default", "CATALOG.CATALOG_PAGE.FILTERS.TAG.REMOVE_WARNING": "Unfortunately, this selection does not return any results.", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.ASSORTMENT": "Assortment", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND": "Brand", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.CATEGORIES": "Categories", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.CATEGORY": "Category", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.FILTERS": "Filter", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.PRICE": "Price", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.PROMOTION": "Promotion", "CATALOG.CATALOG_PAGE.FILTERS.TITLE.SUBCATEGORIES": "Subcategories", "CATALOG.CATALOG_PAGE.HUB.SHOW_ALL": "Show all", "CATALOG.CATALOG_PAGE.LOADING": "Loading...", "CATALOG.CATALOG_PAGE.LOADING.TITLE": " Fitting products being loaded... ", "CATALOG.CATALOG_PAGE.MSRP.LABEL": "MSRP", "CATALOG.CATALOG_PAGE.PAGINATOR.LOAD_MORE.LABEL": "Show Next <%= limit %> Products", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.B2B_OFFER_ONLY.LABEL": "Register to see price", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.SERIES": "Serie", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-AVAILABLE-SIZES": "More options available", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-AVAILABLE-SIZES-PLURAL": "Available in more sizes", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-COLORS": "+{ number } color", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-COLORS-PLURAL": "+{ number } colors", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-COLORS-SIZES": "+{number} colors / options", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-SIZES": "+{number} option", "CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-SIZES-PLURAL": "+{number} sizes", "CATALOG.CATALOG_PAGE.PRODUCT.ENERGY_EFFICIENCY": "EE: {value}", "CATALOG.CATALOG_PAGE.PRODUCT.ENERGY_EFFICIENCY_FROM_TO": "Range: {from} till {to}", "CATALOG.CATALOG_PAGE.PRODUCT.PROMO_ENDS_IN": "Deal ends in", "CATALOG.CATALOG_PAGE.PRODUCT.STRIKETHROUGH_PRICE": "prev", "CATALOG.CATALOG_PAGE.PRODUCT.VAT_LABEL": "VAT incl.", "CATALOG.CATALOG_PAGE.PROMO_HEADER.BLACK_FRIDAY_CONTENT": "Discover our Black Deals. Save up to 55% and benefit from free shipping. <p class=\"{class}\"><img src=\"{iconUrl}\"><span>Valid from 21.11 – 4.12.19</span></p>", "CATALOG.CATALOG_PAGE.PROMO_HEADER.CHRISTMAS_CONTENT": "Discover our exclusive Christmas offers and Save up to 45% <p> and benefit from free shipping while stocks last.</p>", "CATALOG.CATALOG_PAGE.PROMO_HEADER.DEFAULT_CONTENT": "This is the default content for the promo header content", "CATALOG.CATALOG_PAGE.PROMO_HEADER.METRO_CONTENT": "Discover over 100,000 products in our online marketplace. Order now and benefit from bulk discounts and free shipping.", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.CATEGORY": "More Products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.EXPANDED_SEARCH": "More Products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.FILTER": "More Products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.MOST_BOUGHT_PRODUCTS": "Most Bought Products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.MOST_POPULAR_PRODUCTS": "Popular products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.MOST_RELEVANT_PRODUCTS": "Popular products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.PARSED_TEXT_PRODUCTS": "Alternative products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.PRODUCT": "Products", "CATALOG.CATALOG_PAGE.RECOMMENDED_PRODUCT.PROMO": "Current offers", "CATALOG.CATALOG_PAGE.SEO_CONTENT.SHOW_MORE": "Read more", "CATALOG.CATALOG_PAGE.SEO_CONTENT.TOC": "Table of contents", "CATALOG.CATALOG_PAGE.ZERO_RESULT.ALTERNATIVE_PRODUCTS.TITLE": "Alternative products", "CATALOG.CATALOG_PAGE.ZERO_RESULT.CATEGORIES_TITLE": "Highlighted categories", "CATALOG.CATALOG_PAGE.ZERO_RESULT.EXPIRED_PAGE.SUBTITLE": "Go ahead explore our available categories instead.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.EXPIRED_PAGE.TITLE": "Unfortunately this page has expired.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.FILTER.SUBTITLE": "Please change your filter settings.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.FILTER.TITLE": "Unfortunately there are no suitable results.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.FOOD_SOON.SUBTITLE": "Unfortunately, your search for food did not yield any results.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.FOOD_SOON.TITLE": "Stay tuned - soon you will also find food products on the Metro Marketplace!", "CATALOG.CATALOG_PAGE.ZERO_RESULT.NO_FOOD.SUBTITLE": "Unfortunately, your search for food did not yield any results.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.NO_FOOD.TITLE": "The METRO marketplace only offers NON-FOOD products.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.OUT_OF_SCOPE.CATEGORY_GRID_TITLE": "Categories you might be interested in", "CATALOG.CATALOG_PAGE.ZERO_RESULT.OUT_OF_SCOPE.DELIVERY_SERVICE_LINK_IN": " in", "CATALOG.CATALOG_PAGE.ZERO_RESULT.OUT_OF_SCOPE.DELIVERY_SERVICE_LINK_OUR": "our ", "CATALOG.CATALOG_PAGE.ZERO_RESULT.OUT_OF_SCOPE.DELIVERY_SERVICE_LINK_SEARCH": "Search ", "CATALOG.CATALOG_PAGE.ZERO_RESULT.OUT_OF_SCOPE.MAIN_TITLE": "Sorry, we don’t sell “[Query]” in our ", "CATALOG.CATALOG_PAGE.ZERO_RESULT.POPULAR_PRODUCTS.TITLE": "Popular products", "CATALOG.CATALOG_PAGE.ZERO_RESULT.RECOMMENDED_PRODUCTS.TITLE": "Recommended for you", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.IMAGE": "Sorry, no matches for your image.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.IMAGE_SUBTITLE": "Try uploading a clearer image or using different keywords", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.MAIN_TITLE": "Sorry, we couldn’t find any matches for ", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.NO_TERM_TITLE": "Sorry, we have not found any results", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.SUBTITLE": "Please try searching for a different product instead.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.TITLE": "Unfortunately your search did not yield any suitable results.", "CATALOG.CATALOG_PAGE.ZERO_RESULT.TRIED_THESE": "Have you tried any of these?", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.CONDITIONS_LINK.LABEL": "Check details", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DATA_SHEETS.TITLE": "Further technical information", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DATA.TITLE": "Product characteristics", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DESCRIPTION.TITLE": "Product description", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DOCUMENTS.TITLE": "Documents and additional specs", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.KEY_FEATURES.TITLE": "Key features", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.MANUFACTURER.TITLE": "Product data", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.PRODUCT_SAFETY_DOWNLOAD": "Download safety regulation documentation", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.PRODUCT_SAFETY_TITLE": "Product safety information", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.PRODUCT_SAFETY.TITLE": "Product Safety", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_ADRESS": "<PERSON>ress", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_DESCRIPTION": "<b>Responsible Person (Product Safety)</b> is an economic operator based in the EU who is responsible for the product placed on the EU Single Market.", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_EMAIL": "Email / Website", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_LINK": "Product safety contact point", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_NAME": "Name", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_PHONE": "Phone", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.RESPONSIBLE_PERSON_TITLE": "Responsible Person (Product Safety)", "CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.SHOW_MORE_CAT": "{count} more", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_BUYBOX_TITLE": "Additional services:", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_DESCRIPTION": "When you buy an electrical and electronic appliance, the law gives you the option of returning an old appliance free of charge. The prerequisite is that the old appliance belongs to the same type as the new one and essentially fulfils the same functions. ", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_DESCRIPTION_2": "For the sake of the environment: the transport of electrical appliances also consumes resources and pollutes our environment. Please help us to keep the consumption of resources as low as possible and only order the free return of old appliances if you actually need it.", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_DETAILS": "Details", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_FREE_COST": "Free", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_LINK_TEXT": "Details", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_PROVIDED_BY_TITLE": "Service provided by:", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_STEP_1": "After ordering, you will receive a link to confirm the collection of the old appliance (please note that the link expires in 24 hours).", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_STEP_2": "The collection will be coordinated between you and the relevant carrier.", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_STEPS": "It works as simple as that:", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_SUB_TITLE": "Free of charge", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES_TITLE": "Taking back old device service", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES.PER_UNIT": "unit", "CATALOG.DETAILS_PAGE.ADDITIONAL_SERVICES.TITLE": "Select a service", "CATALOG.DETAILS_PAGE.ALCOHOL_WARNING": "Sale of alcohol to persons under the age of 18 is prohibited. By ordering this product, you confirm being 18 years or older. Alcohol abuse is harmful to health. Use responsibly and moderately. This product is not recommended for pregnant women.", "CATALOG.DETAILS_PAGE.BLACK_DEALS.LABEL": "Black Deal", "CATALOG.DETAILS_PAGE.BLACK_DEALS.LIMITED_STOCK.LABEL": "Limited stock", "CATALOG.DETAILS_PAGE.BLACK_DEALS.NAVIGATION.LABEL": "Black Deals %", "CATALOG.DETAILS_PAGE.BREADCRUMBS.HOME.LABEL": "Home", "CATALOG.DETAILS_PAGE.BUYBOX.ADD_BUSINESS_DETAILS_MSG.DESCRIPTION": "to benefit from this offer.", "CATALOG.DETAILS_PAGE.BUYBOX.ADD_BUSINESS_DETAILS.LINK.TITLE": "Add a Business profile", "CATALOG.DETAILS_PAGE.BUYBOX.B2B_PRODUCT.BUTTON_LABEL": "Buy as Business", "CATALOG.DETAILS_PAGE.BUYBOX.BUTTON.LABEL.OUT_OF_STOCK": "Out of stock", "CATALOG.DETAILS_PAGE.BUYBOX.CALLBACK_BUTTON": "Request free callback ", "CATALOG.DETAILS_PAGE.BUYBOX.CALLBACK_TITLE": "<b>Do you still have questions?</b> Book a consultation with our experts.", "CATALOG.DETAILS_PAGE.BUYBOX.CANCELLATION_POLICY.LABEL": "Cancellation Policy", "CATALOG.DETAILS_PAGE.BUYBOX.CANCELLATION_POLICY.LABEL.NEW": "Cancellation Policy", "CATALOG.DETAILS_PAGE.BUYBOX.DATA_POLICY.LABEL": "Data Policy", "CATALOG.DETAILS_PAGE.BUYBOX.ECO.FEE.INFORMATION": "Eco-contribution included in price, if applicable.", "CATALOG.DETAILS_PAGE.BUYBOX.EMPTY_OFFERS.BUTTON_LABEL": "Register Now", "CATALOG.DETAILS_PAGE.BUYBOX.EMPTY_OFFERS.MESSAGE": "Only verified business customers can order this product", "CATALOG.DETAILS_PAGE.BUYBOX.EMPTY_OFFERS.TITLE": "Register or log-in as Business customer to see {count} {count, plural, =1{offer} other{offers}}", "CATALOG.DETAILS_PAGE.BUYBOX.EPR.INFORMATION": "Do you want to recycle your old product? ", "CATALOG.DETAILS_PAGE.BUYBOX.FREE_SHIPPING": "Free Shipping", "CATALOG.DETAILS_PAGE.BUYBOX.FREE_SHIPPING.LABEL": "Free shipping", "CATALOG.DETAILS_PAGE.BUYBOX.FREE_SHIPPING.LABEL_SHIPPING": "Delivery", "CATALOG.DETAILS_PAGE.BUYBOX.FREE_SHIPPING.TEXT": "Free Shipping", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.FRANCE.CURB": "Free shipping for any extra bulky item Sold by METRO", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD": "Buy {quantity} to get <b>free shipping</b>", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD_MORE": "Add {quantity} more to get <b>Free Delivery</b>", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD_REACHED": "You reached Free delivery for this product.", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD_SIDEBAR": "Based on your current cart composition, you can add {quantity} units to get <b>Free delivery</b>.", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD_SIDEBAR_INFO": "Orders above {price} € from the Seller {seller} (exceptions might apply)", "CATALOG.DETAILS_PAGE.BUYBOX.FREE.SHIPPING.THRESHOLD_SIDEBAR_MORE": "Based on your current cart composition, you can add {quantity} more to get <b>Free delivery</b>.", "CATALOG.DETAILS_PAGE.BUYBOX.FREIGHT_FORWARDING.LINK": "see details.", "CATALOG.DETAILS_PAGE.BUYBOX.FREIGHT_FORWARDING.TEXT": "This product may have a delivery service specifically for large or heavy items.", "CATALOG.DETAILS_PAGE.BUYBOX.HEADING.LABEL": "Best offer", "CATALOG.DETAILS_PAGE.BUYBOX.IMPRINT.LABEL": "Imprint", "CATALOG.DETAILS_PAGE.BUYBOX.LEARN_MORE_TITLE": "Learn more", "CATALOG.DETAILS_PAGE.BUYBOX.LOGIN_OR_REGISTER": "Login or Register", "CATALOG.DETAILS_PAGE.BUYBOX.LOGIN_OR_REGISTER_MSG.DESCRIPTION": "as Business to benefit from this offer.", "CATALOG.DETAILS_PAGE.BUYBOX.LOGIN_OR_REGISTER_MSG.TITLE": "This product is exclusive for Business Customers.", "CATALOG.DETAILS_PAGE.BUYBOX.LOGIN.TITLE": "<PERSON><PERSON>", "CATALOG.DETAILS_PAGE.BUYBOX.METROX.LOGIN_OR_REGISTER_MSG.DESCRIPTION": "with a Business account to benefit from this offer.", "CATALOG.DETAILS_PAGE.BUYBOX.METROX.LOGIN_OR_REGISTER_MSG.TITLE": "This offer is exclusive for Business Customers.", "CATALOG.DETAILS_PAGE.BUYBOX.MSRP.LABEL": "MSRP", "CATALOG.DETAILS_PAGE.BUYBOX.MSRP.LABEL.SAVE": "Save {price}", "CATALOG.DETAILS_PAGE.BUYBOX.NO_METRO_CARD_MSG.DESCRIPTION": "In the METRO-Marktplatz you can purchase with or without a card. Register as business or consumer to have an unique experience.", "CATALOG.DETAILS_PAGE.BUYBOX.NO_METRO_CARD_MSG.TITLE": "Not a METRO Card holder? Not a problem!", "CATALOG.DETAILS_PAGE.BUYBOX.OTHER_OFFERS_LABEL.BUTTON": "Other offers ({count})  {count, plural, =1 {} other{from}} {price}", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.BUTTON_LABEL": "Explore Similar Products", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.BUTTON_NOTIFY_LABEL": "Notify me!", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.EMAIL_LABEL": "E-mail", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.EMAIL_PLACEHOLDER": "Email address", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.MESSAGE": "We are working to have this product back in stock.", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.NOTIFY_MESSAGE": "Missed out on this item? Don’t worry, you can be notified by email when the item is back in stock.", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.NOTIFY_SUCCESS_MESSAGE": "We received your request! We will contact you as soon as the item is available again.", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.TITLE": "Currently unavailable", "CATALOG.DETAILS_PAGE.BUYBOX.OUT_OF_STOCK.VALIDATION": "Please enter a valid Email address", "CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.PRICE_PER_UNIT.PIECE": "piece", "CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.PRICE_PER_UNIT.ROLL": "roll", "CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.PRICE_PER_UNIT.SHEET": "sheet", "CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.VAT": "VAT incl. {price}", "CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.VAT_INCLUDED": "VAT incl.", "CATALOG.DETAILS_PAGE.BUYBOX.QUANTITY_PICKER.ADD.ARIA_LABEL": "Increase quantity", "CATALOG.DETAILS_PAGE.BUYBOX.QUANTITY_PICKER.ARIA_LABEL": "Quantity to add to cart", "CATALOG.DETAILS_PAGE.BUYBOX.QUANTITY_PICKER.SUBSTRACT.ARIA_LABEL": "Decrease quantity", "CATALOG.DETAILS_PAGE.BUYBOX.REGION.DELIVERY_TO": "to", "CATALOG.DETAILS_PAGE.BUYBOX.REGION.ES.BALEARIC": "Spain Balearic", "CATALOG.DETAILS_PAGE.BUYBOX.REGION.ES.MAINLAND": "Spain Mainland ", "CATALOG.DETAILS_PAGE.BUYBOX.REGION.NO_DELIVERY_IN_SELECTED_REGION": "This offer cannot be delivered to ", "CATALOG.DETAILS_PAGE.BUYBOX.REGISTER.TITLE": "Register", "CATALOG.DETAILS_PAGE.BUYBOX.SELLER": "<PERSON><PERSON>", "CATALOG.DETAILS_PAGE.BUYBOX.SELLER.MORE_INFO": "For imprint, Terms & Conditions, Cancellation policy and Data Policy please click on the <PERSON><PERSON>'s name", "CATALOG.DETAILS_PAGE.BUYBOX.SELLER.SOLD_BY": "Sold by", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_COST.LABEL": "Delivery per unit", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_COSTS": "Shipping Costs", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_INFO": "Final delivery costs will be calculated later based on your shipping address and order composition.", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_NOT_AVAILABLE": "Delivery not available", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_THRESHOLD.NEW_COMMUNICATION.B2B": "<b>Kostenlose Lieferung </b> wenn Sie mehr als {net} (inkl. Mwst: {gross}) für", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_THRESHOLD.NEW_COMMUNICATION.B2C": "<b>Kostenlose Lieferung  </b> wenn Sie mehr als {gross} inkl. Mwst für ", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_THRESHOLD.NEW_COMMUNICATION.FOOD_LABEL1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING_THRESHOLD.NEW_COMMUNICATION.FOOD_LABEL2": "ausgeben", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.CURB.DESCRIPTION_NEW": "By forwarding agents: Delivery in {min}-{max} working days to the curbside.", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.DELIVERY_DAYS.LABEL": ", delivered in {min}-{max} business days{ freightForwarding, select, false{, Default-delivery} other{} }", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.DELIVERY_DAYS.MODAL.LABEL": "delivered in {min}-{max} business days{ freightForwarding, select, false{, Default-delivery} other{} }", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.LABEL": "Shipping {price}", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.LABEL_SHIPPING": "Shipping", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.MORE_INFORMATION": "For more information, please", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.NEW.LAYOUT.DEFAULT.SHIPPING": "Free shipping", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.NEW.LAYOUT.TITLE": "Delivery", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.NONE.DESCRIPTION_NEW": "By forwarding agents: delivery in {min}-{max} working days. For more information", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.PREMISE.DESCRIPTION_NEW": "By forwarding agents: Delivery in {min}-{max} working days to the place of use. For more information", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.STANDARD.DESCRIPTION_NEW": "Standard shipping in {min}-{max} working days", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.TITLE": "Important information", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.TITLE.NEW": "Delivery: Free shipping.", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.TOOLTIP.FINAL_SHIPPING_COST_MAY_VARY": "Shipping costs may vary depending on your final order.", "CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.VAT_INCLUDED": "VAT incl.", "CATALOG.DETAILS_PAGE.BUYBOX.STOCK_AVAILABILITY.TITLE": "Only {stock} left in stock", "CATALOG.DETAILS_PAGE.BUYBOX.STRIKETHROUGH_PRICE": "Was", "CATALOG.DETAILS_PAGE.BUYBOX.SUBSIDIARY_ASSORTMENT.LINK_TEXT": "Your assortment page.", "CATALOG.DETAILS_PAGE.BUYBOX.SUBSIDIARY_ASSORTMENT.TEXT": "Your tailored assortment is available in the ", "CATALOG.DETAILS_PAGE.BUYBOX.SUBSIDIARY_ASSORTMENT.TITLE": "{keyAccountName} Account Holders: ", "CATALOG.DETAILS_PAGE.BUYBOX.TERMS_CONDITIONS.LABEL": "Terms and Conditions", "CATALOG.DETAILS_PAGE.BUYBOX.TOOLTIP.LABEL": "The best price offer from the sellers", "CATALOG.DETAILS_PAGE.BUYBOX.VAT_EXCLUDED.LABEL": "Excl. VAT", "CATALOG.DETAILS_PAGE.BUYBOX.VAT_INCLUDED.LABEL": "VAT incl.", "CATALOG.DETAILS_PAGE.BUYBOX.VOLUME_PRICE.TOOLTIP.B2B_LABEL": "Volume prices do not include VAT.", "CATALOG.DETAILS_PAGE.BUYBOX.VOLUME_PRICE.TOOLTIP.B2C_LABEL": "Volume prices include VAT.", "CATALOG.DETAILS_PAGE.BUYBOX.WEEE.FREE_DELIVERY": "Free", "CATALOG.DETAILS_PAGE.BUYBOX.WEEE.LABEL": "Return of old device", "CATALOG.DETAILS_PAGE.CALLBACK_CALENDER_INFO": "Arrange a callback with our experts", "CATALOG.DETAILS_PAGE.CALLBACK_CALENDER_TITLE": "Book a free consultation", "CATALOG.DETAILS_PAGE.CALLBACK_PHONE_INFO": "Advice & ordering for business customers", "CATALOG.DETAILS_PAGE.CALLBACK_TITLE": "Do you have anymore questions? We are happy to help", "CATALOG.DETAILS_PAGE.CATALOG_TOOLBAR.TITLE": "Product details", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.BEST_FOR": "Best for", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.CONS": "Cons", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.PROS": "Pros", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.BEST_FOR": "Fresh products, meat, pastries", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.CONS": "Not ideal for high-usage environments\nTemperature differences between top and bottom shelves may occur\nLonger pull-down time (takes longer to cool down contents)\nManual defrosting needed", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.DESCRIPTION_BOLD": "Static cooling relies on natural air circulation, creating cooler zones at the bottom. It consumes less energy and keeps humidity higher, making it a great choice for storing unpackaged, moisture-sensitive foods like vegetables, meat, or pastries.", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.DESCRIPTION_NORMAL": "Since there's no fan, energy consumption is lower and noise levels are minimal. However, the temperature inside the unit may vary between shelves—especially in upright coolers—and it may take longer to cool newly stocked items. Static cooling is best suited for storage units with infrequent door openings.", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.PROS": "Low noise emissions\nLow energy consumption\nHigher humidity, ideal for fresh products", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.VENTILATED.BEST_FOR": "Beverages, packaged products.", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.VENTILATED.CONS": "Higher noise levels due to fan operation\nIncreased energy consumption\nTypically higher upfront cost\nCan dry out unpackaged or uncovered food items", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.VENTILATED.DESCRIPTION_BOLD": "Ventilated cooling uses an internal fan and an evaporator, usually positioned at the top of the unit, to circulate cold air evenly throughout the cabinet. This helps maintain food safety by reducing temperature fluctuations.", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.VENTILATED.DESCRIPTION_NORMAL": "It's ideal for high-volume environments storing beverages or packaged goods. Keep in mind that the fan increases energy consumption and can cause drying—less suitable for unpackaged or delicate products. Also, expect more noise due to the active air circulation.", "CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.VENTILATED.PROS": "Fast pull-down time (cools contents quickly)\nEven temperature distribution throughout the unit\nIdeal for high-traffic environments\nAutomatic defrost function", "CATALOG.DETAILS_PAGE.COOLING.COMPRESSOR": "Compressor cooling", "CATALOG.DETAILS_PAGE.COOLING.STATIC": "Static cooling", "CATALOG.DETAILS_PAGE.COOLING.VENTILATED": "Ventilated cooling", "CATALOG.DETAILS_PAGE.COUNTDOWN.END_IN_FEW_DAYS": "Ends in {count} days", "CATALOG.DETAILS_PAGE.COUNTDOWN.END_IN_FEW_HOURS": "Ends in ", "CATALOG.DETAILS_PAGE.DESCRIPTION_SUBHEADER.TITLE": "Other details", "CATALOG.DETAILS_PAGE.DETAILS_CARD.DESCRIPTION.TITLE": "Description", "CATALOG.DETAILS_PAGE.DIMENSIONS": "Dimensions", "CATALOG.DETAILS_PAGE.DOCUMENTS.TITLE": "Documents and additional specifications", "CATALOG.DETAILS_PAGE.EEC.HEADER": "Energy efficiency class", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_COOLING.TITLE": "Cooling", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_DRYING.TITLE": "Drying", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_HEATING.TITLE": "Heating", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_WASHING_DRYING.DT.TITLE": "Washing Drying", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_WASHING_DRYING.TITLE": "Washing<br/>Drying", "CATALOG.DETAILS_PAGE.ENERGY_EFFICIENCY_WASHING.TITLE": "Washing", "CATALOG.DETAILS_PAGE.ERROR_NOTIFICATION.ADD_TO_CART.CONTENT": " and try one more time", "CATALOG.DETAILS_PAGE.ERROR_NOTIFICATION.ADD_TO_CART.LABEL": "Please ", "CATALOG.DETAILS_PAGE.ERROR_NOTIFICATION.ADD_TO_CART.LINK": "reload the page", "CATALOG.DETAILS_PAGE.ERROR_NOTIFICATION.ADD_TO_CART.TITLE": "Oops, something went wrong.", "CATALOG.DETAILS_PAGE.FREIGHT_FORWARDING_MODAL.BUTTON": "Ok, I Got It", "CATALOG.DETAILS_PAGE.FREIGHT_FORWARDING_MODAL.DESCRIPTION": "Please note that you may be contacted by the seller or a logistics company by phone, SMS or email to coordinate a delivery date. If you have any questions about the delivery, please contact the seller directly.", "CATALOG.DETAILS_PAGE.FREIGHT_FORWARDING_MODAL.TITLE": "Ordering Heavy Bulky items from a Marketplace-Seller:", "CATALOG.DETAILS_PAGE.G_RECOMMENDER.TITLE": "Wird oft zusammen gekauft", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.ENLARGE_VIEW.TEXT": "Click to open expanded view", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.MAIN_IMAGE.ZOOM.ARIA_LABEL": "Zoom image", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.POPUP.ZOOM_INFO.TEXT": "Pinch to zoom", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.PREVIEW.ARIA_LABEL": "Product picture {index}", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.ROLL_OVER.TEXT": "Roll over image to zoom in", "CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.ZOOM_RECTANGLE.TEXT": "Mouse over to zoom or click to enlarge", "CATALOG.DETAILS_PAGE.INSTALLATION_SERVICES_PER_UNIT": "per unit.", "CATALOG.DETAILS_PAGE.INSTALLATION_SERVICES_SUB_TITLE": "Includes delivery to the place of use of your product", "CATALOG.DETAILS_PAGE.INSTALLATION_SERVICES_TITLE": "Full on-site installation service", "CATALOG.DETAILS_PAGE.LOADBEE.LINK": "More information from", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.DIMENSION_WEIGHT.TITLE": "Dimensions & Weight", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.FEATURE_FUNCTION.TITLE": "Features & Functions", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.GUARANTEE_CONDITIONS_AGGREGATED.LABEL": "Guarantee", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.HAZARD_CLASS.TITLE": "Hazard classes", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.INSTRUCTION_MANUAL_AGGREGATED.LABEL": "Instruction manual", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.MATERIAL_COLOR.TITLE": "Material & Color", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.OTHER_INFO.TITLE": "Other info", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PACKAGE_DIMENSIONS.LABEL": "Package Dimensions (H x W x D)", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PERFORMANCE_CONSUMPTION.TITLE": "Performance & Consumption", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PRODUCT_BRAND.TITLE": "Product & Brand", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PRODUCT_DIMENSIONS.LABEL": "Product Dimensions (H x W x D)", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PRODUCT_ID.LABEL": "Product ID", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.SAFETY_INSTRUCTION.TITLE": "Safety instructions", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.TEMPERATURE_RANGE_AGGREGATED.VALUE": "from %temperature_min% to %temperature_max%", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.TEMPERATURE_RANGE.LABEL": "Temperature range", "CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.TEMPERATURE_RANGE.VALUE": "from {from} {unit} to {to} {unit}", "CATALOG.DETAILS_PAGE.MANUFACTURER.LABEL": "Manufacturer", "CATALOG.DETAILS_PAGE.MEDIA_MODAL.TITLE": "Images and media for this product", "CATALOG.DETAILS_PAGE.MINIMUM_QUANTITY.LABEL": "Minimum quantity: {quantity}", "CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL": "Further information about the product", "CATALOG.DETAILS_PAGE.OFFERS_BOX.ADD_TO_CART.PLACEHOLDER": "Add to Basket", "CATALOG.DETAILS_PAGE.OFFERS_BOX.ADD_TO_CART.TITLE": "To the Cart", "CATALOG.DETAILS_PAGE.OFFERS_BOX.ADDED_TO_CART": "{quantity} in the Cart", "CATALOG.DETAILS_PAGE.OFFERS_BOX.OTHER_OFFERS.PLACEHOLDER": "Other offers:", "CATALOG.DETAILS_PAGE.OFFERS_BOX.PACK_PRICE.LABEL": "Pack Price / {unitCount} units", "CATALOG.DETAILS_PAGE.OFFERS_BOX.PRICE.LABEL": "Price:", "CATALOG.DETAILS_PAGE.OFFERS_BOX.REMOVED_FROM_CART": "The product was removed from your Shopping cart", "CATALOG.DETAILS_PAGE.OFFERS_BOX.SELLER_NAME.LABEL": "Seller name:", "CATALOG.DETAILS_PAGE.OFFERS_BOX.TITLE": "Best offer", "CATALOG.DETAILS_PAGE.OFFERS_BOX.UNIT_PRICE.LABEL": "Unit Price", "CATALOG.DETAILS_PAGE.OFFERS_BOX.UPDATE_CART": "Updating...", "CATALOG.DETAILS_PAGE.OFFERS_LABEL.BUSINESS_CUSTOMERS": "For Business Customers", "CATALOG.DETAILS_PAGE.OFFERS_LABEL.CHEAPEST_OFFER": "Cheapest offer", "CATALOG.DETAILS_PAGE.OFFERS_LABEL.FAST_DELIVERY": "Fastest delivery", "CATALOG.DETAILS_PAGE.OFFERS_LABEL.VOLUME_PRICE": "Volume price", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_MODAL.LABEL": " Buy more, pay less", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_MODAL.TITLE": "Other offers ({count})", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.ACTIONS": "Compare all offers", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.BOX_ACTION": "See {count} other {count, plural, =1{offer} other{offers}} (from", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.BOX_ACTION_PLURAL": "See {count} other {count, plural, =1{offer} other{offers}} (from", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.BOX_ACTION_SINGULAR": "See {count} other {count, plural, =1{offer} other{offers}} (from", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.BOX_ACTION_VOLUME_PRICING_VARIANT_TWO": "See {count} more {count, plural, =1{offer} other{offers}}, also with volume discounts (from ", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.BOX_TITLE": "Other offers for this product:", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.CLOSE": "Close", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.DETAILS": "{count} other {count, plural, =1{offer} other{offers}} for this product:", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.DETAILS_PLURAL": "{count} other {count, plural, =1{offer} other{offers}} for this product:", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.DETAILS_SINGULAR": "{count} other {count, plural, =1{offer} other{offers}} for this product:", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.MOBILE_ADD_TO_CART": "Add to cart", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.RANKING_INFO": "Offers are sorted by price", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.RANKING_INFO_NEW": "How offers are sorted?", "CATALOG.DETAILS_PAGE.OTHER_OFFERS_V2.SHOW_DETAILS": "See Offer", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.ALERT.TITLE_OR": "or", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.RANKING_INFO": "Offers are sorted by price:", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING_TIME.LABEL": "{min}-{max} business days", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.CURB.SUBTEXT": "Your item will be dropped off at the curb, outside of your selected address.", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.CURB.TEXT": "By forwarding agents to the curbside. For more information", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.FORWARDING_AGENTS.TEXT": "By forwarding agents.", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.PREMISE.SUBTEXT": "Your item will be delivered directly to the specified location where it will be used.", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.PREMISE.TEXT": "By forwarding agents to the place of use. For more information", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHIPPING.STANDARD.TEXT": "Standard shipping", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SHOW_MORE.LABEL": "All {count} offers for this article", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SORTING_INFO_TOOLTIP_TEXT": "Offers are ranked according to the following criteria:\n  1. availability for private customers\n  2. additional services\n  3. product price\n  4. delivery costs", "CATALOG.DETAILS_PAGE.OTHER_OFFERS.SORTING_INFO_TOOLTIP_TEXT_NEW": "The offers are ranked taking into account the following parameters:\n  1. Availability for private customers\n  2. Additional services\n  3. Product price\n  4. Shipping costs\n  5. Percentage of on-time deliveries\n  6. Delivery speed and delivery window size\n  7. Number of customer complaints\n  8. Response time to customer inquiries\n  9. Number of seller-initiated order cancellations", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.CONTENT": " or continue shopping with available quantity", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL": "Please", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL_MORE_OFFER": "To see if more are available from another seller, check the other offers.", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL_PART_ONE": "This seller has only", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL_PART_ONE_MUL": "This seller has only", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL_PART_TWO": "of these available.", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LABEL_PART_TWO_MUL": "of these available.", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.LINK": "choose another offer", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.TITLE": "Unfortunately, the desired quantity is not available.", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.TOOL_TIP_LABEL": "{quantity, plural, =1{This seller has only {quantity} of these available.} other{This seller has only {quantity}  of these available.}}", "CATALOG.DETAILS_PAGE.OUT_OF_ORDER_NOTIFICATION.ADD_TO_CART.TOOL_TIP_LABEL_NEXT": "This seller has only {quantity} of these available.", "CATALOG.DETAILS_PAGE.OUT_OF_STOCK_MODAL.BUTTON.CANCEL": "Cancel", "CATALOG.DETAILS_PAGE.OUT_OF_STOCK_MODAL.CONTENT": "Sorry, this item is out of stock.", "CATALOG.DETAILS_PAGE.OUT_OF_STOCK_MODAL.TITLE": "Out of stock", "CATALOG.DETAILS_PAGE.PRODUCT_BLACK_DEAL_BANNER.BUTTON": "Discover now", "CATALOG.DETAILS_PAGE.PRODUCT_BLACK_DEAL_BANNER.TEXT1": "Exclusive online offer", "CATALOG.DETAILS_PAGE.PRODUCT_BLACK_DEAL_BANNER.TEXT2": "On Monday, November 21st,", "CATALOG.DETAILS_PAGE.PRODUCT_BLACK_DEAL_BANNER.TEXT3": "It's finally time -", "CATALOG.DETAILS_PAGE.PRODUCT_BLACK_DEAL_BANNER.TEXT4": "the METRO Black Week starts!", "CATALOG.DETAILS_PAGE.PRODUCT_CHARACTERISTICS": "Product characteristics", "CATALOG.DETAILS_PAGE.PRODUCT_ID.COPIED": "Copied!", "CATALOG.DETAILS_PAGE.PRODUCT_ID.LABEL": "Product ID", "CATALOG.DETAILS_PAGE.PRODUCT_VARIANTS.PACKAGE_SIZE": "Package Size", "CATALOG.DETAILS_PAGE.REBUY_GUARANTEE.LABEL": "<b>5 years</b> repurchase guarantee", "CATALOG.DETAILS_PAGE.RELATED_CATEGORIES.TITLE": "Related Categories", "CATALOG.DETAILS_PAGE.RELATED_PRODUCTS.TITLE": "Related products", "CATALOG.DETAILS_PAGE.SEE_ALL_CHARACTERISTICS": "See all characteristics", "CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.SEE_ALL": "See all", "CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.TITLE": "More products from the {series} series:", "CATALOG.DETAILS_PAGE.SHIPPING.CURB.DESCRIPTION": "Curbside delivery", "CATALOG.DETAILS_PAGE.SHIPPING.DELIVERY_TIME.ARRIVES_BY": "Arrives by", "CATALOG.DETAILS_PAGE.SHIPPING.DELIVERY_TIME.BUSINESS_DAYS": "{min} - {max} business days", "CATALOG.DETAILS_PAGE.SHIPPING.DELIVERY_TIME.IN": "In", "CATALOG.DETAILS_PAGE.SHIPPING.EXTRA_UNIT": "per extra unit", "CATALOG.DETAILS_PAGE.SHIPPING.NONE.DESCRIPTION": "Delivery by forwarding agents", "CATALOG.DETAILS_PAGE.SHIPPING.PARCEL.DESCRIPTION": "Parcel delivery", "CATALOG.DETAILS_PAGE.SHIPPING.PREMISE.DESCRIPTION": "Delivery to place of use", "CATALOG.DETAILS_PAGE.SHIPPING.RETURN_POLICY": "Returns Policy FAQ", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.BASE_SHIPPING_FEE": "Base shipping fee", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.CLOSE": "Close", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.DELIVERY_DESTINATION": "Delivery to {region}", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.DISCLAIMER": "Delivery costs may vary depending on your final order. If you order multiple products, they may be delivered in separate shipments.", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.EXTRA_UNIT_COST": "Extra unit cost", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.EXTRA_UNIT.TITLE": "Cost per each extra unit: +", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.FREE_DELIVERY_REQUIRMENT": "Free Delivery Requirements", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.FREIGHT.CONTENT": "The courier will notify you of the delivery date by telephone and email prior to delivery. Please provide your phone number when completing your order. Estimated delivery dates can be found for your products in the cart.", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.FREIGHT.TITLE": "Ordering bulky items from a marketplace seller:", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.HOW_DELIVERY_IS_CALCULATED": "How we calculate the delivery price?", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.THRESHOLD": "Buy {quantity} units: <b>free shipping</b>", "CATALOG.DETAILS_PAGE.SHIPPING.SIDEBAR.TITLE": "Delivery Information", "CATALOG.DETAILS_PAGE.SHIPPING.STANDARD.DESCRIPTION": "Standard delivery", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADD": "The product was added to {listName}", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADD_OLD": "Das Produkt wurde zu Ihrer Liste hinzugefügt.", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADD_TO_ANY_LIST_TITLE": "Add this product to your list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADDED_TO": "Added to the <a href=\"{url}\">List</a>", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADDED_TO_NEW_one": "Added to", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ADDED_TO_NEW_other": "Added to <a href=\"{url}\">{name}</a>", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.CREATE": "Create New List", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.DEFAULT_LIST": "Shopping list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.ERROR": "Oops, something went wrong.", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.LABEL": "Add to the List", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.MANAGE_ACTION": "Manage", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.MULTIPLE_LIST": "Multiple lists", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.PANEL.CREATE_BTN": "Create list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.PANEL.CREATE_LIST": "Create a new list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.PANEL.INPUT_DESCRIPTION": "Add a name for the list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.PANEL.TITLE": "Remove or add this product to another list", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.REMOVE": "The product was removed from {listName}", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.REMOVE_ACTION": "Remove", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.REMOVE_OLD": "The product is removed from your Shopping List", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.UPDATE": "{listCount} lists were updated", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.UPDATE_LIST": "Update", "CATALOG.DETAILS_PAGE.SHOPPING_LIST.VIEW": "View", "CATALOG.DETAILS_PAGE.SIMILAR_PRODUCTS.TITLE": "Similar Products", "CATALOG.DETAILS_PAGE.SPONSORED_PRODUCTS.TITLE": "Sponsored products", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.ADDED_TO_CART_one": "item added to the Cart", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.ADDED_TO_CART_other": "{quantity} items added to the Cart", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.CONTINUE_SHOPPING": "Continue Shopping", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.INSTALLATION_SERVICE": "Installation service", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.ITEMS_IN_CART": "{quantity} in the Cart", "CATALOG.DETAILS_PAGE.SUCCESS_MODAL.SEE_CART": "View Shopping Cart", "CATALOG.DETAILS_PAGE.TABLEWARE.DESIGNED_FOR_PROFESSIONALS": "Key features for professionals", "CATALOG.DETAILS_PAGE.TABLEWARE.DISHWASHER_SAFE": "Our professional products have passed certified dishwasher testing, ensuring durability after repeated cycles. They will not lose color even after 500 washes in a professional dishwasher.", "CATALOG.DETAILS_PAGE.TABLEWARE.MICROWAVE_SAFE": "With our microwave-safe products, you can confidently heat or reheat meals without concerns about overheating or damaging the items. Enjoy the convenience and versatility of using our products in microwave ovens without compromising on quality or safety.", "CATALOG.DETAILS_PAGE.TABLEWARE.OVENPROOF": "Our professional products are oven safe, undergoing rigorous testing for heat resistance up to 250°C for a minimum of 1 hour. This testing ensures that the ceramic products can withstand high temperatures without damage, offering versatility for various culinary applications. Whether you’re baking, broiling, or reheating dishes, our oven-safe products provide reliability and durability, making them a trusted choice for professional kitchens.", "CATALOG.DETAILS_PAGE.TABLEWARE.REBUY_GUARANTEE": "Our professional products come with a 5-year rebuy guarantee. This reflects our commitment to providing consistent supply, ensuring easy replenishment whenever needed, and eliminating worries about availability issues.", "CATALOG.DETAILS_PAGE.TABLEWARE.STACKABLE": "Stackable professional products are engineered for efficiency. Our products allow secure stacking of at least 10 items on top of one another without damage, saving space in busy environments.", "CATALOG.DETAILS_PAGE.TABLEWARE.THERMAL_SHOCK_RESISTANT": "Our professional products feature exceptional thermal shock resistance, crucial for commercial use. You can take these items from the freezer and put them in a hot oven, and they will not crack. Built for durability and reliability, our products ensure longevity in demanding environments.", "CATALOG.DETAILS_PAGE.TELESALES.BOOK_CALL": "Book a free consultation", "CATALOG.DETAILS_PAGE.TELESALES.CALL_US": "Call us", "CATALOG.DETAILS_PAGE.TELESALES.DESC": "Gain access to our dedicated team of B2B professionals, ready to support you—whether you’re selecting essential equipment or launching your venue from the ground up.", "CATALOG.DETAILS_PAGE.TELESALES.PERKS_1": "<b>10+ years of experience</b> in HoReCa consulting", "CATALOG.DETAILS_PAGE.TELESALES.PERKS_2": "<b>Personalized recommendations</b> tailored to your menu and setup", "CATALOG.DETAILS_PAGE.TELESALES.PERKS_3": "Our team speaks <b>6 languages</b>", "CATALOG.DETAILS_PAGE.TELESALES.TITLE": "Get Expert Advice for Your HoReCa Business", "CATALOG.DETAILS_PAGE.TOAST.REGION_SELECTOR_UPDATED": "Delivery information updated based on region selection", "CATALOG.DETAILS_PAGE.TOP_CART_LAYER.ADDED_TEXT": "Added to the shopping cart", "CATALOG.DETAILS_PAGE.TOP_CART_LAYER.EDIT_TEXT": "Go to shopping cart", "CATALOG.DETAILS_PAGE.USPS.ADJUSTABLE_SHELVES": "This product has adjustable shelves. You can rearrange them to fit trays, containers, or bottles of different sizes — so you can organize food efficiently and make full use of the space.", "CATALOG.DETAILS_PAGE.USPS.CONTROL_PANEL": "This product includes a control panel for adjusting temperature and other settings. It gives you precise control to match the equipment to your needs. ", "CATALOG.DETAILS_PAGE.USPS.CORROSION_RESISTANT": "This product is corrosion resistant. It is made from a high cast of chromium, which ensures long-lasting performance and rust resistance. It is designed to withstand frequent use in demanding HoReCa environments.", "CATALOG.DETAILS_PAGE.USPS.DOOR_OPEN_ALARM": "This product alerts you if the door is left open. It helps keep the temperature stable, protects food quality, and reduces energy waste — especially useful in busy kitchens.", "CATALOG.DETAILS_PAGE.USPS.EXTENDABLE": "This table is extendable. You can easily extend it without help from anyone else to accommodate additional guests, then return it to a smaller size to save space on your terrace for everyday use.", "CATALOG.DETAILS_PAGE.USPS.FOLDABLE": "This product is foldable. It can be easily deployed and collapsed, offering flexibility for your outdoor setups and seasonal activities, allowing you to move it effortlessly and save space.", "CATALOG.DETAILS_PAGE.USPS.FRIDGE_LIGHT": "This product has a built-in interior light. It helps you find what you need quickly, even in low-light kitchens or when you're in a rush.", "CATALOG.DETAILS_PAGE.USPS.GN_COMPATIBLE": "This product is GN compatible. It is designed to match European standards for kitchenware tray and container sizes. It ensures efficient storage, organization, and seamless integration in professional kitchens.", "CATALOG.DETAILS_PAGE.USPS.INDUCTION_COMPATIBLE": "This product is designed for induction stoves. It has a magnetic base that ensures quick, even, and energy-efficient heating. Since the product heats directly, it responds quickly to temperature adjustments, allowing for precise control over the heat level.", "CATALOG.DETAILS_PAGE.USPS.LEARN_MORE": "Learn more", "CATALOG.DETAILS_PAGE.USPS.LOCKABLE": "This product has a lockable door. It helps protect stored food from unwanted access — useful in shared kitchens, bar areas, or when storing high-value ingredients.", "CATALOG.DETAILS_PAGE.USPS.NON_STICKING": "This product has a non-stick Teflon coating. In addition to offering effortless cooking, it extends the product’s lifespan and makes it easy to clean, abrasion-resistant and scratch-resistant in intensive usage. Perfect for professional kitchens!", "CATALOG.DETAILS_PAGE.USPS.SANDWICH_BOTTOM": "The bottom of this product consists of three layers: steel-aluminum-steel, known as a sandwich bottom. The aluminum layer ensures faster heat distribution, while the stainless steel layer makes it compatible with induction stoves, providing efficient and versatile cooking.", "CATALOG.DETAILS_PAGE.USPS.TEMPERATURE_DISPLAY": "This product has a temperature display that shows the current internal temperature. It helps you quickly check if the device is staying at the right level — important for safe storage and consistent results.", "CATALOG.DETAILS_PAGE.USPS.TEMPERED_GLASS": "This product is crafted from tempered glass, which makes it resistant to temperature changes and more scratch-resistant than regular glass. If it breaks, it shatters into small, blunt pieces, reducing the risk of injury and making it a safer choice for busy HoReCa spaces.", "CATALOG.DETAILS_PAGE.USPS.UV_PROTECTION": "This product offers UV 50+ protection against harmful sun exposure. It maintains its integrity and color even under prolonged sunlight.", "CATALOG.DETAILS_PAGE.USPS.WATER_REPELLANT": "This umbrella is water repellant. It  features an advanced EcoElite Teflon polyurethane coating that forces water to bead up and roll off the surface. This technology keeps your guests and gear dry in wet conditions, ensuring moisture doesn’t penetrate the material.", "CATALOG.DETAILS_PAGE.USPS.WATER_RESISTANT": "This product is water-resistant, meaning it doesn’t get damaged by moisture. Whether facing rain or morning dew, you can leave it outdoors without worrying that its appearance or characteristics will be affected.", "CATALOG.DETAILS_PAGE.USPS.WEATHER_PROOF": "This product is weatherproof. It is capable of enduring various outdoor conditions. This item was tested to ensure it won’t get damaged by rain or wind, and it resists fading or losing color under heavy sunlight. Ideal for year-round use!", "CATALOG.DETAILS_PAGE.USPS.WITH_CALLIBRATION": "This product is calibrated. It is designed with exact measurement markings which are required by law for commercial use in many European countries. It ensures legal compliance for your business and transparency for your customers.", "CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS": "See available options", "CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS_DESKTOP": "See available<br/>options", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_SUBTOTAL.POPUP.PLACEHOLDER.LABEL": "Type your quantity", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.BUY.LABEL": "Quantity", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.PRICE.LABEL": "Price", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.SAVINGS.LABEL": "Savings", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL": "Buy {quantity} {quantity, plural, =1{} =2{} =3{} =4{} =5{+} other{+}}", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT": "<PERSON><PERSON> {quantity}", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL": "(1-4)[ ];(4-inf)[+];", "CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.SAVINGS.LABEL": "from -", "CATALOG.DETAILS_PAGE.VOLUME_PRICE.LABEL": "VOLUME PRICE", "CATALOG.DETAILS_PAGE.VOLUME_PRICEBOX.ITEMS_COUNT.LABEL": " - if {quantity} {quantity, plural, =1{} =2{} =3{} =4{} =5{+} other{+}}", "CATALOG.DETAILS_PAGE.VOLUME_PRICEBOX.SAVE.LABEL": "Save {price}", "CATALOG.DETAILS_PAGE.VOLUME_PRICEBOX.SUBTOTAL.LABEL": "Offer subtotal:", "CATALOG.DETAILS_PAGE.VOLUME_PRICEBOX.TITLE": "Buy more pay less", "CATALOG.FILTERS.REBUY_GUARANTEE.DISCLAIMER": "5-year rebuy guarantee ensures uninterrupted restocking.", "CATALOG.PAGINATION.ARIA.NEXT_PAGE": "Next page", "CATALOG.PAGINATION.ARIA.PAGE": "Page", "CATALOG.PAGINATION.ARIA.PAGINATION": "Pagination", "CATALOG.PAGINATION.ARIA.PREVIOUS_PAGE": "Previous page", "CATALOG.VAT_DISCLAIMER": "The final VAT will be detailed on the review order page. ", "CATALOG.VAT_DISCLAIMER_TOOLTIP": "Final price incl. VAT may differ from the displayed price. Applicable VAT will be calculated based on EU regulations, buyer status (private or business), shipping address, and seller’s country.", "CHECKOUT.ACCEPT_TERMS.VALIDATION": "You should accept our terms and conditions", "CHECKOUT.ACCOUNTING.VALIDATOR.ACCOUNT_HOLDER_REQUIRED": "  A bank account holder is required.", "CHECKOUT.ACCOUNTING.VALIDATOR.DETAILS_NAME_REQUIRED": " A company name is required. Please check your account information.", "CHECKOUT.ACCOUNTING.VALIDATOR.IBAN_REQUIRED": " An iban is required. A valid iban for example is **********************. Please check your bank account information.", "CHECKOUT.ACCOUNTING.VALIDATOR.VAT_NUMBER_REQUIRED": "  A valid vat number is required. A valid vat number for example DE123456789. Please check your account information.", "CHECKOUT.ADDRESS_CREATE.BACK_BILLING_LINK": "Return to Billing Address", "CHECKOUT.ADDRESS_CREATE.BACK_SHIPPING_LINK": "Return to Shipping Address", "CHECKOUT.ADDRESS_CREATE.CREATE_BTN": "Save", "CHECKOUT.ADDRESS_CREATE.HEADER": "Enter a New Address", "CHECKOUT.ADDRESS_EDIT_BILLING.HEADER": "Update this Billing Address", "CHECKOUT.ADDRESS_EDIT_SHIPPING.HEADER": "Update this Shipping Address", "CHECKOUT.ADDRESS_ES_MARKET_POSTAL_CODE_INFORMATION": "Currently shipping is allowed only within Mainland Spain.", "CHECKOUT.ADDRESS_ITEM.BILLING.BUTTON": "Use this address", "CHECKOUT.ADDRESS_ITEM.BILLING.LABEL": "Billing address option", "CHECKOUT.ADDRESS_ITEM.BILLING.SELECTED_BUTTON": "Selected for billing", "CHECKOUT.ADDRESS_ITEM.SHIPPING.BUTTON": "Deliver to this address", "CHECKOUT.ADDRESS_ITEM.SHIPPING.LABEL": "Shipping address option", "CHECKOUT.ADDRESS_ITEM.SHIPPING.SELECTED_BUTTON": "Selected for shipping", "CHECKOUT.ADDRESS_LIST.BACK_LINK": "Return to Order Details", "CHECKOUT.ADDRESS_LIST.BACK_TEXT": "Return to Addresses", "CHECKOUT.ADDRESS_LIST.BILLING.TITLE": "Select a Billing Address", "CHECKOUT.ADDRESS_LIST.CREATE_LINK": "Add New Address", "CHECKOUT.ADDRESS_LIST.SHIPPING.TITLE": "Select a Shipping Address", "CHECKOUT.ADDRESS_VALIDATION.ACTIONS.CHANGE_ADDRESS": "Change delivery address", "CHECKOUT.ADDRESS_VALIDATION.ACTIONS.CHANGE_ORDER": "Back to cart", "CHECKOUT.ADDRESS_VALIDATION.ES_IB": "Balearic Islands", "CHECKOUT.ADDRESS_VALIDATION.MESSAGE": "One or more of the products in your cart cannot be delivered to the selected address. Please return to your cart or change the delivery address to proceed.", "CHECKOUT.ADDRESS_VALIDATION.TITLE": "Some products are unavailable in:", "CHECKOUT.ADDRESS-FORM.TEXT.ITALY_PROVINCES_PLACEHOLDER": "Type and select your province...", "CHECKOUT.ADDRESS.AS_DEFAULT_BILLING": "Use as default billing address", "CHECKOUT.ADDRESS.AS_DEFAULT_SHIPPING": "Use as default shipping address", "CHECKOUT.ADDRESS.BILLING_AS_SHIPPING": "Same as shipping address", "CHECKOUT.ADDRESS.BILLING.BUTTON.USE_SHIPPING_AS_BILLING": "Use shipping address as billing address", "CHECKOUT.ADDRESS.BILLING.TITLE": "Billing address", "CHECKOUT.ADDRESS.BUTTON.CANCEL": "Cancel", "CHECKOUT.ADDRESS.BUTTON.SAVE": "Save to \"My account\"", "CHECKOUT.ADDRESS.CHANGE": "Change", "CHECKOUT.ADDRESS.INVALID_POSTAL_CODE_DE_RESTRICTED.ALERT.ERROR.MESSAGE": "Invalid shipping address. Your saved address is in restricted areas of Germany and currently we don't ship there. Please change or add a new shipping address.", "CHECKOUT.ADDRESS.INVALID_POSTAL_CODE_DE_RESTRICTED.ALERT.ERROR.TITLE": "Invalid address for shipping.", "CHECKOUT.ADDRESS.INVALID_POSTAL_CODE_ES_NOT_MAINLAND.ALERT.ERROR.MESSAGE": "The address you entered is outside of Spain mainland, currently we only allow addresses within Spain mainland for shipping. Please change or add a new address for shipping.", "CHECKOUT.ADDRESS.INVALID_POSTAL_CODE_ES_NOT_MAINLAND.ALERT.ERROR.TITLE": "Invalid address for shipping.", "CHECKOUT.ADDRESS.INVALID_POSTAL_CODE_IT_RESTRICTED.ALERT.ERROR.MESSAGE": "The address you have entered is inside an area where we currently don't deliver. Please add a new shipping address.", "CHECKOUT.ADDRESS.LINE_2.LABEL": "Address Line 2 (optional)", "CHECKOUT.ADDRESS.LINE.LABEL": "Street, house number", "CHECKOUT.ADDRESS.NEED_AN_INVOICE.MESSAGE": "I need an invoice for my order", "CHECKOUT.ADDRESS.NOTIFICATIONS.SAVE_SUCCESS": "Address successfully saved", "CHECKOUT.ADDRESS.PHONE_NUMBER.DESCRIPTION": "Please add your phone number in case we need to contact you.", "CHECKOUT.ADDRESS.PHONE_NUMBER.INPUT": "Telephone Number", "CHECKOUT.ADDRESS.PHONE_NUMBER.SAVE.DESCRIPTION": "Save this number to my METRO account for future orders.", "CHECKOUT.ADDRESS.PHONE_NUMBER.TITLE": "Phone Number", "CHECKOUT.ADDRESS.PROCESSING-PAYMENT": "Your payment is being processed. Please, await...", "CHECKOUT.ADDRESS.REMEMBER_SAVE": "Save Address", "CHECKOUT.ADDRESS.RESTRICTED_POSTAL_CODE.ALERT.ERROR.TITLE": "Invalid address for shipping.", "CHECKOUT.ADDRESS.RESTRICTED_POSTAL_CODE.INPUT.ERROR.MESSAGE": "The address you have entered is in an area where we currently don’t deliver.", "CHECKOUT.ADDRESS.SELECTOR.DESCRIPTION": "Select address from the list", "CHECKOUT.ADDRESS.SELECTOR.TITLE": "Address book", "CHECKOUT.ADDRESS.SHIPPING.SAVE.SUCCESS.MESSAGE": "Shipping address successfully saved", "CHECKOUT.ADDRESS.SHIPPING.TITLE": "Shipping address", "CHECKOUT.ADDRESS.STEP.SAVE": "Continue", "CHECKOUT.ADDRESS.STEP.SAVE_AND_CONTINUE": "Save & Continue", "CHECKOUT.ADDRESS.TAX_NUMBER.LABEL": "Tax number:", "CHECKOUT.ADDRESS.TAX_NUMBER.MANDATORY.MESSAGE": "Fiscal number *", "CHECKOUT.ADDRESS.VALIDATION.ERROR_TOO_LONG": "The text is too long", "CHECKOUT.ADDRESS.VAT_ID.LABEL": "VAT ID", "CHECKOUT.ADDRESS.VAT_SECTION.DESCRIPTION": "Please add your VAT ID to qualify for 0% VAT.", "CHECKOUT.ADDRESS.VAT_SECTION.INVALID_VAT.ERROR_MESSAGE": "Tax is included due to invalid VAT ID", "CHECKOUT.ADDRESS.VAT_SECTION.LABEL": "VAT ID", "CHECKOUT.ADDRESS.VAT_SECTION.TITLE": "Company VAT ID", "CHECKOUT.ADDRESS.VAT_VALIDATION_DIALOG.BODY": "The order contains products that will be shipped from other EU countries than Germany. Your VAT ID does not fulfil the requirements for 0% VAT shipments for these products as it is invalid.", "CHECKOUT.ADDRESS.VAT_VALIDATION_DIALOG.BUTTON.CHANGE_VAT": "Change VAT ID", "CHECKOUT.ADDRESS.VAT_VALIDATION_DIALOG.BUTTON.PROCEED_WITH_VAT": "Proceed with tax included", "CHECKOUT.ADDRESS.VAT_VALIDATION_DIALOG.HEADING": "VAT ID does not meet 0% VAT requirements", "CHECKOUT.ADDRESS.VAT_VALIDATION_DIALOG.NON_X_BORDER.BODY": "Your VAT ID does not qualify for 0% VAT orders. Please correct your VAT ID or continue with tax included.", "CHECKOUT.ADDRESSES.VALIDATOR.COUNTRY_REQUIRED": " Field 'country' is required", "CHECKOUT.APP.CHECKOUT.ACCOUNTING.BANK_ACCOUNT_DETAILS_ACCOUNT_HOLDER.REQUIRED": "A bank account holder is required.", "CHECKOUT.APP.CHECKOUT.ACCOUNTING.BANK_ACCOUNT_DETAILS_IBAN.REQUIRED": "An iban is required. A valid iban for example is **********************. Please check your bank account information.", "CHECKOUT.APP.CHECKOUT.ACCOUNTING.COMPANY_DETAILS_NAME.REQUIRED": "A company name is required. Please check your account information.", "CHECKOUT.APP.CHECKOUT.ACCOUNTING.COMPANY_DETAILS_VAT_NUMBER.REQUIRED": "A valid vat number is required. A valid vat number for example DE123456789. Please check your account information.", "CHECKOUT.APP.CHECKOUT.CART.IS_EMPTY": "Cart is empty", "CHECKOUT.APP.CHECKOUT.CART.OFFER_DOES_NOT_EXIST_IN_CART": "Offer does not exist in the cart.", "CHECKOUT.APP.CHECKOUT.CHECKOUT.NO_INVENTORY_ERROR": "There is no Inventory linked to the Offer", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_CANT_BE_ADDED_TO_CART": "Offer can’t be added to the cart", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_INACTIVE": "Not available anymore", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_NOT_AVAILABLE_IN_GERMANY": "Unfortunately, this offer is not available in Germany.", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_NOT_AVAILABLE_IN_SPAIN": "Unfortunately, this offer is not available in Spain.", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_NOT_ENOUGH_QUANTITY": "Not enough inventory in stock", "CHECKOUT.APP.CHECKOUT.CHECKOUT.OFFER_NOT_EXISTS": "Offer not exists", "CHECKOUT.APP.CHECKOUT.CHECKOUT.ORDER_CREATION.VALIDATION_ERROR": "Order creation failed due to invalid data", "CHECKOUT.APP.CHECKOUT.CHECKOUT.ORDER_MANAGEMENT.FAILED_DEPENDENCY": "Sorry! We are experiencing technical difficulties.", "CHECKOUT.APP.CHECKOUT.CHECKOUT.PRODUCT_IS_OUT_OF_STOCK": "Offer is out of stock", "CHECKOUT.APP.CHECKOUT.CHECKOUT.PRODUCT_PRICE_HAS_BEEN_CHANGED": "Price for product %name% has been changed. Please check.", "CHECKOUT.APP.CHECKOUT.CHECKOUT.VALIDATION.NOT_VALID": "Not valid", "CHECKOUT.APP.CHECKOUT.COMMON.COUNTRY_NOT_FOUND_ERROR": "Country not found", "CHECKOUT.APP.CHECKOUT.COMMON.PHONE.REQUIRED": "A valid phone number is required.", "CHECKOUT.APP.CHECKOUT.COMMON.PHONE.VALIDATION": "A valid phone number is required. A valid phone number should  between 6 and 15 digits.", "CHECKOUT.APP.CHECKOUT.COMMON.SHIPPING_COUNTRY_ONLY_GERMANY": "We're currently shipping only to Germany", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.ADDRESS_LINE_2_MAX_LENGTH": "Max length for field 'addressLine2' is { limit }", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.CHARACTERS_SHOULD_HAVE": "This value should have exactly { limit } characters.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.CITY_IS_REQUIRED": "Field 'city' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.CITY_MAX_LENGTH": "Max length for field 'city' is { limit }", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.COUNTRY_IS_REQUIRED": "Field 'country' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.FIELD_IS_REQUIRED": "The field can not be empty", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.FIRST_NAME_IS_REQUIRED": "Field 'firstName' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.FIRST_NAME_MAX_LENGTH": "Max length for field 'firstName' is { limit } characters", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.HOUSE_NUMBER_IS_REQUIRED": "Field 'House Number' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.LAST_NAME_IS_REQUIRED": "Field 'lastName' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.LAST_NAME_MAX_LENGTH": "Max length for field 'lastName' is { limit } characters", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.LATIN_WITH_SLASH": "Only letters, digits, symbols . , - ' / and spaces are available here.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.OFFER_ID_IS_REQUIRED": "The Offer ID is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.OFFER_ID_IS_UUID": "Offer ID should have UUID format", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.ONLY_DIGITS_ALLOWED": "Only digits are allowed.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.POSTAL_CODE_IS_REQUIRED": "Field 'postal code' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.QUANTITY_GREATER_THAN_ZERO": "The Quantity parameter should be greater than 0.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.QUANTITY_IS_REQUIRED": "The Quantity parameter is required", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.QUANTITY_TYPE": "The value { value } is not a valid type for quantity.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.STATEORPROVINCE_FIX_LENGTH": "State or province must be 2 characters.", "CHECKOUT.APP.CHECKOUT.COMMON.VALIDATION.STREET_IS_REQUIRED": "Field 'street' is required", "CHECKOUT.APP.CHECKOUT.COMMON.VAT_NUMBER.VALIDATION": "A valid vat number is required. A valid vat number for example DE123456789. Please check your account information.", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.ADDRESS_LINE_2_MAX_LENGTH": "Max length for field 'addressLine2' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.ADDRESS_LINE_IS_REQUIRED": "Field 'addressLine' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.ADDRESS_LINE_MAX_LENGTH": "Max length for field 'addressLine' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.CITY_IS_REQUIRED": "Field 'city' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.CITY_MAX_LENGTH": "Max length for field 'city' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.COUNTRY_IS_REQUIRED": "Field 'country' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.COUNTRY_REQUIRED_LENGTH": "Length for field 'country' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.DEFAULT_BILLING_IS_BOOLEAN": "Field 'defaultBilling' should be boolean", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.DEFAULT_SHIPPING_IS_BOOLEAN": "Field 'defaultShipping' should be boolean", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.FIRST_NAME_IS_REQUIRED": "Field 'firstName' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.FIRST_NAME_MAX_LENGTH": "Max length for field 'firstName' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.LAST_NAME_IS_REQUIRED": "Field 'lastName' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.LAST_NAME_MAX_LENGTH": "Max length for field 'lastName' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.LATIN_CHARACTERS_ERROR": "Only latin, ' and spaces available here", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.MAX_LENGTH_ERROR": "This value is too long. It should have { limit } character or less.", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.MIN_LENGTH_ERROR": "This value is too short. It should have { limit } character or more.", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.ONLY_DIGITS_ALLOWED": "Only digits are allowed.", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.POSTAL_CODE_IS_REQUIRED": "Field 'postal code' is required", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.POSTAL_CODE_MAX_LENGTH": "Length for field 'postal code' is { limit }", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATION.UPPER_FIRST_NAME_FIRST_LETTER_ERROR": "First name should starts from the capital letter", "CHECKOUT.APP.CHECKOUT.CUSTOMER.VALIDATIONS.EMAIL_ALREADY_IN_USE": "This email already in use.", "CHECKOUT.APP.CHECKOUT.ORDER_LINES.NOT_FOUND": "Order lines not found", "CHECKOUT.APP.CHECKOUT.ORDER.INCORRECT_STATUS": "Order has incorrect status", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE": "Invoice", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_DATE": "Date:", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_NUM": "Invoice #:", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_PRICE": "Price", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_PRODUCT": "Product", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_QTY": "Quantity", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_SELLER_NAME": "Seller name", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_SHIPPING_COST": "Shipping Cost:", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_SUB_TOTAL": "Sub Total:", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_SUM": "Sum", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_TAXES": "Taxes", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_TO": "To:", "CHECKOUT.APP.CHECKOUT.ORDER.INVOICE_TOTAL": "Total:", "CHECKOUT.APP.CHECKOUT.ORDER.ORDER_NOT_EXISTS": "Order does not exist", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_ERROR": "Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_IS_REQUIRED": "Address Line is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_MAX_LENGTH": "Max length for field 'Address Line' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.BILLING_ADDRESS_IS_REQUIRED": "Billing address is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.CITY_FORMAT_ERROR": "Only letters, symbols . , - ' / and spaces are available here", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.CITY_IS_REQUIRED": "Field 'city' is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.CITY_MAX_LENGTH": "Max length for field 'city' is { limit }", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_INVALID": "Country format is invalid.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_IS_REQUIRED": "Field 'country' is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_NOT_FOUND_ERROR": "Country not found", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_ONLY_GERMANY": "We're currently shipping only to Germany", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_ONLY_ITALY": "We're currently shipping only to Italy", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_ONLY_SPAIN": "We're currently shipping only to Spain", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.COUNTRY_REQUIRED_LENGTH": "Length for field 'country' is { limit }", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.DEFAULT_IS_BOOLEAN": "Field 'isDefault' should be boolean", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.DEVICE_FINGERPRINT_IS_REQUIRED": "Device Fingerprint is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FIRST_NAME_IS_REQUIRED": "First Name is required.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FIRST_NAME_MAX_LENGTH": "Max length for field 'First Name' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FIRST_NAME_MIN_LENGTH": "Min length for field 'First Name' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_INCORRECT_LENGTH": "Not valid, phone number should be between 6 and 15 digits", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_IS_REQUIRED": "Field 'freightForwardPhone' is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_MAX_LENGTH": "Max length for field 'freightForwardPhone' is { limit }", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_TYPE": "Field 'freightForwardPhone' should be number", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.HOUSE_NUMBER_MAX_LENGTH": "Max length for field 'House number' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.INVALID_ITALY_TAXNUMBER": "The tax identification number should be 16 characters long", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.INVALID_PAYMENT_METHOD": "Payment Method is invalid.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.LAST_NAME_IS_REQUIRED": "Last Name is required.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.LAST_NAME_MAX_LENGTH": "Max length for field 'Last Name' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.LAST_NAME_MIN_LENGTH": "Min length for field 'Last Name' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.LATIN_ERROR": "Only letters, symbols . , - ' and spaces are available here", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.LATIN_WITH_DIGITS_ERROR": "Only letters, digits, symbols . , - ' and spaces are available here", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.NOT_VALID": "Not valid", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID": "Postal code format is invalid.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_IS_REQUIRED": "Field 'postal code' is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_MAX_LENGTH": "Max length for field 'postal code' is { limit }", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.SHIPPING_ADDRESS_IS_REQUIRED": "Shipping address is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.STATUS_IS_REQUIRED": "Status is required", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.STREET_MAX_LENGTH": "Max length for field 'Street' is { limit }.", "CHECKOUT.APP.CHECKOUT.ORDER.VALIDATION.VALID_STATUS": "Choose a valid status", "CHECKOUT.BUTTON.MESSAGE.SUBSIDIARIES_REQUEST": "Request Approval", "CHECKOUT.BUTTON.PROCEED": "Place Order", "CHECKOUT.BUTTON.PROCEED.FRANCE": "Order and pay", "CHECKOUT.BUTTON.SUBMIT_REQUEST": "Submit Request", "CHECKOUT.CANCELLATION_POLICY_B2B_LINK": "revocation instruction", "CHECKOUT.CANCELLATION_POLICY_B2C_LINK": "cancellation policy", "CHECKOUT.CART.EDIT_LINK": "Edit", "CHECKOUT.CART.ERROR.IS_EMPTY": " Cart is empty", "CHECKOUT.CART.ERROR.OFFER_DOES_NOT_EXIST": " Offer does not exist in the cart.", "CHECKOUT.CART.ITEMS_COUNT": "{count} {count, plural, one{item} other{items}}", "CHECKOUT.CART.PRICE_PER_UNIT": "{count} {count, plural, one{pc} other{pcs}}", "CHECKOUT.CART.PRODUCT_ID": "Product ID", "CHECKOUT.CART.TITLE": "In your cart", "CHECKOUT.CART.UA.DISCOUNT_CODE.ERROR.MESSAGE": "The products selected are not valid for this campaign. Please, review your order", "CHECKOUT.CART.UA.DISCOUNT_CODE.SUCCESS.MESSAGE": "These products are donated and shipped directly to the Tafel Deutschland e.V. organization.", "CHECKOUT.CHECKOUT.ERROR.NO_INVENTORY": " There is no Inventory linked to the Offer", "CHECKOUT.CHECKOUT.VALIDATOR.NOT_VALID": " Not valid", "CHECKOUT.COMMON.ERROR.COUNTRY_NOT_FOUND": " Country not found", "CHECKOUT.COMMON.ERROR.PHONE_REQUIRED": " A valid phone number is required.", "CHECKOUT.COMMON.ERROR.PHONE_VALIDATION": " A valid phone number is required. A valid phone number should  between 6 and 15 digits.", "CHECKOUT.COMMON.ERROR.SHIPPING_COUNTRY_ONLY_GERMAN": " We're currently shipping only to Germany", "CHECKOUT.COMMON.ERROR.SHIPPING_COUNTRY_ONLY_SPAIN": " We're currently shipping only to Spain", "CHECKOUT.COMMON.ERROR.VAT_NUMBER_VALIDATION": " A valid vat number is required. A valid vat number for example DE123456789. Please check your account information.", "CHECKOUT.COMMON.VALIDATION.ADDRESS_LINE_2_MAX_LENGHT": " Max length for field 'addressLine2' is {limit}", "CHECKOUT.COMMON.VALIDATION.ADDRESS_LINE_FORMAT_ERROR": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.CHARACTERS_SHOULD_HAVE": " This value should have exactly {limit} characters.", "CHECKOUT.COMMON.VALIDATION.CITY_FORMAT_ERROR": "Only letters, symbols . - ' / and spaces can be used here", "CHECKOUT.COMMON.VALIDATION.CITY_MAX_LENGTH": " Max length for field 'city' is {limit}", "CHECKOUT.COMMON.VALIDATION.CITY_REQUIRED": "Field 'city' is required", "CHECKOUT.COMMON.VALIDATION.COUNTRY_REQUIRED": " Field 'country' is required", "CHECKOUT.COMMON.VALIDATION.FIELD_REQUIRED": " The field can not be empty", "CHECKOUT.COMMON.VALIDATION.FIRST_MAX_LENGTH": " Max length for field 'firstName' is {limit} characters", "CHECKOUT.COMMON.VALIDATION.FIRST_NAME_FORMAT": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.FIRST_NAME_REQUIRED": " Field 'firstName' is required", "CHECKOUT.COMMON.VALIDATION.HOUSE_NUMBER_FORMAT": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.HOUSE_NUMBER_REQUIRED": " Field House Number is required", "CHECKOUT.COMMON.VALIDATION.LAST_NAME_FORMAT": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.LAST_NAME_MAX_LENGTH": " Max length for field 'lastName' is {limit} characters", "CHECKOUT.COMMON.VALIDATION.LAST_NAME_REQUIRED": " Field 'lastName' is required", "CHECKOUT.COMMON.VALIDATION.LATIN_WITH_SLASH": " Only letters, digits, symbols . , -  / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.MAX_LENGTH": " Max length is {limit}", "CHECKOUT.COMMON.VALIDATION.OFFER_ID_REQUIRED": " The Offer ID is required", "CHECKOUT.COMMON.VALIDATION.OFFER_ID_UUID": " Offer ID should have UUID format", "CHECKOUT.COMMON.VALIDATION.ONLY_DIGITS_ALLOWED": " Only digits are allowed.", "CHECKOUT.COMMON.VALIDATION.POSTAL_CODE_REQUIRED": " Field 'postal code' is required", "CHECKOUT.COMMON.VALIDATION.QUANTITY_GREATER_THAN_ZERO": " The Quantity parameter should be greater than 0.", "CHECKOUT.COMMON.VALIDATION.QUANTITY_REQUIRED": " The Quantity parameter is required", "CHECKOUT.COMMON.VALIDATION.QUANTITY_TYPE": " The value {value} is not a valid type for quantity.", "CHECKOUT.COMMON.VALIDATION.STATE_OR_PROVINCE_FIX_LENGTH": " State or province must be 2 characters.", "CHECKOUT.COMMON.VALIDATION.STREET_FORMAT_ERROR": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.COMMON.VALIDATION.STREET_REQUIRED": " Field street is required", "CHECKOUT.COMMON.VALIDATION.VALID_STRING": " Only letters, digits, symbols . , - & / and spaces are available here", "CHECKOUT.COSTUMER.CONSENT.COMBINED_PHONE_EMAIL.TEXT.MAIN": "I would like to be informed about (personalised) offers, discounts, and trends from the METRO group by email and via my stored phone number and receive a 10% voucher for my next purchase.", "CHECKOUT.COSTUMER.CONSENT.EMAILS.SUBTEXT.VOUCHER": "Unsubscription is possible at any time.", "CHECKOUT.COSTUMER.CONSENT.EMAILS.TEXT": "I would like to receive (personalized) offers, discounts, and trends from METRO Marketplace via email.\nUnsubscription possible at any time.", "CHECKOUT.COSTUMER.CONSENT.EMAILS.TEXT.VOUCHER": "I would like to receive (personalised) offers, discounts and trends from METRO Group via email and receive a {htmlTagStart}10 % voucher{htmlTagEnd} for my next purchase.", "CHECKOUT.COSTUMER.CONSENT.EMAILS.TEXT.VOUCHER.FRANCE": "I would like to be informed by METRO Markets GmbH and other companies of METRO Group about (personalized) offers, discounts and trends via email and receive a {htmlTagStart}10% promo code{htmlTagEnd} for my next purchase.\nYou can unsubscribe at any time free of charge. Information on the use of your data in <a href='https://www.metro.fr/traitement-donnees-personnelles/marketplace' target='_blank'>METRO Markets Privacy Policy</a>. <a href='https://www.metro.fr/cgv_marketplace/codespromo' target='_blank'>Conditions</a> apply to the promo code.", "CHECKOUT.COSTUMER.CONSENT.EMAILS.TEXT.VOUCHER.SPECIAL.NETHERLANDS": "I would like to receive (personalised) offers, discounts and trends from METRO Group via e-mail.", "CHECKOUT.COSTUMER.CONSENT.PHONE.TEXT.MAIN": "As a professional customer I would like to receive expert advice, product consultation and customised offers from METRO’s gastronomy specialists via my stored phone number.*", "CHECKOUT.COSTUMER.CONSENT.PHONE.TEXT.SUBTEXT": "You can unsubscribe at any time", "CHECKOUT.CREATE_ADDRESS.USER_NOT_ALLOWED.ALERT.DESCRIPTION": "Only admin of the selected business can add new address for this business, please ask admin to add the new address in the business addressbook.", "CHECKOUT.CREATE_ADDRESS.USER_NOT_ALLOWED.ALERT.TITLE": "You’re not allowed add a new address", "CHECKOUT.CUSTOMER.VALIDATOR.ADDRESS_LINE_2_MAX_LENGTH": " Max length for field 'addressLine2' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.ADDRESS_LINE_MAX_LENGTH": " Max length for field 'addressLine' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.ADDRESS_LINE_REQUIRED": " Field 'addressLine' is required", "CHECKOUT.CUSTOMER.VALIDATOR.CITY_MAX_LENGTH": " Max length for field 'city' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.CITY_REQUIRED": " Field 'city' is required", "CHECKOUT.CUSTOMER.VALIDATOR.COUNTRY_REQUIRED": " Field 'country' is required", "CHECKOUT.CUSTOMER.VALIDATOR.COUNTRY_REQUIRED_LENGTH": " Length for field 'country' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.DEFAULT_BILLING_IS_BOOLEAN": " Field 'defaultBilling' should be boolean", "CHECKOUT.CUSTOMER.VALIDATOR.DEFAULT_SHIPPING_IS_BOOLEAN": " Field 'defaultShipping' should be boolean", "CHECKOUT.CUSTOMER.VALIDATOR.EMAIL_ALREADY_IN_USE": " This email already in use.", "CHECKOUT.CUSTOMER.VALIDATOR.FIRST_NAME_MAX_LENGTH": " Max length for field 'firstName' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.FIRST_NAME_REQUIRED": " Field 'firstName' is required", "CHECKOUT.CUSTOMER.VALIDATOR.LAST_NAME_MAX_LENGTH": " Max length for field 'lastName' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.LAST_NAME_REQUIRED": " Field 'lastName' is required", "CHECKOUT.CUSTOMER.VALIDATOR.LATIN_CHARACTERS_ERROR": " Only latin, ' and spaces available here", "CHECKOUT.CUSTOMER.VALIDATOR.MAX_LENGTH_ERROR": " This value is too long. It should have {limit} character or less.", "CHECKOUT.CUSTOMER.VALIDATOR.MIN_LENGTH_ERROR": " This value is too short. It should have {limit} character or more.", "CHECKOUT.CUSTOMER.VALIDATOR.ONLY_DIGITS_ALLOWED": " Only digits are allowed.", "CHECKOUT.CUSTOMER.VALIDATOR.POSTAL_CODE_LENGTH": " Length for field 'postal code' is {limit}", "CHECKOUT.CUSTOMER.VALIDATOR.POSTAL_CODE_REQUIRED": " Field 'postal code' is required", "CHECKOUT.CUSTOMER.VALIDATOR.UPPER_FIRST_NAME_FIRST_LETTER_ERROR": " First name should starts from the capital letter", "CHECKOUT.DIALOG.CANCEL_EDIT_ADDRESS.BUTTON.DISCARD": "Discard Changes ", "CHECKOUT.DIALOG.CANCEL_EDIT_ADDRESS.BUTTON.SAVE": "Save Changes", "CHECKOUT.DIALOG.CANCEL_EDIT_ADDRESS.MESSAGE": "You are about to discard your changes", "CHECKOUT.DIALOG.CANCEL_EDIT_ADDRESS.TITLE": "Are you sure you want to cancel changes?", "CHECKOUT.DISCOUNT.CODE.APPLIED.CODE.MESSAGE": "Discount has been successfully applied to all products sold by METRO in your cart.", "CHECKOUT.DISCOUNT.CODE.APPLY.BUTTON": "Apply", "CHECKOUT.DISCOUNT.CODE.FAILED.ALERT": "Something went wrong please try again.", "CHECKOUT.DISCOUNT.CODE.FIELD.ACTIVE.BUTTON": "Apply discount code", "CHECKOUT.DISCOUNT.CODE.INVALID.COUNTRY": "This discount code is currently not redeemable in your country.", "CHECKOUT.DISCOUNT.CODE.INVALID.DISABLED": "Unable to find a valid discount matching the code entered.", "CHECKOUT.DISCOUNT.CODE.INVALID.EXCEED_VALUE": "This discount code can not be applied as the order value is exceeded.", "CHECKOUT.DISCOUNT.CODE.INVALID.EXPIRED": "The discount code you entered is expired.", "CHECKOUT.DISCOUNT.CODE.INVALID.MIN_AMOUNT": "This discount code can be applied only from a minimum order value of {minSpendingAmount}€ and for products from the seller METRO.", "CHECKOUT.DISCOUNT.CODE.INVALID.NOT_FOUND": "The discount code you enter is invalid, please confirm the code and try again.", "CHECKOUT.DISCOUNT.CODE.INVALID.ORDERLINE": "The discount code is valid for only certain products sold by METRO and it cannot be applied to your cart.", "CHECKOUT.DISCOUNT.CODE.INVALID.PENDING": "The discount code is not yet available.", "CHECKOUT.DISCOUNT.CODE.INVALID.REDEEMED": "This discount code has already been redeemed.\n", "CHECKOUT.DISCOUNT.CODE.INVALID.SYMBOLS.MESSAGE": "Looks like you entered not valid symbols.", "CHECKOUT.DISCOUNT.CODE.INVALID.USER": "This discount code can not be applied by this user.", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.LOGIN": "<PERSON><PERSON>", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.OR": "or", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.PART1": "You need to be logged in to use discount codes. ", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.PART2": "to apply your discount code.", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.PLEASE": "Please", "CHECKOUT.DISCOUNT.CODE.LOGGEDOUT.ALERT.REGISTER": "Register", "CHECKOUT.DISCOUNT.CODE.SCREEN_READER.REMOVE": "Remove Discount Code Applied.", "CHECKOUT.DISCOUNT.CODE.SCREEN_READER.SUCCESS": "Discount Code Applied Successfully.\n", "CHECKOUT.DISCOUNT.CODE.TITLE": "Discount code", "CHECKOUT.DISCOUNT.CODE.TOOLTIP": "Only one discount code can be used for the <strong>OnlineShop</strong>. Currently, discounts only apply to products from the seller <strong>METRO</strong>.", "CHECKOUT.EMPLOYEE.ORDER_REVIEW_PAGE.DIALOG.FAILED_ORDER.BODY": "There was an error confirming this order as another impersonation session was active.", "CHECKOUT.ERROR.3DS1.WRONG_LOG_IN": "3D Secure authentication failed. You have not been charged. Please try again or contact your bank if the problem persists. Alternatively, you can change the payment method.", "CHECKOUT.ERROR.ADDRESS_ALREADY_EXISTS": "This address already exists in your Address Book, please create a new one or go to your <a href=\"{myMetroAddressBookUrl}\" target=\"_blank\">user profile and select this as your address</a>.", "CHECKOUT.ERROR.INVALID_ADDRESS_DATA": "Field {fieldParam} in {typeParam} has an error:", "CHECKOUT.ERROR.NO_PAYMENT_SELECTED": "There was an issue with the selected Payment Method. Please choose a Payment Method again.", "CHECKOUT.ERROR.SOFORT": "There was an error with your payment details, please choose your payment method again to proceed.", "CHECKOUT.FREIGHT_FORWARDING.CHANGE": "Change", "CHECKOUT.FREIGHT_FORWARDING.DESCRIPTION_LINK": " Freight Forwarding ", "CHECKOUT.FREIGHT_FORWARDING.DESCRIPTION_PREFIX": "Some items in your cart require", "CHECKOUT.FREIGHT_FORWARDING.DESCRIPTION_SUFFIX": "for being large/heavy. Please add your phone number so we can contact you for the details.", "CHECKOUT.FREIGHT_FORWARDING.DIALOG.BODY": "Please note that you may be contacted by the Seller or a transport company by telephone, SMS or e-mail to agree a delivery date. If you have any questions regarding delivery, please contact the Seller directly.", "CHECKOUT.FREIGHT_FORWARDING.DIALOG.HEADER": "Ordering bulky items from a Marketplace Seller", "CHECKOUT.FREIGHT_FORWARDING.INPUT": "Telephone Number", "CHECKOUT.FREIGHT_FORWARDING.INPUT.VALIDATION": "A valid phone number is required. A valid phone number should  between 6 and 15 digits.", "CHECKOUT.FREIGHT_FORWARDING.LABEL": "Telephone Number", "CHECKOUT.FREIGHT_FORWARDING.TITLE": "Freight Forwarding", "CHECKOUT.INVALID_OFFER_OVERLAY.ACTION.BACK_TO_CART": "Return to Cart", "CHECKOUT.INVALID_OFFER_OVERLAY.INFO": "You are unable to complete this order as a product in your cart is no longer available. Please return to the cart to review your order.", "CHECKOUT.INVALID_OFFER_OVERLAY.TITLE": "This product is unavailable", "CHECKOUT.OFFER.ERROR.B2B_ONLY": " Offer B2B only", "CHECKOUT.OFFER.ERROR.CANT_BE_ADDED_TO_CART": " Offer can’t be added to the cart", "CHECKOUT.OFFER.ERROR.DELIVERY_OPTION_NOT_AVAILABLE": "Requested delivery method is not available", "CHECKOUT.OFFER.ERROR.INACTIVE": " Not available anymore", "CHECKOUT.OFFER.ERROR.NOT_AVAILABLE_IN_GERMANY": " Unfortunately, this offer is not available in Germany.", "CHECKOUT.OFFER.ERROR.NOT_AVAILABLE_IN_SPAIN": " Unfortunately, this offer is not available in Spain.", "CHECKOUT.OFFER.ERROR.NOT_ENOUGH_QUANTITY": " Not enough inventory in stock", "CHECKOUT.OFFER.ERROR.NOT_EXIST": " Offer not exists", "CHECKOUT.ORDER_CREATION.ERROR.VALIDATION_ERROR": " Order creation failed due to invalid data", "CHECKOUT.ORDER_MANAGEMENT.ERROR.FAILED_DEPENDENCY": " Sorry! We are experiencing technical difficulties.", "CHECKOUT.ORDER.ERROR.INCORRECT_STATUS": " Order has incorrect status", "CHECKOUT.ORDER.ERROR.INVOICE": " Invoice", "CHECKOUT.ORDER.ERROR.INVOICE_DATE": " 'Date:’", "CHECKOUT.ORDER.ERROR.INVOICE_NUM": " 'Invoice #:’", "CHECKOUT.ORDER.ERROR.INVOICE_PRICE": " Price", "CHECKOUT.ORDER.ERROR.INVOICE_PRODUCT": " Product", "CHECKOUT.ORDER.ERROR.INVOICE_QTY": " Qty", "CHECKOUT.ORDER.ERROR.INVOICE_SELLER_NAME": " Seller name", "CHECKOUT.ORDER.ERROR.INVOICE_SHIPPING_COST": " 'Shipping cost:’", "CHECKOUT.ORDER.ERROR.INVOICE_SUB_TOTAL": " 'Sub Total:’", "CHECKOUT.ORDER.ERROR.INVOICE_SUM": " Sum", "CHECKOUT.ORDER.ERROR.INVOICE_TAXES": " 'Taxes:’", "CHECKOUT.ORDER.ERROR.INVOICE_TO": " 'To:’", "CHECKOUT.ORDER.ERROR.INVOICE_TOTAL": " 'Total:’", "CHECKOUT.ORDER.ERROR.ORDER_LINES_NOT_FOUND": " Order lines not found", "CHECKOUT.ORDER.ERROR.ORDER_NOT_EXIST": " Order does not exist", "CHECKOUT.ORDER.SAVE.SUCCESS.MESSAGE": "Order successfully saved", "CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_ERROR": " Only letters, digits, symbols . , - ' / and spaces are available here", "CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_MAX_LENGTH": " Max length for field 'Address Line' is {limit}.", "CHECKOUT.ORDER.VALIDATION.ADDRESS_LINE_REQUIRED": " Address Line is required.", "CHECKOUT.ORDER.VALIDATION.BILLING_ADDRESS_REQUIRED": " Billing address is required", "CHECKOUT.ORDER.VALIDATION.CITY_FORMAT_ERROR": " Only letters, symbols . , - ' / and spaces are available here", "CHECKOUT.ORDER.VALIDATION.CITY_MAX_LENGTH": " Max length for field 'city' is {limit}", "CHECKOUT.ORDER.VALIDATION.CITY_REQUIRED": " Field 'city' is required", "CHECKOUT.ORDER.VALIDATION.COUNTRY_INVALID": " Country format is invalid.", "CHECKOUT.ORDER.VALIDATION.COUNTRY_NOT_FOUND": " Country not found", "CHECKOUT.ORDER.VALIDATION.COUNTRY_REQUIRED": " Field 'country' is required", "CHECKOUT.ORDER.VALIDATION.COUNTRY_REQUIRED_LENGTH": " Length for field 'country' is {limit}", "CHECKOUT.ORDER.VALIDATION.DEFAULT_IS_BOOLEAN": " Field 'isDefault' should be boolean", "CHECKOUT.ORDER.VALIDATION.DEVICE_FINGERPRINT_REQUIRED": " Device Fingerprint is required", "CHECKOUT.ORDER.VALIDATION.FIRST_NAME_MAX_LENGTH": " Max length for field 'First Name' is {limit}.", "CHECKOUT.ORDER.VALIDATION.FIRST_NAME_MIN_LENGTH": " Min length for field 'First Name' is {limit}.", "CHECKOUT.ORDER.VALIDATION.FIRST_NAME_REQUIRED": " First Name is required.", "CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_INCORRECT_LENGTH": " Not valid, phone number should be between 6 and 15 digits", "CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_MAX_LENGTH": " Max length for field 'freightForwardPhone' is {limit}", "CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_REQUIRED": " Field 'freightForwardPhone' is required", "CHECKOUT.ORDER.VALIDATION.FREIGHT_FORWARD_PHONE_TYPE": " Field 'freightForwardPhone' should be number", "CHECKOUT.ORDER.VALIDATION.HOUSE_NUMBER_MAX_LENGTH": " Max length for field 'House number' is {limit}.", "CHECKOUT.ORDER.VALIDATION.INVALID_ITALY_TAX_NUMBER": " The tax identification number should be 16 characters long", "CHECKOUT.ORDER.VALIDATION.INVALID_PAYMENT_METHOD": " Payment Method is invalid.", "CHECKOUT.ORDER.VALIDATION.LAS_NAME_MIN_LENGTH": " Min length for field 'Last Name' is {limit}.", "CHECKOUT.ORDER.VALIDATION.LAST_NAME_MAX_LENGTH": " Max length for field 'Last Name' is {limit}.", "CHECKOUT.ORDER.VALIDATION.LAST_NAME_REQUIRED": " Last Name is required.", "CHECKOUT.ORDER.VALIDATION.LATIN_ERROR": " Only letters, symbols . , - ' and spaces are available here", "CHECKOUT.ORDER.VALIDATION.LATIN_WITH_DIGITS_ERROR": " Only letters, digits, symbols . , - ' and spaces are available here", "CHECKOUT.ORDER.VALIDATION.NOT_VALID": " Not valid", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID": " Postal code format is invalid.", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID_FRANCE_AREA": "Invalid shipping address. Your saved address is in restricted areas of France and currently we don't ship there. Please change or add a new shipping address.", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID_GERMANY_AREA": "Invalid shipping address. Your saved address is in restricted areas of Germany and currently we don't ship there. Please change or add a new shipping address.", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID_PORTUGAL_AREA": " Invalid shipping address. Your saved address is in restricted areas of Portugal and currently we don't ship there. Please change or add a new shipping address.", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_INVALID_SPAIN_AREA": "Invalid shipping address. Your saved address is outside mainland Spain, currently we only ship within it. Please change or add a new shipping address.", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_MAX_LENGTH": " Max length for field 'postal code' is {limit}", "CHECKOUT.ORDER.VALIDATION.POSTAL_CODE_REQUIRED": " Field 'postal code' is required", "CHECKOUT.ORDER.VALIDATION.SHIPPING_ADDRESS_REQUIRED": " Shipping address is required", "CHECKOUT.ORDER.VALIDATION.SHIPPING_ONLY_GERMANY": "We're currently shipping only to Germany", "CHECKOUT.ORDER.VALIDATION.SHIPPING_ONLY_ITALY": "We're currently shipping only to Italy", "CHECKOUT.ORDER.VALIDATION.SHIPPING_ONLY_SPAIN": "We're currently shipping only to Spain", "CHECKOUT.ORDER.VALIDATION.STATUS_REQUIRED": " Status is required", "CHECKOUT.ORDER.VALIDATION.STREET_MAX_LENGTH": " Max length for field 'Street' is {limit}.", "CHECKOUT.ORDER.VALIDATION.VALID_STATUS": " Choose a valid status", "CHECKOUT.PARTIALLY_SUCCESS_VIEW.MESSAGE.DESCRIPTION_PART_1": "Looks like some items in your order had failed to be purchased.", "CHECKOUT.PARTIALLY_SUCCESS_VIEW.MESSAGE.DESCRIPTION_PART_2": "Please review your order and contact our support to fix this issue.", "CHECKOUT.PARTIALLY_SUCCESS_VIEW.MESSAGE.MAIN_IMAGE.ALT": "Partially success payment", "CHECKOUT.PARTIALLY_SUCCESS_VIEW.MESSAGE.REVIEW_ORDER_BTN.TITLE": "Review the order", "CHECKOUT.PARTIALLY_SUCCESS_VIEW.MESSAGE.TITLE": "Oops! Something went wrong.", "CHECKOUT.PAYMENT_DATA_EDIT": "Change", "CHECKOUT.PAYMENT_METHODS.MESSAGE.IDEAL_BANK_LABEL": "Select bank", "CHECKOUT.PAYMENT_METHODS.MESSAGE.IDEAL_BANK_PLACEHOLDER": "Choose your bank…", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.CLOSE": "Close", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.FAILED_MESSAGE": "Your chosen payment method was unable to proceed. Please try again or choose a different payment method.", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.FAILED_TITLE": "Payment was unsuccessful", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.MESSAGE": "It may take a moment to process your payment.", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.REDIRECT": "Other payment methods", "CHECKOUT.PAYMENT_PROCESSING_OVERLAY.TITLE": "Processing payment...", "CHECKOUT.PAYMENT.ACCOUNT_HOLDER_TITLE": "Bank Account Holder", "CHECKOUT.PAYMENT.ACCOUNT.FULL_NAME": "Full name", "CHECKOUT.PAYMENT.ALERT": "Payment failed.  Please try again or choose another method.", "CHECKOUT.PAYMENT.BILLIE": "<PERSON><PERSON> auf Rechnung für Firmenkunden", "CHECKOUT.PAYMENT.BILLIE_DESCRIPTION": "Interest-free\nNo registration needed\nEurope's go-to B2B payment solution", "CHECKOUT.PAYMENT.BILLIE_SUBHEADING": "Pay after 30 days", "CHECKOUT.PAYMENT.CASH": "Cash in advance", "CHECKOUT.PAYMENT.CREDIT": "Card", "CHECKOUT.PAYMENT.CREDIT_CARD.EXPIRES": "Expires", "CHECKOUT.PAYMENT.CREDIT_CARD.LAST_FOUR_DIGITS": "Credit Card ending in {creditCardLastFourDigits}", "CHECKOUT.PAYMENT.CREDIT_CARD.PAYMENT_METHOD_TOKENS_TITLE": "Saved payment method", "CHECKOUT.PAYMENT.CREDIT_CARD.SAVE_PAYMENT": "Save this payment method for future purchases", "CHECKOUT.PAYMENT.CREDIT_CARD.UPDATE_PAYMENT": "Update this entered card to be default payment option", "CHECKOUT.PAYMENT.CREDIT_CARD.USE_ANOTHER_CARD": "Use a different card", "CHECKOUT.PAYMENT.CREDIT_CARD.USE_ANOTHER_IBAN": "Use a different IBAN", "CHECKOUT.PAYMENT.CREDIT.CLICK_TO_EDIT": "Click to edit", "CHECKOUT.PAYMENT.DATE_OF_BIRT.DAY": "Day", "CHECKOUT.PAYMENT.DATE_OF_BIRT.MONTH": "Month", "CHECKOUT.PAYMENT.DATE_OF_BIRT.YEAR": "Year", "CHECKOUT.PAYMENT.DATE_OF_BIRTH": "Date of birth (optional)", "CHECKOUT.PAYMENT.DIRECT_DEBIT": "Direct Debit", "CHECKOUT.PAYMENT.DIRECT_DEBIT.FRANCE": "Direct Debit", "CHECKOUT.PAYMENT.DOB": "Date of birth", "CHECKOUT.PAYMENT.ERROR.METHOD_NOT_SELECTED": "Bitte wähle eine Zahlungsmethode aus", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_DATA_LINK": "Correct your data", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_PM": "Change the payment method", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_PM_3DS": "select different payment method or card", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_PM_3DS_MOBILE": "Please try again or change your card", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_PM_3DS_TEXT": "Please try to make your payment again or", "CHECKOUT.PAYMENT.FAILED.ALERT.CHANGE_PMP": "Change your payment method to proceed", "CHECKOUT.PAYMENT.FAILED.ALERT.DATA_COMBINATION": "The combination of your name and address could not be validated.", "CHECKOUT.PAYMENT.FAILED.ALERT.OR": "or", "CHECKOUT.PAYMENT.FAILED.ALERT.PAYMENT_DATA": "Unfortunately, it is not possible to use the selected payment method.", "CHECKOUT.PAYMENT.FAILED.ALERT.TITLE": "Payment failed", "CHECKOUT.PAYMENT.IBAN_TITLE": "IBAN", "CHECKOUT.PAYMENT.IDEAL": "iDeal", "CHECKOUT.PAYMENT.LEARN_MORE_TITLE": "Learn more", "CHECKOUT.PAYMENT.NEW_LABEL": "New", "CHECKOUT.PAYMENT.OPEN_INVOICE": "Open Invoice", "CHECKOUT.PAYMENT.ORDER_BELOW_MAX_AMOUNT": "This option is not available for orders above {textParam} €. <strong>Please select other available options to proceed.</strong>", "CHECKOUT.PAYMENT.PAYPAL": "PayPal", "CHECKOUT.PAYMENT.PAYPAL_INS": "Pay in 4", "CHECKOUT.PAYMENT.PAYPAL_INS_FRANCE": "Pay in 4", "CHECKOUT.PAYMENT.PAYPAL_INS.ITALY": "Pay in 3 instalments", "CHECKOUT.PAYMENT.PAYPAL_INS.SPAIN": "Pay in 3 instalments", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO": "Pay in 4 interest-free payments with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_1_SPAIN": "0% TAE.", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_1.SPAIN": "0% TAE.", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_FRANCE": "Pay in 4 installments with no fees with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_GERMANY": "Pay after 30 days with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_ITALY": "Pay in 3 installments of €40.00 without interest with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO_SPAIN": "Pay in 3 interest-free instalments of €40.00 with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO.FRANCE": "Pay in 4 installments with no fees with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO.GERMANY": "Pay after 30 days with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO.ITALY": "Pay in 3 installments of €40.00 without interest with", "CHECKOUT.PAYMENT.PAYPAL.PAY_LATER_INFO.SPAIN": "Pay in 3 interest-free instalments of €40.00 with", "CHECKOUT.PAYMENT.PAYSAFE": "Pay Safely", "CHECKOUT.PAYMENT.PHONE_NUMBER_TITLE": "Phone", "CHECKOUT.PAYMENT.RATE_PAY_INVOICE": "Invoice", "CHECKOUT.PAYMENT.RATE_PAY.LEGAL_TEXT.B2B": "By agreeing to the SEPA Direct Debit Mandate, you authorise Ratepay GmbH to collect payments from your account by direct debit. At the same time, you instruct your bank to debit your account in accordance with the instructions from Ratepay GmbH.<br/><br/>\nNote: This SEPA Direct Debit Mandate is only for the collection of direct debits that have been withdrawn from corporate accounts. You are not entitled to demand a refund of the charged amount after it has been collected. You are entitled to instruct your bank not to redeem direct debits until the due date.<br/><br/>\nThe mandate reference and creditor identification number will be communicated to you separately.", "CHECKOUT.PAYMENT.RATE_PAY.LEGAL_TEXT.B2B.FRANCE": "By checking this box, you authorize Ayden N.V. to send instructions to your bank to debit your account, and your bank to debit your account in accordance with Ayden N.V.'s instructions. You have the right to be reimbursed by your bank according to the conditions described in the agreement you have signed with it. A request for reimbursement must be made no later than 8 weeks after the date on which your account was debited for an authorized direct debit.\n<br/>\n<strong>Please check that your IBAN has been entered correctly</strong> and that you have <strong>sufficient funds</strong>. Payment will be debited from the bank account provided, which may take several days.", "CHECKOUT.PAYMENT.RATE_PAY.LEGAL_TEXT.B2C": "By agreeing to the SEPA Direct Debit Mandate, you authorise Ratepay GmbH to collect payments from your account by direct debit. At the same time, you instruct your bank to debit your account in accordance with the instructions from Ratepay GmbH.<br/><br/>\nNote: As part of your rights, you are entitled to a refund from your bank. A refund must be claimed within 8 weeks, starting from the date on which your account was debited. The terms and conditions of your bank apply.<br/><br/>\nThe mandate reference and creditor identification number will be communicated to you separately.", "CHECKOUT.PAYMENT.RATE_PAY.LEGAL_TEXT.B2C.FRANCE": "By checking this box, you authorize Ayden N.V. to send instructions to your bank to debit your account, and your bank to debit your account in accordance with Ayden N.V.'s instructions. You have the right to be reimbursed by your bank according to the conditions described in the agreement you have signed with it. A request for reimbursement must be made no later than 8 weeks after the date on which your account was debited for an authorized direct debit.\n<br/>\n<strong>Please check that your IBAN has been entered correctly</strong> and that you have <strong>sufficient funds</strong>. Payment will be debited from the bank account provided, which may take several days.", "CHECKOUT.PAYMENT.SEPA_DIRECT_DEBIT": "SEPA Direct Debit", "CHECKOUT.PAYMENT.SEPADD.CREDITOR.ID": "Creditor ID", "CHECKOUT.PAYMENT.SEPADD.NOTICE": "<strong>Please check your IBAN is entered correctly</strong> and that you have <strong>sufficient funds</strong>. Payment will be charged from the provided bank account which may take several days.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.FRANCE": "<strong>Me<PERSON><PERSON> de vérifier que votre IBAN a été saisi correctement</strong> et que vous disposez de <strong>fonds suffisants</strong>. Le paiement sera débité du compte bancaire fourni, ce qui peut prendre plusieurs jours.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.GERMANY": "<strong>Bitte überprüfen Sie, ob Ihre IBAN korrekt eingegeben wurde und ob Ihr Konto über ausreichende Deckung verfügt.</strong> Die Abbuchung erfolgt von dem angegebenen Bankkonto, wobei dieser Vorgang mehrere Tage in Anspruch nehmen kann.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.ITALY": "Per favore, controlla che il tuo IBAN sia inserito correttamente e di disporre di fondi sufficienti. Il pagamento verrà addebitato sul conto bancario indicato e potrebbe richiedere alcuni giorni.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.NETHERLANDS": "Controleer of je IBAN juist is ingevoerd en of je voldoende saldo hebt. De betaling wordt van de opgegeven bankrekening afgeschreven, wat enkele dagen kan duren.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.PORTUGAL": "Por favor, verifique se o seu IBAN está correto e se dispõe de crédito suficiente. O pagamento será debitado na conta bancária indicada. Este processo pode demorar alguns dias.", "CHECKOUT.PAYMENT.SEPADD.NOTICE.SPAIN": "Por favor, comprueba que tu IBAN es correcto y que tienes saldo suficiente. El pago se cargará en la cuenta bancaria facilitada. Esto podría tardar varios días.", "CHECKOUT.PAYMENT.SEPADD.TNC": "Ich ermächtige die Adyen N.V. (Creditor ID: *******************) Zahlungen von meinem Konto mittels Lastschrift einzuziehen. Zugleich weise ich mein Kreditinstitut an, die von der Adyen N.V. auf mein Konto gezogenen Lastschriften einzulösen.", "CHECKOUT.PAYMENT.SEPADD.TNC.1": "Ich ermächtige die Adyen N.V. Zahlungen von meinem Konto mittels Lastschrift einzuziehen. Zugleich weise ich mein Kreditinstitut an, die von der Adyen N.V. auf mein Konto gezogenen Lastschriften einzulösen.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.FRANCE": "Vous bénéficiez du droit d'être remboursé par votre banque selon les conditions décrites dans la convention que vous avez passée avec elle. Une demande de remboursement doit être présentée dans les 8 semaines suivant la date de débit de votre compte pour un prélèvement autorisé.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.GERMANY": "Ich kann innerhalb von acht W<PERSON>en, beginnend mit dem Belastungsdatum, die Erstattung des belasteten Betrages verlangen. Es gelten dabei die mit meinem Kreditinstitut vereinbarten Bedingungen.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.ITALY": "Ti invitiamo a controllare di aver inserito correttamente il tuo IBAN e di disporre di fondi sufficienti. Il pagamento verrà addebitato sul conto corrente indicato, operazione che potrebbe richiedere alcuni giorni.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.NETHERLANDS": "Controleer of je IBAN correct is ingevoerd en of er voldoende saldo op je rekening staat. Het bedrag zal worden afgeschreven van de opgegeven bankrekening, dit proces kan enkele dagen duren.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.PORTUGAL": "Por favor, verifique se o seu IBAN foi introduzido corretamente e se existe saldo suficiente na sua conta. O montante será debitado da conta bancária indicada, embora este processo possa demorar alguns dias.", "CHECKOUT.PAYMENT.SEPADD.TNC.1.SPAIN": "Compruebe que ha introducido correctamente su IBAN y que dispone de fondos suficientes. El pago se cargará en la cuenta bancaria facilitada, lo que puede tardar varios días.", "CHECKOUT.PAYMENT.SEPADD.TNC.FRANCE": "En cochant cette case, vous autorisez Ayden N.V à envoyer des instructions à votre banque pour débiter votre compte, et votre banque à débiter votre compte conformément aux instructions de Ayden N.V.", "CHECKOUT.PAYMENT.SEPADD.TNC.GERMANY": "Ich ermächtige die Adyen N.V. (Creditor ID: *******************) Zahlungen von meinem Konto mittels Lastschrift einzuziehen. Zugleich weise ich mein Kreditinstitut an, die von der Adyen N.V. auf mein Konto gezogenen Lastschriften einzulösen.", "CHECKOUT.PAYMENT.SEPADD.TNC.ITALY": "<PERSON><PERSON><PERSON><PERSON><PERSON>a casella, autorizzi Adyen N.V. a riscuotere i pagamenti dal tuo conto tramite addebito diretto. Allo stesso tempo, autorizzi la tua banca a eseguire gli addebiti diretti effettuati da Adyen N.V. sul tuo conto. Hai diritto a un rimborso da parte della tua banca secondo i termini stabiliti nel contratto con la tua banca. La richiesta di rimborso deve essere presentata entro 8 settimane dalla data in cui il conto è stato addebitato con un addebito diretto autorizzato.", "CHECKOUT.PAYMENT.SEPADD.TNC.NETHERLANDS": "Ik machtig Adyen N.V. om betalingen van mijn rekening te innen door middel van automatische incasso. Tegelijkertijd geef ik mijn bank opdracht de door Adyen N.V. van mijn rekening afgeschreven incasso’s te honoreren. Ik kan de terugbetaling van het afgeschreven bedrag eisen binnen acht weken na de datum van afschrijving. De met mijn bank overeengekomen voorwaarden zijn van toepassing.", "CHECKOUT.PAYMENT.SEPADD.TNC.PORTUGAL": "Autorizo a Adyen N.V. a cobrar pagamentos da minha conta por débito direto. Ao mesmo tempo, dou instruções ao meu banco para honrar os débitos directos debitados pela Adyen N.V. na minha conta. Posso exigir o reembolso do montante debitado no prazo de oito semanas a contar da data do débito. Aplicam-se os termos e condições acordados com o meu banco.", "CHECKOUT.PAYMENT.SEPADD.TNC.SPAIN": "Al marcar esta casilla, autoriza a Ayden N.V. a enviar instrucciones a su banco para que efectúe el cargo en su cuenta, y a su banco a efectuar el cargo en su cuenta. Tiene derecho a que su banco le reembolse el importe de acuerdo con las condiciones establecidas en el contrato con su banco. La solicitud de reembolso deberá presentarse en un plazo de 8 semanas a partir de la fecha en la que se cargó en su cuenta un adeudo directo autorizado.", "CHECKOUT.PAYMENT.SOFORT": "Sofort", "CHECKOUT.PAYMENT.STEP.CONTINUE": "Continue", "CHECKOUT.PAYMENT.STEP.SAVE_CONTINUE": "Save & Continue", "CHECKOUT.PAYMENT.STEP.TITLE": "Payment Method", "CHECKOUT.PAYMENT.TITLE": "Payment method", "CHECKOUT.PAYMENT.UA.ONLY_CREDIT_CARD_AVAILABLE": "Only Credit card and Paypal are the payment methods available for the Support for Ukraine campaign.", "CHECKOUT.PAYMENT.VAT_ID_TITLE": "VAT Number (optional)", "CHECKOUT.PAYMENT.VAT_ID.PLACEHOLDER": "DEXXXXXXXXX", "CHECKOUT.PAYMENT.VAT_ID.TOOLTIP": "<p>Please add the VAT ID in this format: </p>\n<p>Country Code + 9 Digits.</p>\n\n<p>Example: <strong>DE</strong><em>123456789<em></p>", "CHECKOUT.PRIVACY_POLICY_LINK": "Privacy Policy", "CHECKOUT.PRIVACY_POLICY_TEXT1": "Check our ", "CHECKOUT.PRIVACY_POLICY_TEXT2": " to learn how we handle data.", "CHECKOUT.PRODUCT.ERROR.OUT_OF_STOCK": " Offer is out of stock", "CHECKOUT.PRODUCT.ERROR.PRICE_HAS_CHANGED": " Price for product {name} has been changed. Please check", "CHECKOUT.PURCHASE_ORDER_NUMBER": "Purchase order number", "CHECKOUT.RATE_PAY_BLOCKED.703.LINK_1.HREF": "https://www.ratepay.com/legal-payment-dataprivacy/?lang=en", "CHECKOUT.RATE_PAY_BLOCKED.703.LINK_1.TEXT": "Data Privacy Statement", "CHECKOUT.RATE_PAY_BLOCKED.703.LINK_2.HREF": "https://www.ratepay.com/en/contact/", "CHECKOUT.RATE_PAY_BLOCKED.703.LINK_2.TEXT": "contact form", "CHECKOUT.RATE_PAY_BLOCKED.703.TEXT_1": "It is not possible to use the selected payment method {Produkt}. This decision is based on automated data processing. You can find further information in the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.703.TEXT_2": " or use the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.720.LINK_1.HREF": "https://www.ratepay.com/legal-payment-dataprivacy/?lang=en", "CHECKOUT.RATE_PAY_BLOCKED.720.LINK_1.TEXT": "Data Privacy Statement", "CHECKOUT.RATE_PAY_BLOCKED.720.LINK_2.HREF": "https://www.ratepay.com/en/contact/", "CHECKOUT.RATE_PAY_BLOCKED.720.LINK_2.TEXT": "contact form", "CHECKOUT.RATE_PAY_BLOCKED.720.TEXT_1": "It is not possible to use the selected payment method {Produkt}. This decision is based on automated data processing. You can find further information in the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.720.TEXT_2": " or use the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.721.LINK_1.HREF": "https://www.ratepay.com/legal-payment-dataprivacy/?lang=en", "CHECKOUT.RATE_PAY_BLOCKED.721.LINK_1.TEXT": "Data Privacy Statement", "CHECKOUT.RATE_PAY_BLOCKED.721.LINK_2.HREF": "https://www.ratepay.com/en/contact/", "CHECKOUT.RATE_PAY_BLOCKED.721.LINK_2.TEXT": "contact form", "CHECKOUT.RATE_PAY_BLOCKED.721.TEXT_1": "It is not possible to use the selected payment method {Produkt}. This decision is based on automated data processing. You can find further information in the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.721.TEXT_2": " or use the Ratepay ", "CHECKOUT.RATE_PAY_BLOCKED.730.TEXT": "Unfortunately, it is not possible to use the selected payment method {Produkt}.", "CHECKOUT.RATE_PAY_BLOCKED.USE_DIFFERENT": "Use a different payment method.", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE": "Within the order process, we will be sending your data to RatePAY GmbH for the purpose of verifying your identity and creditworthiness as well as the performance of the contract. The <a target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-600 focus:outline-none focus:ring-0 focus:underline\" href=\"https://www.ratepay.com/legal\">Additional Terms and Conditions and Data Protection Notice of RatePAY GmbH</a> apply.", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE.FRANCE": "<strong>Please check your IBAN is entered correctly</strong> and that you have <strong>sufficient funds</strong>. Payment will be charged from the provided bank account which may take several days.", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE.GERMANY": "Bitte überprüfen Sie, ob Ihre IBAN korrekt eingegeben wurde und ob Ihr Konto über ausreichende Deckung verfügt. Die Abbuchung erfolgt von dem angegebenen Bankkonto, wobei dieser Vorgang mehrere Tage in Anspruch nehmen kann.", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE.LINK": "Additional Terms and Conditions and Data Protection Notice of RatePAY GmbH", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE.PREFIX": "Within the order process, we will be sending your data to RatePAY GmbH for the purpose of verifying your identity and creditworthiness as well as the performance of the contract. The", "CHECKOUT.RATE_PAY_DIRECT_DEBIT.LEGAL_NOTICE.SUFFIX": "apply.", "CHECKOUT.RATE_PAY.LEGAL_NOTICE.LINK": "Additional Terms and Conditions for RatePAY payment methods and the data protection notice of RatePAY GmbH", "CHECKOUT.RATE_PAY.LEGAL_NOTICE.PREFIX": "Within the order process, we will be sending your data to RatePAY GmbH for the purpose of verifying your identity and creditworthiness as well as the performance of the contract.", "CHECKOUT.RATE_PAY.REJECTED_REQUEST.LINK.HREF": "https://www.ratepay.com/en/additional-general-terms-and-conditions-and-data-protection-notice-dach/", "CHECKOUT.RATE_PAY.REJECTED_REQUEST.LINK.TEXT": "additional terms and conditions and privacy policy for RatePAY payment methods", "CHECKOUT.RATE_PAY.REJECTED_REQUEST.PREFIX": "Unfortunately, it is not possible to use the selected payment method Invoice. This decision is based on an automated data processing procedure. For details click", "CHECKOUT.REFERENCE_NUMBER": "Reference number", "CHECKOUT.RESEARCH_INTERVIEW.MODAL.MESSAGE": "Get a METRO voucher for the online shop – simply register and take part in our customer interviews!", "CHECKOUT.RESEARCH_INTERVIEW.MODAL.TITLE": "Your opinion counts: Help us to improve your shopping experience!", "CHECKOUT.REVIEW_PAGE.MESSAGE.TERMS_AND_CONDITIONS_RATEPAY": "At the same time you agree to the terms of payment of <a href=\"{ratepayLegalPaymentTermsLink}\" target=\"_blank\" rel=\"noopener noreferrer\">RatePAY GmbH</a>  as well as to the performance of a <a href=\"{ratepayLegalPaymentDataPrivacyLink}\" target=\"_blank\" rel=\"noopener noreferrer\">risk check by RatePAY GmbH</a>. ", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FOR_VAS": "By clicking on “{buttonMessage}” you accept the terms and conditions of the respective seller and in case you selected a installation service the terms and conditions of the respective service provider (see link to the service provider and/or seller in the shopping cart overview on the left). There you will also find the seller's and the service provider's privacy policy and cancellation policy. Based on your order, we will email you direct marketing for similar products and services. You can object to this via the link in the e-mail. There are no costs beyond the basic tariff for the objection.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_OPT_OUT": "as well as at any time and free of charge via the link contained in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_PAYPAL_BNPL_TEXT_1": "Clicking on “{buttonMessage}” constitutes an undertaking on your part to pay for the said order. By clicking on “{buttonMessage}” you certify having read and accepted without reservation the general terms and conditions of the relevant seller (see link to seller in the shopping cart overview on the left, where you can also find the Seller’s cancellation and data protection policy).", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_PAYPAL_TEXT_1": "By clicking on “PayPal Buy Now” you certify having read and accepted without reservation the general terms and conditions of the relevant seller (see link to seller in the shopping cart overview on the left, where you can also find the Seller’s cancellation and data protection policy).", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_TEXT_1": "By clicking on “{buttonMessage}” you certify having read and accepted without reservation the general terms and conditions of the relevant seller (see link to seller in the shopping cart overview on the left, where you can also find the Seller’s cancellation and data protection policy).", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_TEXT_2": "You consent to Metro Markets GmbH (Metro Markets) sending you direct advertising by email about products or services similar to those you have purchased.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_FRANCE_TEXT_2_WITH_LINK": "METRO may send you advertising e-mails about products or services similar to those you have purchased. You can object to this by clicking", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT": "as well as at any time and free of charge via the link contained in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT_CHECKBOX": "I don't want to receive direct marketing communications from METRO by e-mail about services and/or products similar to those purchased.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT_TOGGLE": "here", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT.FRANCE": "as well as at any time and free of charge via the link contained in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT.ITALY": "as well as at any time and free of charge via the link contained in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_OPT_OUT.SPAIN": "as well as at any time and free of charge via the link contained in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_RATEPAY_LINK_1": "RatePAY GmbH", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_RATEPAY_LINK_2": "risk check by RatePAY GmbH", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_RATEPAY_TEXT_1": "At the same time you agree to the terms of payment of", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_RATEPAY_TEXT_2": "as well as to the performance of a", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_1": "By click on “{buttonMessage}” I agree with the Sales GTC of the respective Seller (see link to the Seller in the card overview on the left). There you will also find the Seller’s privacy policy and cancellation policy.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_1.ITALY": "Clicking on “{buttonMessage}” constitutes an undertaking on your part to pay for the said order. By clicking on “{buttonMessage}” you certify having read and accepted without reservation the general terms and conditions of the relevant seller (see link to seller in the shopping cart overview on the left, where you can also find the Seller’s cancellation and data protection policy).", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_1.SPAIN": "Clicking on “{buttonMessage}” constitutes an undertaking on your part to pay for the said order. By clicking on “{buttonMessage}” you certify having read and accepted without reservation the general terms and conditions of the relevant seller (see link to seller in the shopping cart overview on the left, where you can also find the Seller’s cancellation and data protection policy).", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2": "Metro Markets GmbH (Metro) may send you direct marketing e-mails about Metro products or services similar to those you have purchased. You can object to this at any time and free of charge via the link provided in each electronic commercial communication.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2_WITH_LINK": "Metro Markets GmbH (Metro) may send you direct marketing e-mails about Metro products or services similar to those you have purchased. You can object to this by clicking", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2_WITH_LINK.FRANCE": "METRO may send you advertising e-mails about products or services similar to those you have purchased. You can object to this by clicking", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2_WITH_LINK.ITALY": "METRO may send you advertising e-mails about products or services similar to those you have purchased. You can object to this by clicking", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2_WITH_LINK.SPAIN": "METRO may send you advertising e-mails about products or services similar to those you have purchased. You can object to this by clicking", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_2.FRANCE": "You consent to Metro Markets GmbH (Metro Markets) sending you direct advertising by email about products or services similar to those you have purchased.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_KEY_ACCOUNTS": "By clicking on \"{buttonMessage}\" you accept our GTC, which you can find <a href=\"{GTCInfoLink}\" target=\"_blank\" rel=\"noopener noreferrer\">here</a>. Our privacy policy can be found <a href=\"{privacyPolicyLink}\" target=\"_blank\" rel=\"noopener noreferrer\">here</a>.\n<br/>\nBased on your order, we will send you direct advertising for similar products and services by e-mail. You can object to this via the link in the e-mail. There are no costs for the objection over and above the basic rate", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS_TEXT_KEY_ACCOUNTS_RESTRICTED": "By clicking on “{buttonMessage}” your order request will be forwarded to \"{parentAccountName}\" for approval. Only after their approval a binding order will be placed in the name and for the account of \"{parentAccountName}\".", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.COMBINED_EMAIL_PHONE_CONSENT": "By clicking the checkbox, you agree to be contacted by the METRO Markets GmbH (METRO) via email and by METROs gastro experts via the telephone numbers stored in your myMETRO customer account for the purpose of preparing offers and personal advice on METRO's professional products and HoReCa services. You can revoke your consent at any time by sending an e-<NAME_EMAIL>. Further information can be found in our privacy policy.", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL_PAYLATER_FRANCE.BUTTON_MESSAGE": "4X PayPal", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL_PAYLATER.BUTTON_MESSAGE": "Buy Now", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL_PAYLATER.BUTTON_MESSAGE.FRANCE": "4X PayPal", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL_PAYLATER.BUTTON_MESSAGE.ITALY": "Paga in 3 rate", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL_PAYLATER.BUTTON_MESSAGE.SPAIN": "Paga en 3 plazos", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PAYPAL.BUTTON_MESSAGE": "Buy Now", "CHECKOUT.REVIEW_PAGE.TERMS_AND_CONDITIONS.PHONE_CONSENT": "By clicking the checkbox, you agree to be contacted by the METRO Markets GmbH gastro experts via the telephone numbers stored in your myMETRO customer account for the purpose of preparing offers and personal advice on METRO's professional products and HoReCa services. You can revoke your consent at any time by sending an e-mail to <a href=\"mailto:{phoneConsentRevokeEmail}\\\" target=\"_blank\" rel=\"noopener noreferrer\"><EMAIL></a>. Further information can be found in our privacy policy.", "CHECKOUT.REVIEW_PAGE.VALIDATOR.INVALID_VAT_ID_TEXT": "Please add a valid VAT ID for 0% Tax Rate, otherwise regular Tax Rate will apply.", "CHECKOUT.REVIEW_PAGE.VALIDATOR.INVALID_VAT_ID_TITLE": "VAT ID is invalid for 0% Tax Rate", "CHECKOUT.REVIEW.A11Y.ADDITIONAL_INFORMATION": "Additional information", "CHECKOUT.REVIEW.A11Y.COMPANY_NAME": "Company name", "CHECKOUT.REVIEW.A11Y.CONSTENT_EMAILS": "I would like to receive (personalised) offers, discounts and trends from METRO Group via email and receive a 10 % voucher for my next purchase.", "CHECKOUT.REVIEW.A11Y.DISCOUNT_CODE_TOOLTIP": "Only one voucher can be used per order on the METRO marketplace. Currently, discounts only apply to products from the seller METRO.", "CHECKOUT.REVIEW.ALCOHOL_SECTION.REQUIRED": "This section is required to confirm your order.", "CHECKOUT.REVIEW.ALCOHOL.CONFIRM": "I confirm that I am 18 years or older.", "CHECKOUT.REVIEW.ALCOHOL.CONFIRM.NETHERLANDS": "I confirm that I am 18 years or older\n(another age check will be done at delivery).", "CHECKOUT.REVIEW.ERROR.CVC_DECLINED": "The CVC you provided for you credit card seems to be incorrect, please check your credit card and provide the proper number, which is usually printed on the backside of your credit card.", "CHECKOUT.REVIEW.ERROR.DECLINED_GENERIC": "We are unable to process your payment at the moment. Please contact your bank or try one of our alternative payment options. We are sorry for the inconvenience.", "CHECKOUT.REVIEW.ERROR.EXPIRED_CARD": "We are unable to process your payment at the moment. The credit card seems to be expired. Try again by using a valid credit card or try one of our alternative payment options.", "CHECKOUT.REVIEW.ERROR.INVALID_CARD_NUMBER": "We are unable to process your payment at the moment. Please check if the credit card number you provided is correct and try again.", "CHECKOUT.REVIEW.ERROR.NOT_ENOUGH_BALANCE": "We regret to inform you that there are insufficient funds on your credit card to process this transaction. Please try to use one of our alternative payment options.", "CHECKOUT.REVIEW.ERROR.PAYMENT_BANK_NOT_VERIFIED": "Your bank account information could not be verified. Please review the information that you have entered.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_3D_NOT_AUTHENTICATED.ASSISTED_SALES": "<strong>3DS authentication failed</strong> and did not charge the customer. Ask the customer if they activated 3DS with their bank (e.g two-factor authentication). If they are unsure, they should contact their bank as there is an error validating their 3DS check.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_3D_NOT_AUTHENTICATED.ASSISTED_SALES.FRANCE": "<strong>3DS authentication failed</strong> and did not charge the customer. Ask the customer if they activated 3DS on their bank card (e.g two-factor authentication via mobile phone). if yes, they should contact their bank as there seems to be an error validating their 3DS check.\n\nFor <strong>assistance</strong> please use our <strong>hotline</strong> at <strong>+33 1 70 911 919</strong> or our <strong>email</strong> at <a href=\"mailto:<EMAIL>\"><EMAIL></a>.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_3D_NOT_AUTHENTICATED.ASSISTED_SALES.SPAIN": "<strong>3DS authentication failed</strong> and did not charge the customer. Ask the customer if they activated 3DS on their bank card (e.g two-factor authentication via mobile phone). if yes, they should contact their bank as there seems to be an error validating their 3DS check.\n\nFor assistance please use our <strong>hotline</strong> at <strong>+34 91-0387956</strong> or our email at <strong><a href=\"mailto:<EMAIL>\"><EMAIL></a></strong>.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_AMOUNT_EXCEED.ASSISTED_SALES": "The <strong>credit card was declined</strong>, the balance on the card wasn’t sufficient for the purchase. To proceed, use a different card or share a payment link with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_AMOUNT_EXCEED.ASSISTED_SALES.FRANCE": "The <strong>credit card</strong> was <strong>declined</strong>, the general limit on the card wasn't sufficient for the purchase. This is not always visible on the credit card overview the bank provides to the customer. To proceed, please <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_AMOUNT_EXCEED.ASSISTED_SALES.SPAIN": "The <strong>credit card</strong> was <strong>declined</strong>, the balance on the card wasn’t sufficient for the purchase. To proceed, please <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_BILLING_ADDRESS": "Your billing address could not be verified. Please review the information that you have entered.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_CARD_NOT_SUPPORTED": "We are currently unable to process the transaction because we do not support the payment method you have chosen. We apologise for the inconvenience.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_CC_DECLINED.ASSISTED_SALES.FRANCE": "The <strong>credit card</strong> was <strong>declined</strong>, the balance on the card wasn’t sufficient for the purchase. To proceed, please <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_CC_FRAUD.ASSISTED_SALES": "The <strong>credit card</strong> was <strong>declined</strong> for the following reason - <strong>suspected</strong> <strong>fraud</strong>. To proceed, share a payment link with the customer and tell them to contact their bank regarding the payment error.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_CC_FRAUD.ASSISTED_SALES.FRANCE": "Please contact the <strong>hotline</strong> at <strong>+33 1 70 911 919</strong> or our <strong>email</strong> at <a href=\"mailto:<EMAIL>\"><EMAIL></a> so we can check the validity of this transaction. You will need to provide at least the <strong>order number</strong> or <strong>customer</strong> email used, so we can resolve the problem.\n\nIf this transaction failed due to the multiple attempts with different credit cards on the same Laptop/PC, our risk score was over the critical threshold and as a result <strong>future transactions</strong> have been <strong>blocked</strong>.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_CC_FRAUD.ASSISTED_SALES.SPAIN": "Please contact the <strong>hotline</strong> at <strong>+34 91-0387956</strong> or our <strong>email</strong> at <strong><a href=\"mailto:<EMAIL>\"><EMAIL></a></strong> so we can check the validity of this transaction. You will need to provide at least the <strong>order number</strong> or <strong>customer email</strong> used, so we can resolve the problem.\n\nIf this transaction failed due to the multiple attempts with different credit cards on the same Laptop/PC, our risk score was over the critical threshold and as a result <strong>future transactions</strong> have been <strong>blocked</strong>.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_DELIVERY_ADDRESS": "Your delivery address could not be verified. Please review the information that you have entered.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_GENERIC_MESSAGE": "We are unable to process your payment at the moment. Please change/update your address or try again later. We are sorry for the inconvenience.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_GENERIC.ASSISTED_SALES": "The <strong>payment</strong> could not be processed due to an <strong>error</strong>. Please <strong>check</strong> the <strong>payment details</strong> or <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_GENERIC.ASSISTED_SALES.FRANCE": "The payment request could not be sent due to an <strong>error</strong> from one of our <strong>payment partners</strong>. For <strong>assistance</strong> please use our <strong>hotline</strong> at <strong>+33 1 70 911 919</strong> or our <strong>email</strong> at <a href=\"mailto:<EMAIL>\"><EMAIL>.</a>", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_GENERIC.ASSISTED_SALES.SPAIN": "The payment request could not be sent due to an <strong>error</strong> from one of our <strong>payment partners</strong>. For <strong>assistance</strong>  please use our <strong>hotline</strong> at <strong>+34 91-0387956</strong> or our email at <strong><a href=\"mailto:<EMAIL>\"><EMAIL></a></strong>.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_MESSAGE": "We are unable to process your payment at the moment. Please choose another payment method or try again later. We are sorry for the inconvenience.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_ORDER_NUMBER": "Order Number:", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_REFUSED.ASSISTED_SALES": "The <strong>credit card</strong> was <strong>declined</strong>. This transaction will be reviewed by our payment team, to proceed please <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_REFUSED.ASSISTED_SALES.FRANCE": "The <strong>credit card</strong> was <strong>declined</strong> This transaction will be reviewed by our payment team, to proceed <strong>please share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_REFUSED.ASSISTED_SALES.SPAIN": "The <strong>credit card</strong> was <strong>declined</strong>. This transaction will be reviewed by our payment team, to proceed please <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_SUSPECTED_FRAUD.ASSISTED_SALES": "The <strong>credit card</strong> was <strong>declined</strong> for the following reason - <strong>suspected</strong> <strong>fraud</strong>. To proceed, share a payment link with the customer and tell them to contact their bank regarding the payment error.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_SUSPECTED_FRAUD.ASSISTED_SALES.FRANCE": "Please contact the <strong>hotline</strong> at <strong>+33 1 70 911 919</strong> or our <strong>email</strong> at <a href=\"mailto:<EMAIL>\"><EMAIL></a> so we can check the validity of this transaction. You will need to provide at least the <strong>order number</strong> or <strong>customer email used</strong>, so we can resolve the problem.", "CHECKOUT.REVIEW.ERROR.PAYMENT_FAILED_SUSPECTED_FRAUD.ASSISTED_SALES.SPAIN": "Please contact the <strong>hotline</strong> at <strong>+34 91-0387956</strong> or our <strong>email</strong> at <strong><a href=\"mailto:<EMAIL>\"><EMAIL></a></strong> so we can check the validity of this transaction. You will need to provide at least the <strong>order number</strong> or <strong>customer email</strong> used, so we can resolve the problem.", "CHECKOUT.REVIEW.ERROR.PAYMENT_TECHNICAL_ERROR": "A technical error has occurred. Please try again at a later point in time.", "CHECKOUT.REVIEW.ERROR.TRANSACTION_NOT_PERMITTED": "We are unable to process your payment at the moment. Please contact your bank or try one of our alternative payment options. We are sorry for the inconvenience.", "CHECKOUT.REVIEW.ERROR.WITHDRAWAL_AMOUNT_EXCEEDED": "We regret to inform you that we cannot proceed with this transaction as the withdrawal amount on your credit card seems to be exceeded. Please try to use one of our alternative payment options.", "CHECKOUT.REVIEW.ERROR.WITHDRAWAL_AMOUNT_EXCEEDED.ASSISTED_SALES": "The <strong>credit card</strong> was <strong>declined</strong>, the general limit on the card wasn't sufficient for the purchase. To proceed, use a <strong>different card</strong> or <strong>share a payment link</strong> with the customer.", "CHECKOUT.REVIEW.ERROR.WITHDRAWAL_AMOUNT_EXCEEDED.ASSISTED_SALES.FRANCE": "The <strong>credit card</strong> was <strong>declined</strong>, the general limit on the card wasn't sufficient for the purchase. This is not always visible on the credit card overview the bank provides to the customer. To proceed, please <strong>share a payment</strong> link with the customer.", "CHECKOUT.REVIEW.ERROR.WITHDRAWAL_AMOUNT_EXCEEDED.ASSISTED_SALES.SPAIN": "The <strong>credit card</strong> was <strong>declined</strong>, the general limit on the card wasn't sufficient for the purchase. This is not always visible on the credit card overview the bank provides to the customer. To proceed, please <strong>share a payment</strong> link with the customer.", "CHECKOUT.REVIEW.ERROR.WRONG_CVC_LENGTH": "The CVC you provided for you credit card seems to be incorrect, please check your credit card and provide the proper number, which is usually printed on the backside of your credit card.", "CHECKOUT.REVIEW.IMPERSONATE_ERROR_CONFLICT.BODY": "There was an error confirming this order as another impersonation session was active.", "CHECKOUT.REVIEW.PAYMENT.PAYPAL_INS": "PayPal", "CHECKOUT.REVIEW.PAYMENT.PAYPAL_INS.FRANCE": "PayPal Pay in 4", "CHECKOUT.REVIEW.PAYMENT.PAYPAL_INS.ITALY": "PayPal Pay in 3 instalments", "CHECKOUT.REVIEW.PAYMENT.PAYPAL_INS.SPAIN": "PayPal Pay in 3 instalments", "CHECKOUT.REVIEW.TOTAL": "Total", "CHECKOUT.SHIPPING-ADDRESS.BLOCKED.MESSAGE": "Every purchase you make on this page with the coupon code SUPPORTFORUKRAINE will be sent directly to the Tafel Deutschland e.V. Organization.", "CHECKOUT.STEP.REVIEW.TITLE": "Review Your Order", "CHECKOUT.STEPPER.ADDRESS.STEP": "Address", "CHECKOUT.STEPPER.BACK": "Back", "CHECKOUT.STEPPER.DONE.STEP": "Done", "CHECKOUT.STEPPER.NEXT": "Next", "CHECKOUT.STEPPER.ORDER.STEP": "Review", "CHECKOUT.STEPPER.PAYMENT.STEP": "Payment", "CHECKOUT.STEPPER.PREVIOUS": "Previous", "CHECKOUT.TERMS_AND_CONDITIONS_B2B_LINK": "GTC", "CHECKOUT.TERMS_AND_CONDITIONS_B2B_TEXT_1": "By placing your order you agree to the ", "CHECKOUT.TERMS_AND_CONDITIONS_B2B_TEXT_2": " by METRO or the respective seller (see shopping cart overview). There you will also find out, if you are granted a contractual revocation right. Direct advertising for similar products and services can be sent to you by email. You can object to this via the link in the email. There are no costs beyond the basic tariff for the objection.", "CHECKOUT.TERMS_AND_CONDITIONS_B2C_CANCELLATION_LINK": "declaration of cancellation", "CHECKOUT.TERMS_AND_CONDITIONS_B2C_LINK": "GTC", "CHECKOUT.TERMS_AND_CONDITIONS_B2C_TEXT_1": "By placing your order you agree to the ", "CHECKOUT.TERMS_AND_CONDITIONS_B2C_TEXT_2": " and the ", "CHECKOUT.TERMS_AND_CONDITIONS_B2C_TEXT_3": " by METRO or the respective seller (see shopping cart overview). Direct advertising for similar products and services can be sent to you by email. You can object to this via the link in the email. There are no costs beyond the basic tariff for the objection.", "CHECKOUT.THANK_YOU.HEADER": "Thank you for your order!", "CHECKOUT.THANK_YOU.IMAGE-ALTERNATIVE": "Thank you for your order!", "CHECKOUT.THANK_YOU.MESSAGE.CHECK_ORDER": "Please check your status and your ", "CHECKOUT.THANK_YOU.MESSAGE.CONFIRMATION": "Thank you for your order, a confirmation email is on its way to you.", "CHECKOUT.THANK_YOU.MESSAGE.HOME_PAGE": "Homepage", "CHECKOUT.THANK_YOU.MESSAGE.OFFERS": "To discover more interesting offers, please visit our ", "CHECKOUT.THANK_YOU.MESSAGE.ORDER_LINK": "Order Details", "CHECKOUT.THANK_YOU.MESSAGE.PASSWORDLESS_PLACEHOLDER": "Enter your password here...", "CHECKOUT.THANK_YOU.MESSAGE.PASSWORDLESS_SUBMIT_BUTTON": "Save password", "CHECKOUT.THANK_YOU.MESSAGE.PASSWORDLESS_TEXT": "Your account is created without a password. You can access your account and invoices next time with secure login link or you can set a password.", "CHECKOUT.THANK_YOU.MESSAGE.PASSWORDLESS_TITLE": "Set password for your account (Optional)", "CHECKOUT.THANK_YOU.MESSAGE.RATEPAY_NOTICE": "Payment is made via RatePay Invoice. You will receive an email with  all payment processing information after the order will be shipped.", "CHECKOUT.THANK_YOU.MESSAGE.SHOPPING": "Continue Shopping", "CHECKOUT.THANK_YOU.MESSAGE.SUBSIDIARIES_CONFIRMATION": "Thank you, your order request has been submitted and is awaiting approval.", "CHECKOUT.THANK_YOU.PASSWORDLESS_NOTICE.BUTTON": "Set Password", "CHECKOUT.THANK_YOU.PASSWORDLESS_NOTICE.HEADER": "Set a password for your account (Optional)", "CHECKOUT.THANK_YOU.PASSWORDLESS_NOTICE.MESSAGE": "Your account is created without a password. You can access your account and invoices next time with secure login link or you can set a password.", "CHECKOUT.TITLE": "Review your order", "CHECKOUT.TOTALS.TITLE.SHIPPING": "Shipping", "CHECKOUT.TOTALS.TITLE.SUBTOTAL": "Subtotal (net prices)", "CHECKOUT.TOTALS.TITLE.TAXES": "Taxes", "CHECKOUT.TOTALS.TITLE.TOTAL": "Total (VAT included)", "CHOOSE_A_REGION": "Choose a region", "CLOSE_REGION_SELECTION_OPTIONS": "Close", "COOKIE.ACTION": "OK", "COOKIE.DESCRIPTION_LINK_1": "here", "COOKIE.DESCRIPTION_LINK_2": "reject it here", "COOKIE.DESCRIPTION_PREFIX": "We use cookies, in particular for the functionality of the website, for range analysis and website optimization and for personalized advertising. You can find further information, including options for objection, ", "COOKIE.DESCRIPTION_SUFIX": ". For certain data processing and the setting of certain cookies we need your consent, this includes the display of personalized advertising, possibly also on third-party sites. By clicking on the website, on a link or on \"Ok\" you give this consent. You can revoke your consent retrospectively or ", "COOKIE.DESCRIPTION_SUFIX_NEW": ". For certain data processing and the setting of certain cookies we need your consent, this includes the display of personalized advertising, possibly also on third-party sites. By clicking on \"Ok\" you give this consent. You can revoke your consent retrospectively or ", "COOKIE.UNIFIED.BUTTON_TEXT_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "COOKIE.UNIFIED.BUTTON_TEXT_2": "Accept Cookies", "COOKIE.UNIFIED.DESCRIPTION_LINK_1": "Imprint", "COOKIE.UNIFIED.DESCRIPTION_LINK_2": "Privacy policy", "COOKIE.UNIFIED.DESCRIPTION_PREFIX": "We, METRO Deutschland GmbH and METRO Markets GmbH, use cookies and similar technologies to ensure the functionality of the website as well as its optimization, for preference management, analysis, as well as for the display and evaluation of personalized content and advertising, possibly by third-party companies and on third-party sites. By clicking on \"Accept all cookies\", you give us your consent to the use of non-essential and non-functional cookies and similar technologies. Under", "COOKIE.UNIFIED.DESCRIPTION_SUFIX": "you will receive further information and can manage the settings yourself and adjust them at any time, also by revoking your consent.", "COOKIE.UNIFIED.DESCRIPTION.GREETINGS": "Dear METRO customers,", "COOKIE.UNIFIED.DESCRIPTION.HEADER": "More data protection for your privacy", "CORE.ALL_PAGES.ASSISTED_SALES_OVERVIEW": "Assisted Sales Overview", "CORE.ALL_PAGES.ASSISTED_SALES.COMPLETED_ORDERS": "Completed Orders", "CORE.ALL_PAGES.ASSISTED_SALES.COMPLETED_ORDERS_TAB_COMING_SOON": "The “Completed Orders” tab is coming soon.", "CORE.ALL_PAGES.ASSISTED_SALES.ORDER_OVERVIEW": "Order Overview", "CORE.ALL_PAGES.ASSISTED_SALES.PAYMENT_LINKS": "Payment Links", "CORE.ALL_PAGES.EMPLOYEE.ASSISTED_SALES_OVERVIEW": "Employee Assisted Sales Overview", "CORE.ALL_PAGES.FLYOUT.INFO.TEXT": "To access account details you will have to first confirm account credentials.", "CORE.ALL_PAGES.FLYOUT.KEY_ACCOUNTS.ADMIN": "Ad<PERSON> Account", "CORE.ALL_PAGES.HEADER_CONTROLS.FAVORITES.LABEL": "Favourites Lists", "CORE.ALL_PAGES.HEADER_CONTROLS.ORDERS.LABEL": "Orders", "CORE.ALL_PAGES.HEADER_NAVIGATION.ASSORTMENT.LABEL": "Assortment", "CORE.ALL_PAGES.HEADER_NAVIGATION.BUTTON.TEXT": "Shop all", "CORE.ALL_PAGES.HEADER_NAVIGATION.INSPIRATION.LABEL": "Inspiration", "CORE.ALL_PAGES.HEADER_NAVIGATION.PROMOTIONS.LABEL": "Promotions", "CORE.ALL_PAGES.HEADER_NAVIGATION.SERVICES.LABEL": "Services", "CORE.ALL_PAGES.HEADER_SEARCH.ERROR": "Provide at least 2 symbols so we can search for better matches.", "CORE.ALL_PAGES.HEADER_SEARCH.INPUT.AI.PLACEHOLDER": "AI powered  ✨ - What do I need for opening an italian restaurant?", "CORE.ALL_PAGES.HEADER_SEARCH.INPUT.NAME": "Search", "CORE.ALL_PAGES.HEADER_SEARCH.INPUT.SUBMIT_BUTTON": "Start search", "CORE.ALL_PAGES.HEADER_SEARCH.PLACEHOLDER.TEXT": " Find products / categories", "CORE.ALL_PAGES.HEADER_TOPBUCKET.BUTTON1.TEXT": "Online Shop", "CORE.ALL_PAGES.HEADER_TOPBUCKET.BUTTON2.TEXT": "Offers & Stores", "CORE.ALL_PAGES.HEADER_TOPBUCKET.BUTTON3.TEXT": "Delivery Service", "CORE.ALL_PAGES.HEADER_TOPBUCKET.BUTTON4.TEXT": "Infos & Services", "CORE.ALL_PAGES.HEADER.SHOPPING_CART.BUTTON_LABEL": "Go to shopping cart", "CORE.ALL_PAGES.HEADER.SHOPPING_CART.QUANTITY": "Quantity", "CORE.ALL_PAGES.HEADER.SHOPPING_CART.SHIPPING_FREE": "free", "CORE.ALL_PAGES.HEADER.SHOPPING_CART.VAS_FREE": "free", "CORE.ALL_PAGES.HEADER.SHOPPING_CART.VAT": "VAT incl. {price}", "CORE.ALL_PAGES.HEADER.SUGGESTION.CATEGORY": " In ", "CORE.ALL_PAGES.HEADER.SUGGESTION.LOADING": " Fitting recommendations being loaded... ", "CORE.ALL_PAGES.META_DESCRIPTION": "Over 600.000 items ✔  Large volume discounts ✔  Order without METRO card ➤ Discover now!", "CORE.ALL_PAGES.OTP_LOGIN.BODY_TEXT.OR": "or", "CORE.ALL_PAGES.OTP_LOGIN.BUTTON_TEXT.CODE_RESENT": "Resent", "CORE.ALL_PAGES.OTP_LOGIN.BUTTON_TEXT.CONTINUE": "Continue", "CORE.ALL_PAGES.OTP_LOGIN.BUTTON_TEXT.LOGIN": "<PERSON><PERSON>", "CORE.ALL_PAGES.OTP_LOGIN.BUTTON_TEXT.LOGIN_USING_A_PASSWORD": "Login using a password", "CORE.ALL_PAGES.OTP_LOGIN.BUTTON_TEXT.RESEND_CODE": "Resend code", "CORE.ALL_PAGES.OTP_LOGIN.IDAM_REDIRECT_DIALOG.BODY_TEXT.REDIRECTING_ONE_MOMENT": "Redirecting, one moment", "CORE.ALL_PAGES.OTP_LOGIN.LOADING_DIALOG.BODY_TEXT.LOGGING_IN_INFO": "Logging in, one moment", "CORE.ALL_PAGES.OTP_LOGIN.MAGIC_LINK_DIALOG.BODY_TEXT.LINK_INFO": "It may take a minute to receive the login link, we have sent an email to", "CORE.ALL_PAGES.OTP_LOGIN.MAGIC_LINK_DIALOG.BUTTON_TEXT.RESEND_MAGIC_LINK": "Resend the secure login link", "CORE.ALL_PAGES.OTP_LOGIN.MAGIC_LINK_DIALOG.HEADER.TEXT": "Please check your inbox", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.CODE_EXPIRY": "Code Expires in", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.EMAIL_NOT_SEND_TEXT": "Didn’t receive an email?", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.EMAIL.SEND_AGAIN": "Send again", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.INVALID_CODE_GENERATION": "There was an issue when using a code to login, we will redirect you to an alternative login method.", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.LOGIN_EMAIL_SEND_INFO": "Log in using the code we sent to", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.BODY_TEXT.VERIFICATION_CODE": "Enter verification code", "CORE.ALL_PAGES.OTP_LOGIN.OTP_DIALOG.HEADER.TEXT": "Enter the code", "CORE.ALL_PAGES.OTP_LOGIN.RESEND_OTP_DIALOG.BODY_TEXT.SUBMIT_CUSTOMER_SUPPORT_REQUEST": "Submit a customer support request", "CORE.ALL_PAGES.OTP_LOGIN.RESEND_OTP_DIALOG.BODY_TEXT.TEMPORARY_CODE_EXPIRED": "Your temporary code has expired.", "CORE.ALL_PAGES.OTP_LOGIN.RESEND_OTP_DIALOG.BODY_TEXT.UNABLE_TO_ACCESS_ACCOUNT": "Unable to access your account?", "CORE.ALL_PAGES.OTP_LOGIN.RESEND_OTP_DIALOG.HEADER.TEXT": "Login code expired", "CORE.ALL_PAGES.OTP_LOGIN.RETRY_OTP_DIALOG.HEADER.TEXT": "<PERSON><PERSON> entering the code", "CORE.ALL_PAGES.OTP_LOGIN.VALIDATE_EMAIL_DIALOG.BODY_TEXT.ENTER_EMAIL_ADDRESS": "Please enter your <b>email address</b> to <b>log in</b> or <b>register</b>", "CORE.ALL_PAGES.OTP_LOGIN.VALIDATE_EMAIL_DIALOG.EMAIL.TITLE": "Email", "CORE.ALL_PAGES.OTP_LOGIN.VALIDATE_EMAIL_DIALOG.HEADER.TEXT": "Login or Register", "CORE.ALL_PAGES.PHOTO_SEARCH.CLOSE_MODAL": "Close modal", "CORE.ALL_PAGES.PHOTO_SEARCH.MODAL.CTA": "Upload a photo", "CORE.ALL_PAGES.PHOTO_SEARCH.MODAL.DESCRIPTION": "Find what you are looking for on METRO by using an image search", "CORE.ALL_PAGES.PHOTO_SEARCH.MODAL.DRAG_DROP": "Drag an image here", "CORE.ALL_PAGES.PHOTO_SEARCH.MODAL.OR": "or", "CORE.ALL_PAGES.PHOTO_SEARCH.MODAL.TITLE": "Search by image", "CORE.ALL_PAGES.PHOTO_SEARCH.RETAKE_PHOTO": "Re take photo", "CORE.ALL_PAGES.PHOTO_SEARCH.SAVE_PHOTO": "Save picture", "CORE.ALL_PAGES.PHOTO_SEARCH.TAKE_PHOTO": "Take photo", "CORE.ALL_PAGES.PHOTO_SEARCH.TOGGLE_CAMERA": "Switch camera", "CORE.ALL_PAGES.PRICE_SWITCH.BUSINESS.LABEL": "Business", "CORE.ALL_PAGES.PRICE_SWITCH.BUSINESS.VAT_INFO.LABEL": "Prices excl. VAT", "CORE.ALL_PAGES.PRICE_SWITCH.CLOSE.ARIA_LABEL": "Close", "CORE.ALL_PAGES.PRICE_SWITCH.CONSUMER.LABEL": "Private", "CORE.ALL_PAGES.PRICE_SWITCH.CONSUMER.VAT_INFO.LABEL": "Prices incl. VAT", "CORE.ALL_PAGES.PRICE_SWITCH.EXPLANATION_MESSAGE_1": "By default, we show prices excluding VAT.", "CORE.ALL_PAGES.PRICE_SWITCH.EXPLANATION_MESSAGE_2": "You can switch to Private Customer prices (including VAT) at any moment.", "CORE.ALL_PAGES.PRICE_SWITCH.EXPLANATION.CLOSE.ARIA_LABEL": "Close", "CORE.ALL_PAGES.PRICE_SWITCH.MOBILE.EXPLANATION_MESSAGE_1": "By default we show prices excluding VAT.", "CORE.ALL_PAGES.PRICE_SWITCH.MOBILE.EXPLANATION_MESSAGE_2": "Switch to Private Customer prices (including VAT).", "CORE.ALL_PAGES.PRICE_TYPE.BUSINESS.LABEL": "Business", "CORE.ALL_PAGES.PRICE_TYPE.CONFIRMATION.ACCEPT.LABEL": "Got it", "CORE.ALL_PAGES.PRICE_TYPE.CONFIRMATION.CONUMER_BUTTON.LABEL": "Show Consumer Prices", "CORE.ALL_PAGES.PRICE_TYPE.CONFIRMATION.MESSAGE": "By default we show business prices with separately stated VAT. You can switch anytime to consumer prices including VAT.", "CORE.ALL_PAGES.PRICE_TYPE.CONFIRMATION.TITLE": "You are seeing Business Prices", "CORE.ALL_PAGES.PRICE_TYPE.CONSUMER.LABEL": "Consumer", "CORE.ALL_PAGES.PRICE_TYPE.LOGGEDIN_BUSINESS.LABEL": "Business prices", "CORE.ALL_PAGES.PRICE_TYPE.LOGGEDIN_CONSUMER.LABEL": "Consumer prices", "CORE.ALL_PAGES.PRICE_TYPE.VAT.LABEL": "({type, select, consumer{with} other{without}} VAT)", "CORE.ALL_PAGES.PRICE.LABEL": "Prices", "CORE.ALL_PAGES.PRODUCT_PROMO.LABEL": "Deal", "CORE.ALL_PAGES.PRODUCT_PROMO.LABEL.VOLUME_PRICING": "Buy {volumePriceQuantity} and save {volumePricePercentage}%", "CORE.ALL_PAGES.UNDER_MAINTENANCE_LABEL": "Under maintenance", "CORE.ALL_PAGES.USER_BOX.ACCOUNT_INFO.LABEL": "Account information", "CORE.ALL_PAGES.USER_BOX.ADDRESS_BOOK.LABEL": "Address book", "CORE.ALL_PAGES.USER_BOX.CART.LABEL": "<PERSON><PERSON>", "CORE.ALL_PAGES.USER_BOX.HEADER.LABEL": "My Account", "CORE.ALL_PAGES.USER_BOX.LOG_OUT.LABEL": "Log out", "CORE.ALL_PAGES.USER_BOX.LOGIN.LABEL": "<PERSON><PERSON>", "CORE.ALL_PAGES.USER_BOX.MY_ACCOUNT.LABEL": "My Account", "CORE.ALL_PAGES.USER_BOX.ORDERS.LABEL": "Orders", "CORE.ALL_PAGES.USER_BOX.SAVED_LISTS.LABEL": "Saved lists", "CORE.ALL_PAGES.USER_BOX.SIGNUP.LABEL": "Sign Up", "CORE.TERMS_CONFIRMATION_DIALOG.CHECKBOX.LABEL": "I confirm that I'm an entrepreneur according to § 14 BGB", "CORE.TERMS_CONFIRMATION_DIALOG.DATA_PROTECTION": "Privacy Policy", "CORE.TERMS_CONFIRMATION_DIALOG.MESSAGE_1": "By clicking Accept, I accept the ", "CORE.TERMS_CONFIRMATION_DIALOG.MESSAGE_2": "The applicable privacy policy can be found here: ", "CORE.TERMS_CONFIRMATION_DIALOG.TERMS_AND_CONDITIONS": "Terms of use and purchase", "CORE.TERMS_CONFIRMATION_DIALOG.TITLE": "Welcome to METRO Marketplace!", "CUSTOM_CATEGORY_HUB.LAST_DEALS_PRODUCTS": "Latest Deals", "CUSTOM_CATEGORY_HUB.MOST_POPULAR_PRODUCTS": "Popular Products", "CUSTOM_CATEGORY_HUB.SEO_CONTENT.FAQ": "FAQ", "CUSTOM_HUB.LAST_DEALS_PRODUCTS": "Latest Deals", "CUSTOM_HUB.MOST_POPULAR_PRODUCTS": "Popular Products", "CUSTOM_HUB.SEO_CONTENT.FAQ": "FAQ", "EMAIL.EDIT_LINK": "Edit", "ERROR.401_PAGE.BUTTON_LABEL": "Contact us", "ERROR.401_PAGE.MESSAGE": "Unfortunately, you are not allowed to access this page. Please make sure you are logged in with the correct account and try again. If you believe this is an error, please contact our customer support.", "ERROR.401_PAGE.TITLE": "Access Denied", "ERROR.403_PAGE.BUTTON_LABEL": "Contact Support", "ERROR.403_PAGE.MESSAGE": "Your account is blocked on Metro OnlineShop, please contact the support centre for more details. You can still use other Metro services. Sorry for the inconvenience.", "ERROR.403_PAGE.OBLIGATION_BUTTON_LABEL": "Contact Support", "ERROR.403_PAGE.OBLIGATION_MESSAGE": "Looks like your company is obliged to do split payments. The METRO Onlineshop cannot accept payments from companies that are obliged to do spit payments, unfortunately.", "ERROR.403_PAGE.OBLIGATION_TITLE": "The METRO Onlineshop does not support split payments", "ERROR.403_PAGE.TITLE": "Your account is blocked", "ERROR.404_PAGE.BUTTON_LABEL": "Go To Home Page", "ERROR.404_PAGE.MESSAGE": "If you want to start over, go to the homepage or start searching.", "ERROR.404_PAGE.TITLE": "Sorry, we couldn't find this page.", "ERROR.500_PAGE_BUTTON_LABEL": "Refresh page", "ERROR.500_PAGE_MESSAGE": "Try to refresh this page, if it does not work please, try again after waiting for a little bit. If you need immediate help please contact our support.", "ERROR.500_PAGE.TITLE": "Looks like we're having some server issues.", "FAKE.DOOR.TEST.CART.CHECKOUT.PAYPAL": "Checkout With PayPal", "FAKE.DOOR.TEST.CART.GUEST.CHECKOUT": "Guest Checkout", "FILTERS.BRANDS.MODAL.APPLY": "Apply Filters", "FILTERS.CATEGORIES_FACETS.ARROW_LEFT.ARIA_LABEL": "Scroll category filters to left.", "FILTERS.CATEGORIES_FACETS.ARROW_RIGHT.ARIA_LABEL": "Scroll category filters to right.", "FILTERS.CATEGORIES_LIST.LINK.TEXT.SHOW_MORE": "Show all", "FILTERS.CATEGORY_CHILDREN_LIST.LINK.TEXT.SHOW_MORE": "Show all", "FILTERS.FILTER_TAGS.CLEAR_ALL_TAG.TEXT": "Clear All", "FILTERS.FILTER_TAGS.CLEAR_TAG.ARIA_LABEL": "Clear {filter_name} filter", "FILTERS.FILTER_TAGS.FILTER_RANGE.DIAMETER": "Diameter", "FILTERS.FILTER_TAGS.FILTER_RANGE.HEIGHT": "Height", "FILTERS.FILTER_TAGS.FILTER_RANGE.LENGTH_DEPTH": "Length/Depth", "FILTERS.FILTER_TAGS.FILTER_RANGE.UNIT.CM": "cm", "FILTERS.FILTER_TAGS.FILTER_RANGE.WIDTH": "<PERSON><PERSON><PERSON>", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT": "Min {min} € - Max {max} €", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT_REACT": "Min {min} - <PERSON> {max}", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT.MAX": "Max {price}", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT.MAX.LABEL": "Maximum price filter number input", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT.MIN": "Min {price}", "FILTERS.FILTER_TAGS.PRICE_RANGE_TAG.TEXT.MIN.LABEL": "Minimum price filter number input", "FILTERS.FILTER_TAGS.PROMOTION_TAG.HOTDISCOUNT": "More than {percentage}% off", "FILTERS.FILTER_TAGS.PROMTION_TAG.ON_SALE": "On Sale", "FILTERS.FILTER_TAGS.VOLUME_PRICE": "Volume price", "FILTERS.FILTER.PRICE_RANGE.TEXT": "{min}{currency} - {max}{currency}", "FILTERS.MODAL.APPLY": "Apply Filters", "FILTERS.MODAL.BRAND_SEARCH.PLACEHOLDER": "Search brand", "FILTERS.MODAL.SEARCH.DONE": "Done", "FILTERS.MODAL.SEARCH.NOT_FOUND.TEXT": "Please try again with a different keyword or check your spelling.", "FILTERS.MODAL.SEARCH.NOT_FOUND.TITLE": "We're sorry, but it seems that there are no results matching your search.", "FILTERS.MODAL.SELLER_SEARCH.PLACEHOLDER": "Search seller", "FOOTER.ALL_PAGES.B2C.PHONE_NUMBER.ARIA_LABEL": "Call {b2c} for online shop customer service", "FOOTER.ALL_PAGES.COPYRIGHT.LABEL": "METRO Markets GmbH", "FOOTER.ALL_PAGES.HELP.CALL.LABEL": "or call", "FOOTER.ALL_PAGES.HELP.CHAT.LINK": "Chat now", "FOOTER.ALL_PAGES.HELP.LABEL": "Need help?", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.DISCOUNT_CODE.LABEL": "Discount code conditions", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.OFFERS.LABEL": "Current METRO offers", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.ORDER_INFO.LABEL": "Order information", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.RETURN_POLICY.LABEL": "Return policy", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.SHIPPING_INFO.LABEL": "Shipping info", "FOOTER.ALL_PAGES.NAVIGATION.BUYER.SHIPPING_INFO.TOOLTIP": "Please note that shipping is currently only possible within Germany.", "FOOTER.ALL_PAGES.NAVIGATION.CO-BROWSING": "Co-Browsing", "FOOTER.ALL_PAGES.NAVIGATION.CO-BROWSING_TOOLTIP": "Here you can start co-browsing safely with the customer support agent to resolve your issue.", "FOOTER.ALL_PAGES.NAVIGATION.CONTACTS.BUYER_HELP_CENTER.LABEL": "Help Center", "FOOTER.ALL_PAGES.NAVIGATION.CONTACTS.CONTACT_US.LABEL": "Contact us", "FOOTER.ALL_PAGES.NAVIGATION.CONTACTS.HELP_CENTER.LABEL": "Help Center", "FOOTER.ALL_PAGES.NAVIGATION.CONTACTS.LABEL": "Customer service - Onlineshop", "FOOTER.ALL_PAGES.NAVIGATION.FAQ.LABEL": "FAQ", "FOOTER.ALL_PAGES.NAVIGATION.FOR_BUYERS.LABEL": "For Buyers", "FOOTER.ALL_PAGES.NAVIGATION.FOR_SELLERS.LABEL": "For Sellers", "FOOTER.ALL_PAGES.NAVIGATION.KUNDENSERVICE.TOOLTIP": "In case of any questions about METRO stores, METRO cards or other METRO services (not Onlineshop related), please contact our colleagues at <a target='_blank' href='https://www.metro.de'>https://www.metro.de</a> (s. footer)", "FOOTER.ALL_PAGES.NAVIGATION.MARKET.ABOUT_US.LABEL": "About us", "FOOTER.ALL_PAGES.NAVIGATION.MARKET.CAREERS.LABEL": "Careers", "FOOTER.ALL_PAGES.NAVIGATION.METRO.LABEL": "METRO Markets", "FOOTER.ALL_PAGES.NAVIGATION.PAYMENTS_METHODS.LABEL": "Payment methods", "FOOTER.ALL_PAGES.NAVIGATION.SELLER.START_SELLING.LABEL": "Start selling", "FOOTER.ALL_PAGES.NAVIGATION.TELESALES.TITLE": "Expert advice for HoReCa professionals", "FOOTER.ALL_PAGES.TELESALES.PHONE_NUMBER.ARIA_LABEL": "Call {telesales} for expert advice for business customers", "FOOTER.ALL_PAGES.VAT_NUMBER.LABEL": "VAT number", "GENERIC_ALERT_ISSUE_ID": "Issue ID", "GLOBAL.A11Y.CLOSE_ALERT": "Close alert", "GLOBAL.A11Y.SKIP_TO_MAIN_CONTENT": "Main content", "GLOBAL.A11Y.SKIP_TO_MENU_LABEL": "Quick navigation shortcuts", "GLOBAL.A11Y.SKIP_TO_MENU_TITLE": "Skip to:", "GLOBAL.A11Y.SKIP_TO_TOTALS": "Skip to totals", "HAZARD_LABELS.GHS01": "Explosive", "HAZARD_LABELS.GHS02": "Flammable", "HAZARD_LABELS.GHS03": "Oxidizing", "HAZARD_LABELS.GHS04": "Compressed Gas", "HAZARD_LABELS.GHS05": "Corrosive", "HAZARD_LABELS.GHS06": "Toxic", "HAZARD_LABELS.GHS07": "Harmful", "HAZARD_LABELS.GHS08": "Health Hazard", "HAZARD_LABELS.GHS09": "Environmental Hazard", "HEADER.ALL_PAGES.HEADER_TOPLINE.CORPORATE_SITE.LINK": "METRO corporate site", "HEADER.ALL_PAGES.HEADER_TOPLINE.SELLERS.LINK": "For Sellers", "HEADER.ALL_PAGES.HEADER.BUY_AGAIN_BUTTON": "Buy again", "HEADER.ALL_PAGES.HEADER.CATEGORIES.LABEL": "Categories", "HEADER.ALL_PAGES.HEADER.CATEGORIES.SHOW_ALL": "All in", "HEADER.ALL_PAGES.HEADER.CATEGORIES.SHOW_ALL_PRODUCTS": "All Products in", "HEADER.ALL_PAGES.HEADER.COMBINED_LOGIN_SIGN_UP.BUTTON": "Sign Up/Login", "HEADER.ALL_PAGES.HEADER.LOGO.ALT.TEXT": "Browse to", "HEADER.ALL_PAGES.HEADER.MENU": "<PERSON><PERSON>", "HEADER.ALL_PAGES.HEADER.SUGGESTIONS.SEARCH_BY_PHOTO.NEW_BADGE": "New!", "HEADER.ALL_PAGES.HEADER.SUGGESTIONS.SEARCH_BY_PHOTO.TEXT": "Search by image", "HEADER.ALL_PAGES.HEADER.SUGGESTIONS.TO_PROMO": "Matching results on <strong>Sale</strong>", "HEADER.ALL_PAGES.HEADER.TOP_CATEGORIES": "Top Categories", "HEADER.ALL_PAGES.YOUR_ASSORTMENT_BUTTON": "Your assortment", "HEADER.NESTED_CATEGORIES.SHOW": "Show", "HEADER.SIMPLIDIED.CALL.LABEL": "or call", "HEADER.SIMPLIFIED.CALL_MIN.LABEL": "Call", "HEADER.SIMPLIFIED.CALL.LABEL": "or call", "HEADER.SIMPLIFIED.GO_TO.LABEL": "Go to", "HEADER.SIMPLIFIED.GO_TO.LABEL.FRANCE": "Contact our", "HEADER.SIMPLIFIED.HELP_CENTER.LABEL": "Help Center", "HEADER.SIMPLIFIED.HELP_CENTER.LABEL.FRANCE": "customer service", "HEADER.SIMPLIFIED.NEED_HELP_MIN.LABEL": "Need help?", "HEADER.SIMPLIFIED.NEED_HELP_MIN.LABEL.FRANCE": "Need help?", "HEADER.SIMPLIFIED.NEED_HELP.LABEL": "Need help? Go to", "HEADER.SIMPLIFIED.NEED_HELP.LABEL.FRANCE": "Need help? Contact our", "HOME.HOME_PAGE.BENEFITS.PAY_LESS_DESCRIPTION": "Get cost advantages with larger purchase quantities.", "HOME.HOME_PAGE.BENEFITS.PAY_LESS_TITLE": "Pay less with volume pricing", "HOME.HOME_PAGE.BENEFITS.SELLERS_LINK_LABEL": "Start selling", "HOME.HOME_PAGE.BENEFITS.TRUSTABLE_SELLERS_DESCRIPTION": "Our sellers are selected according to strict guidelines.", "HOME.HOME_PAGE.BENEFITS.TRUSTABLE_SELLERS_TITLE": "100% Trustable sellers", "HOME.HOME_PAGE.HEADER.CATEGORIES.ALL_OFFERS": "All offers", "HOME.HOME_PAGE.NEWSLETTER_BUBBLE.SUBTITLE": "voucher", "HOME.HOME_PAGE.NEWSLETTER_BUBBLE.SUBTITLE.PERCENTAGE": "voucher", "HOME.HOME_PAGE.NEWSLETTER_BUBBLE.TITLE": "Get a", "HOME.HOME_PAGE.NEWSLETTER_VOUCHER.HEADER": "Sign up now and get a 10 % voucher!", "HOME.HOME_PAGE.NEWSLETTER-LANDING.BODY": "From now on, we will inform you regularly about new offers, exclusive coupon & discount promotions, as well as tips & trends from the gastronomy industry.", "HOME.HOME_PAGE.NEWSLETTER-LANDING.BUTTON": "Back to shopping", "HOME.HOME_PAGE.NEWSLETTER-LANDING.HEADER": "Mission complete!", "HOME.HOME_PAGE.NEWSLETTER-LANDING.SUBHEADER": "Now you are registered to receive the latest news of the METRO Online Marketplace via e-mail.", "HOME.HOME_PAGE.NEWSLETTER.BUTTONTEXT": "Sign up", "HOME.HOME_PAGE.NEWSLETTER.CONFIRMATION_HEADER": "Almost there!", "HOME.HOME_PAGE.NEWSLETTER.CONFIRMATION_MESSAGE": "Please check your inbox. We have sent you an email to confirm your email address", "HOME.HOME_PAGE.NEWSLETTER.CONFIRMATION_SUBHEADER": "Please confirm your registration.", "HOME.HOME_PAGE.NEWSLETTER.EMAIL_PLACEHOLDER": "E-Mail-Address", "HOME.HOME_PAGE.NEWSLETTER.EMAIL_RESEND_TEXT": "Didn’t receive an email? Send again", "HOME.HOME_PAGE.NEWSLETTER.LEGALTEXT": "I confirm that I would like to be informed by METRO Markets GmbH and other companies of the METRO Group about - also personalized - offers, discounts and trends by e-mail. Unsubscription is possible at any time free of charge using the unsubscribe link at the end of each e-mail. Information on the use of your data can be found in our", "HOME.HOME_PAGE.NEWSLETTER.PRIVACY_POLICY": "privacy policy", "HOME.HOME_PAGE.NEWSLETTER.PROMO_LINK_TEXT": "conditions of the discount code", "HOME.HOME_PAGE.NEWSLETTER.PROMO_TEXT": "*More information about the ", "HOME.HOME_PAGE.NEWSLETTER.SUBHEADER": "New offers, exclusive voucher & discount promotions, trends in gastronomy and more.", "HOME.HOME_PAGE.PROMO_BANNER.DISCOUNT_LABEL": "Save up to", "HOME.HOME_PAGE.PROMO_BANNER.DISCOUNT_PRODUCT_TYPE": "for all furniture", "HOME.HOME_PAGE.PROMO_BANNER.DISCOUNT_VALUE": "25%", "HOME.HOME_PAGE.PROMO_BANNER.LINK_LABEL": "Save now", "HOME.HOME_PAGE.PROMO_CATEGORIES.ALL_PROMO_CATEGORIES_TITLE": "See All Offers", "HOME.HOME_PAGE.PROMO_CATEGORIES.CHRISTMAS_NAV_ITEM": "Christmas", "HOME.HOME_PAGE.PROMO_CATEGORIES.OUTDOOR_LABEL": "BBQ & Outdoor", "HOME.HOME_PAGE.PROMO_CATEGORIES.PARENT_CATEGORY_TITLE": "Offers & inspiration", "HOME.HOME_PAGE.PROMO_TITLE": "Offers", "HOME.HOME_PAGE.SELLER_BANNER_TWO.LINK_LABEL": "Discover brochures", "HOME.HOME_PAGE.SELLER_BANNER_TWO.LINK_LABEL_UKRAINE": "Discover brochures", "HOME.HOME_PAGE.SELLER_BANNER_TWO.SUB_TITLE": "¹An offer from METRO Deutschland GmbH", "HOME.HOME_PAGE.SELLER_BANNER_TWO.SUB_TITLE_TWO_UKRAINE": "¹An offer from METRO Deutschland GmbH", "HOME.HOME_PAGE.SELLER_BANNER_TWO.SUB_TITLE_UKRAINE": "¹An offer from METRO Deutschland GmbH", "HOME.HOME_PAGE.SELLER_BANNER_TWO.TITLE": "Benefit from top offers & special offers at your METRO¹", "HOME.HOME_PAGE.SELLER_BANNER.LINK_LABEL": "Register as a seller", "HOME.HOME_PAGE.SELLER_BANNER.TITLE": "Start selling product on the METRO Marketplace in just a few steps", "HOME.HOME_PAGE.SPONSORED.ABOUT_ADVERTISER.TITLE.TEXT": "About the advertiser", "HOME.HOME_PAGE.SPONSORED.ADVERTISER_NAME.TEXT": "Name of the advertiser:", "HOME.HOME_PAGE.SPONSORED.ADVERTISER_PAYER_NAME.TEXT": "Who paid for this ad:", "HOME.HOME_PAGE.SPONSORED.WHY_I_SEE_THIS_AD.ANSWER.TEXT": "You are seeing this ad because of a relevant search query or because of the content and products you are viewing on this page.", "HOME.HOME_PAGE.SPONSORED.WHY_I_SEE_THIS_AD.QUESTION.TEXT": "Why am I seeing this ad?", "HOME.HOME_PAGE.TOP_BANNER.METROCARD.DESCRIPTION": "We got it! Simply pay by invoice or credit card!", "HOME.HOME_PAGE.TOP_BANNER.METROCARD.TITLE": "Safe and fast (payment) processing?", "HOME.HOME_PAGE.TOP_BANNER.PLACEHOLDER.GREETING": "Welcome to the new marketplace for", "HOME.HOME_PAGE.TOP_BANNER.PLACEHOLDER.TARGET": "hospitality businesses!", "HOME.HOME_PAGE.TOP_CATEGORIES.CATEGORY_TITLE_1": "Catering Equipment", "HOME.HOME_PAGE.TOP_CATEGORIES.CATEGORY_TITLE_2": "Disposables", "HOME.HOME_PAGE.TOP_CATEGORIES.CATEGORY_TITLE_3": "Catering accessories", "HOME.HOME_PAGE.TOP_CATEGORIES.CATEGORY_TITLE_4": "Hotel & Front house", "HOME.HOME_PAGE.TOP_CATEGORIES.CONTAINER_SUB_TITLE": "Top", "HOME.HOME_PAGE.TOP_CATEGORIES.CONTAINER_TITLE": "Categories", "HOME.HOME_PAGE.TOP_PRODUCTS.EXPLORE_LABEL": "Explore {mobile, select, true{} other{All Products}}", "HOME.HOME_PAGE.TOP_PRODUCTS.TOP_LABEL": "Top in", "HOME.HOME_PAGE.TOP_PRODUCTS.TOP_SALES": "Top sales in ", "HOME.HOME_PAGE.TOP_PRODUCTS.TOP_SALES_PREFIX": "Top", "HOME.HOME_PAGE.TOP_PRODUCTS.TOP_SALES_SUFFIX": "products", "HOME.HOME_PAGE.TOP_PROMOS.PROMO_BUTTON": "Explore this offer", "HOME.HOME_PAGE.TOP_PROMOS.PROMO1_PRICE_LABEL": "MSRP", "HOME.HOME_PAGE.TOP_PROMOS.PROMO1_SALE": "Sales", "HOME.HOME_PAGE.TOP_PROMOS.PROMO1_TOP": "Top", "HOME.HOME_PAGE.TOP_PROMOS.PROMO2_DESCRIPTION": "Get ready for awesome Christmas discounts", "HOME.HOME_PAGE.TOP_PROMOS.PROMO2_PRICE_LABEL": "statt bisher", "HOME.HOME_PAGE.TOP_PROMOS.PROMO2_TITLE": "Christmas", "LANDING_PAGE.CURRENT_CAMPAIGN.TITLE": "Current Campaigns", "MESSAGE_CENTER_NO_MESSAGE_BODY": "Your messages will be displayed here.", "MESSAGE_CENTER_NO_MESSAGE_TITLE": "You have no messages, yet.", "MESSAGE_CENTER_PAGE_TITLE": "Messages", "NEWSLETTER_V2_VOUCHER.DISCOUNT": "10% voucher!*", "NEWSLETTER_V2_VOUCHER.TITLE": "Receive the latest deals and more. Sign up and get a", "ORDER_HISTORY.ORDER_LINE.BUY_AGAIN": "Buy it again", "ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER": "Contact seller", "ORDER_HISTORY.ORDER_LINE.OTHER_ENQUIRY": "Other enquiry", "ORDER_HISTORY.ORDER_LINE.OTHER_ENQUIRY.EMAIL_BODY": " ", "ORDER_HISTORY.ORDER_LINE.OTHER_ENQUIRY.EMAIL_SUBJECT": "Question about the order ORDER_NUMBER", "ORDER_HISTORY.ORDER_LINE.REQUEST_DELIVERY_TIME": "Request delivery time", "ORDER_HISTORY.ORDER_LINE.REQUEST_DELIVERY_TIME.EMAIL_BODY": "Dear seller, %0D%0Dplease provide me with the tracking code for the order ORDER_NUMBER. %0D%0DThank you", "ORDER_HISTORY.ORDER_LINE.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT": "Request code for shipment tracking for order ORDER_NUMBER", "ORDER_HISTORY.ORDER_LINE.VAS.CONTACT_SELLER": "Contact customer service", "ORDERS_HISTORY_NAV_TITLE": "Orders", "PAGE.AUTH.ADD_BUSINESS_DETAILS.METRO.TNC": "By clicking “Register” you confirm the accuracy of your information and confirm that you are authorized to register the company as a legal entity.", "PAGE.AUTH.PASSWORDLESS_SIGNUP.ADD_BUSINESS_DETAILS.METRO.TNC": "By clicking “Submit” you ensure that your details are correct and confirm that you are authorised to legally register the company.", "PAGES.ACCOUNT.ADDRESS_BOOK.ADD_ADDRESS": "Add to My Address Book", "PAGES.ACCOUNT.ADDRESS_BOOK.ADD_NEW_ADDRESS": "Add New Address", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_ADDED_MESSAGE": "The address has been added", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_DELETE_DISABLED.ALERT.MESSAGE": "At the moment you cannot delete your account information due to maintenance reasons. Sorry for the inconvenience! Our service will be available again soon.", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_REMOVE_CONFIRMATION_TITLE": "Are you sure you want to remove this address?", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_REMOVED_MESSAGE": "The address has been removed", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_SAVED_MESSAGE": "The address has been saved", "PAGES.ACCOUNT.ADDRESS_BOOK.ADDRESS_WITHOUT_CHANGES_MESSAGE": "You don’t have any updates", "PAGES.ACCOUNT.ADDRESS_BOOK.CREATE_ADDRESS.TITLE": "Add New Address", "PAGES.ACCOUNT.ADDRESS_BOOK.DEFAULT_BILLING_LABEL": "Default billing", "PAGES.ACCOUNT.ADDRESS_BOOK.DEFAULT_SHIPPING_LABEL": "Default shipping", "PAGES.ACCOUNT.ADDRESS_BOOK.EDIT_ADDRESS.TITLE": "Change Address", "PAGES.ACCOUNT.ADDRESS_BOOK.REMOVE_ADDRESS": "Remove Address", "PAGES.ACCOUNT.ADDRESS_BOOK.TITLE": "Address Book", "PAGES.ACCOUNT.AFTER_CHANGE_EMAIL.ELEMENTS.MESSAGE": "The validation email has been sent to the provided email address. You will need to confirm your new email address to change your credentials. Your account will be connected to the current email address until you validate the new one.", "PAGES.ACCOUNT.AFTER_CHANGE_EMAIL.ELEMENTS.RETURN_TO_ACCOUNT": "Return to My account", "PAGES.ACCOUNT.AFTER_CHANGE_EMAIL.TITLE": "The validation email has been sent!", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.APPROVE_ACTION.TEXT": "Approved. The branch has been notified.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.APPROVE.TEXT": "Approve", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.BILLING_ADDRESS.TEXT": "Billing Address", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.KVK.TEXT": "KVK", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.LEGAL.TEXT": "By clicking on “Approve” you are placing a binding order in your name and on your account for the content of the order request. Our <a href=\"{gtcLink}\" className=\"text-info-main\"> GTC </a> and our <a href=\"#\" className=\"text-info-main underline\" target=\"_blank\"> Privacy Policy </a> apply.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDER.TEXT": "Order", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDERLINE_QUANTITY.TEXT": "Quantity", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDERLINE_VAT_INCL.TEXT": "(incl. VAT)", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.REJECT_ACTION.TEXT": "Rejected. The branch has been notified.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.REJECT.TEXT": "Reject", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.SHIPPING_ADDRESS.TEXT": "Shipping Address", "PAGES.ACCOUNT.APPROVE_ORDERS.EMPTY_PAGE.MESSAGE": "All clear! When you have new orders to approve, they will appear here.", "PAGES.ACCOUNT.APPROVE_ORDERS.EMPTY_PAGE.TITLE": "No orders to approve", "PAGES.ACCOUNT.APPROVE_ORDERS.LISTING.HEADER": "Approvals Overview", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.MESSAGE": "Orders Approval is currently unavailable for your account. Please contact your manager to enable this feature.", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.PAGE_TITLE": "Approve Orders", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.TITLE": "Feature disabled", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.BRANCH_NAME.TEXT": "Branch", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.CREATED_AT.TEXT": "Order Date", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.DETAILS_LINK.TEXT": "See details", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.KVK_NUMBER.TEXT": "KVK Number", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.ORDER_NUMBER.TEXT": "Order Number", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.ORDER_TOTAL.TEXT": "Order Value", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.REQUESTED_BY.TEXT": "Requested By", "PAGES.ACCOUNT.BUY_AGAIN.EMPTY_PAGE_BUTTON_TEXT": "Continue shopping", "PAGES.ACCOUNT.BUY_AGAIN.EMPTY_PAGE_DESCRIPTION": "Looks like you haven’t placed an order yet. Once you do, we’ll suggest products for buying again.", "PAGES.ACCOUNT.BUY_AGAIN.EMPTY_PAGE_TITLE": "There are no suggestions yet", "PAGES.ACCOUNT.BUY_AGAIN.LOAD_MORE_BUTTON": "Show more products", "PAGES.ACCOUNT.BUY_AGAIN.NEW_LABEL": "New", "PAGES.ACCOUNT.BUY_AGAIN.TITLE": "Buy Again", "PAGES.ACCOUNT.BUY_AGAIN.UNAVAILABLE_PRODUCT_MESSAGE": "Currently unavailable", "PAGES.ACCOUNT.BUY_AGAIN.UNAVAILABLE_PRODUCT.LINK_BUTTON_TEXT": "Explore Similar Products", "PAGES.ACCOUNT.INFO.ACCOUNT_ID": "Account ID", "PAGES.ACCOUNT.INFO.ACCOUNT_STATUS": "Status", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.ALERT.FILL_TO_VALIDATE": "Please, fill the information below to validate your business account", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.BUTTON.SAVE": "Save & Validate Business Account", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.BUTTON.SKIP": "Skip and Proceed as Consumer", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.CANCELLATION_DIALOG.CANCEL": "Add Business Info", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.CANCELLATION_DIALOG.SUBMIT": "Proceed as Consumer", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.CANCELLATION_DIALOG.TEXT": "Until you validate your business account by filling your Business information you will be using METRO Marketplace as Consumer", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.CANCELLATION_DIALOG.TITLE": "Leave now and proceed as Consumer?", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.FIELDS.LEGAL_FORM_TYPE.LABEL": "Legal Form (optional)", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.FOOTER.TEXT": "You will be able to add your business information later on and validate your account as business", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.LEGAL_TEXT": "By clicking \"Save & validate business account\" I confirm that I am entitled to represent the above-mentioned company.", "PAGES.ACCOUNT.INFO.ADD_BUSINESS_INFO.TITLE": "Add Business Info", "PAGES.ACCOUNT.INFO.ADDED_BUSINESS_INFO_MESSAGE": "You have been successfully validated as business buyer!", "PAGES.ACCOUNT.INFO.CHANGE_EMAIL.SUBMIT_BUTTON": "Send Email", "PAGES.ACCOUNT.INFO.CHANGE_EMAIL.TITLE": "Change Email", "PAGES.ACCOUNT.INFO.CHANGE_PASSWORD.SUBMIT_LABEL": "Update password", "PAGES.ACCOUNT.INFO.CHANGE_PASSWORD.TITLE": "Change Password", "PAGES.ACCOUNT.INFO.EDIT_BUSINESS_INFO.TITLE": "Change Business Information", "PAGES.ACCOUNT.INFO.EDIT_PRIVATE_INFO.TITLE": "Change Private Information", "PAGES.ACCOUNT.INFO.EXPIRED_CHANGE_EMAIL_LINK_DIALOG.CHANGE_EMAIL": "Change Email", "PAGES.ACCOUNT.INFO.EXPIRED_CHANGE_EMAIL_LINK_DIALOG.KEEP_EMAIL": "Keep current Email", "PAGES.ACCOUNT.INFO.EXPIRED_CHANGE_EMAIL_LINK_DIALOG.TEXT": "Looks like {linkLifetime} hours have passed already since you've requested to change e-mail associated with your account. Please request it again and we will send you a new link.", "PAGES.ACCOUNT.INFO.EXPIRED_CHANGE_EMAIL_LINK_DIALOG.TITLE": "Your change E-mail link has expired", "PAGES.ACCOUNT.INFO.NOT_ADDED_BUSINESS_INFO_MESSAGE": "We cannot validate your business account. You are considered as Consumer until you fill business information and we validate it.", "PAGES.ACCOUNT.INFO.PASSWORD_CHANGED_MESSAGE": "The password has been changed", "PAGES.ACCOUNT.INFO.PRIVATE_INFO": "Private Information", "PAGES.ACCOUNT.INFO.SAVED_MESSAGE": "Account Information has been saved", "PAGES.ACCOUNT.INFO.TITLE": "Account Information", "PAGES.ACCOUNT.INFO.WITHOUT_CHANGES_MESSAGE": "You don’t have any updates", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.BODY_TEXT": "The following business will be removed from your account. Are you sure you would like to delete the following branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.DELETE_BRANCH_BUTTON_TEXT": "Delete branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.DELETE_SUCCESS_NOTIFICATION_TEXT": "Branch successfully removed from your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.TITLE": "Delete a connect business", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.HEADING": "Add a branch to your company account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.INFO_TEXT_1": "To link a new branch to your company, you will require the company representative email and metro card number.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.INFO_TEXT_2": "The information below will be provided to the branch to identify where the invite is originating from.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.LABEL_AND_MODAL_HEADING.INVITE_BRANCH": "Invite a branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.LABEL.COMPANY_NAME": "Company Name", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.BRANCH_ADDED_NOTIFICATION_TEXT": "Branch successfully added to your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.BODY_INFO_TEXT": "We found the following company, click confirm to invite and connect this company to your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.HEADING": "Confirm company details", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.ADDRESS": "Address", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.CONFIRM_BRANCH_DETAILS": "Confirm branch details", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.EMAIL": "Email", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.VAT_ID": "VAT ID", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.VAT_ID.NOT_AVAILABLE": "Not available", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.HEADING": "Connected branches", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.BRANCH_NUMBER": "Branch number", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.CONTACT": "Contact", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.METRO_CARD": "Metro Card", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LOAD_MORE": "Show more branches", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.BODY_INFO_TEXT": "requires a digital account in order to continue. Confirm the account creation for this email to continue:", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.HEADING": "Create a digital account", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.LABEL.CONFIRM_ACCOUNT_CREATION": "Confirm account creation", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.FORM.BUTTON": "Back", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.BUTTON.INVITE_BRANCH": "Invite branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.FOOTER_NOTE": "Please ask the company to sign up for a METRO card here if they don’t have one.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.HEADING": "Please fill out the following information", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.METRO_CARD_NUMBER": "METRO card number", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.METRO_CARD_NUMBER.ERROR.ALREADY_LINKED": "The company you are trying to add is already linked as a branch to your organisation.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.REPRESENTATIVE_EMAIL": "Representative email", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.REPRESENTATIVE_NAME": "Representative name", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.LABEL.EMAIL_ADDRESS": "Email Address", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.PAGE_TITLE": "Registered branches", "PAGES.ACCOUNT.LOADING": "Loading...", "PAGES.ACCOUNT.MENU.BUSINESS_ADDRESSES.TITLE": "Address Book", "PAGES.ACCOUNT.MENU.BUSINESS_PROFILE.TITLE": "Business Profile", "PAGES.ACCOUNT.MENU.CONTACT_DETAILS.TITLE": "Business Contact Details", "PAGES.ACCOUNT.MENU.DIGITAL_PURCHASE.TITLE": "Digital Purchases Authorization", "PAGES.ACCOUNT.MENU.INVOICES_OF_STORE.TITLE": "eInvoices", "PAGES.ACCOUNT.MENU.LOGIN_SECURITY.TITLE": "Login & Security", "PAGES.ACCOUNT.MENU.LOGOUT.TITLE": "Logout", "PAGES.ACCOUNT.MENU.METRO_MAIL.TITLE": "METRO Mail", "PAGES.ACCOUNT.MENU.NEWSLETTERS_PROMOTIONS.TITLE": "Newsletters & Promotions", "PAGES.ACCOUNT.MENU.PAYMENT_METHODS.TITLE": "Onlineshop payment methods", "PAGES.ACCOUNT.MENU.PERSONAL_ADDRESSES.TITLE": "Addresses", "PAGES.ACCOUNT.MENU.PERSONAL_PROFILE.TITLE": "Your Profile", "PAGES.ACCOUNT.MENU.PROMOTIONAL_FLYERS.TITLE": "Promotional flyers", "PAGES.ACCOUNT.MENU.PURCHASES_ONLINE_SHOP.TITLE": "Marketplace", "PAGES.ACCOUNT.MENU.PURCHASES.TITLE": "Purchases", "PAGES.ACCOUNT.MENU.STAFF_AND_PERMISSIONS.TITLE": "Staff & Permissions", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.BACK.BUTTON": "Back to orders", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.BODY": "Something went wrong while loading your order details. Please reload the page to try again.", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.RELOAD.BUTTON": "Reload", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.TITLE": "Couldn’t load order details", "PAGES.ACCOUNT.ORDER_HISTORY": "Close", "PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.EMPLOYEE_SIGNOUT": "Employee sign out", "PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.NEW_IMPERSONATION": "Start with a new customer", "PAGES.ACCOUNT.ORDER_HISTORY.BUY_AGAIN": "Buy Again", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL_PRODUCT": "Cancel product", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL_TITLE": "Cancel item", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CANCEL": "Do not cancel", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CONFIRM": "Confirm Cancellation", "PAGES.ACCOUNT.ORDER_HISTORY.COMPANY": "Company:", "PAGES.ACCOUNT.ORDER_HISTORY.CONTACT_SELLER": "<PERSON>ller", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.DOCUMENTS": "Get documents", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.INVOICES": "Get invoice(s)", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.DOWNLOAD_ALL": "Download all", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE": "Documents for order", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE.CREDIT_NOTES": "Credit notes", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE.INVOICES": "Invoices", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.BODY": "Something went wrong while loading your orders. Please reload the page to try again.", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.MESSAGE": "Something went wrong while processing your request. Please try again later.", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.RELOAD": "Reload", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.TITLE": "Couldn’t load orders", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY": "Payment and delivery information", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.BILLING_ADDRESS": "Billing address", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.DELIVERY_ADDRESS": "Delivery address", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.PAID_BY": "Paid by", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.CONTACT": "Contact Customer Service", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.INFO": "You will be contacted by the service provider to arrange a date and time.", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.INSTRUCTIONS": "View installation instructions", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.BODY": "You have not placed any orders yet. Place your first order to see the purchase details here.", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.CONTINUE_SHOPPING": "Continue shopping", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.TITLE": "No orders yet", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.CANCEL.ORDER": "Cancel All Items", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.CANCEL.ORDER.DIALOG.TITLE": "Cancel order #{orderNumber}?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER": "<PERSON>ller", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.ENQUIRY": "Order enquiry", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.TIME": "Request Delivery Time", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.TRACKING_INVALID": "Track id might be invalid", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.TRACKING_NOT_AVAILABLE": "Not available", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY": " ", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT": "Question about the order {orderNumber}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY": "Dear seller, %0D%0Dplease provide me with the tracking code for the order {orderNumber}. %0D%0DThank you", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT": "Request code for shipment tracking for order {orderNumber}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY": "Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY_WITH": "Delivered with", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.CURBSIDE": "Curbside Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PARCEL": "<PERSON><PERSON><PERSON>", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PLACE_OF_USE": "Place of Use Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.STANDARD": "Standard Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DISCOUNT": "Discount", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.EXCL_VAT": "excl. VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.INCL_VAT": "incl, VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.MULTIPLE.PACKAGES": "Multiple packages", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.ORDER_PLACED": "Order placed:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.PACKAGE": "Package", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.PRICE": "Price", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.QUANTITY": "Quantity", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.RETURN": "Return item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.SOLD_BY": "Sold by", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.CANCELLATION_REASONS": "Cancellation reasons", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SECURITY": "Security Check Failed. This is to safeguard your account and ensure safe shopping. Contact support if needed.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SELLER": "Refund has been initiated. It might take several business days depending on your bank account.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.DELAYED": "Delayed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_BY": "Expected by", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY": "Expected delivery:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_ON": "Delivered on", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PAID": "We are manually checking your payment. This is to safeguard your account and ensure safe shopping. Contact support if needed.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED": "We will communicate a new delivery estimation via e-mail when the item will be shipped.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED.EXPECTED": "Initial expected delivery:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REFUNDED": "Refund might take several business days depending on your bank account.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REJECTED": "Contact your head office for more info", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.RETURNED": "We will proceed with the refund soon", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.SHIPPED_FAILED": "Please track your item or reach out to the seller", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED": "Order cancelled", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED": "Order delayed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED": "Order placed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.PAYMENT_FAILED": "Payment failed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.READY_SHIP": "Ready to ship", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REFUNDED": "Refunded", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REJECTED": "Order rejected", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED": "Shipped", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED": "Delivered", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_FAILED": "Delivery failed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_ON_ITS_WAY": "On its way", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_APPROVAL": "Pending approval", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_PAYMENT": "Payment processing", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN_REJECTED_REASONS": "Return decline reason", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT": "You will be contacted by the courier company or the seller to arrange the pickup of your return. For more information", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT.PLANT": "We sent you an e-mail with the instructions to follow to return your item. Please check your e-mail.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.VIEW_INSTRUCTIONS": "view return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.VIEW_REASONS": "View reason", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TOTAL_PRICE": "Total price", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK": "Track this item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_ID": "Track ID:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_PACKAGE": "Track package {numPackage}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING_ITEM": "Track this item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.BUTTON": "Tracking not available", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.LABEL": "Why there is no tracking?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.INFO": "Shipment tracking may not be available for the following reasons:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_1": "Some shipments do not have tracking information.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_2": "Marketplace sellers do not always provide Metro Markets with tracking information for their orders.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_3": "The freight forwarder still has to pick up the shipment from the seller.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_4": "There may be delays in synchronizing data.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_1": "Do you have further questions? Check out our", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_2": "FAQs for Delivery & Tracking.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.TITLE": "Why is there no tracking available?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.DISCOUNT": "Total Discount", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.GRAND_TOTAL": "Grand Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.SHIPPING_COST": "Shipping cost", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.SUBTOTAL": "Subtotal", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.TITLE": "Summary of the order", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.VAT": "VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.BACK": "Back to Orders", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.DELIVER": "Deliver to", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDER_NUMBER": "Order number", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDER_PLACED": "Orders placed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDERS": "Orders", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDERS_DETAILS": "Order Details", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.VIEW_DETAILS": "View Order Details", "PAGES.ACCOUNT.ORDER_HISTORY.PENDING_INFO.CHECK_EMAIL": "Check your email", "PAGES.ACCOUNT.ORDER_HISTORY.PENDING_INFO.PAYMENT_PENDING": "Rate Pay payment pending.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_1.CONTENT": "Prepare the item(s) for a pick-up.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_1.TITLE": "Prepare items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_2.CONTENT": "Wait for our customer service team to contact you and arrange a pick-up date and time for your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_2.TITLE": "Receive pick-up details", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_3.CONTENT": "Handover your return item(s) to the carrier.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_3.TITLE": "Handover items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_4.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_4.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.TITLE": "<PERSON><PERSON><PERSON> returns to Metro", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_1.CONTENT": "Pack the item(s) in an appropriate package or the one you received it in.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_1.TITLE": "Pack items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_2.CONTENT": "Print and attach return label on the package. Use a separate package for each label you received. You can find the return label attached in your email or download it from your orders page.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_2.TITLE": "Attach return label", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_3.CONTENT": "Drop the package(s) at a convenient branch of the carrier assigned to your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_3.TITLE": "Drop-off", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_4.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_4.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.TITLE": "<PERSON><PERSON><PERSON> returns to Metro", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.MORE_1": "Do you have further questions? Check out our", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.MORE_2": "FAQs for Returns & Refunds", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_1.CONTENT": "Wait for seller instructions which may take up to 7 working days.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_2.CONTENT": "Print and attach return label on the package. Use a separate package for each label you received. You can download the return label from your orders page once it is available.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_2.TITLE": "Received a return label?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_3.CONTENT": "Wait for the seller to contact you and arrange a pick-up date and time for your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_3.TITLE": "Returning a bulky item?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.TITLE": "Receive seller instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_2.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_2.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.TITLE": "Returns to Partners", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.TITLE": "Return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.DOWNLOAD": "Download return label", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.INFORMATION": "Your return label will be available as soon as the seller provides it. You will be notified via e-mail.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.VIEW_INSTRUCTIONS": "View return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL": "Cancel Return", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CANCEL": "Cancel", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CONFIRM": "Yes, discard", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CONTENT": "Discard return request?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.TITLE": "You are going to discard the return request.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.ALL": "All", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CANCEL": "Cancel", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CONFIRM": "Return item(s)", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.ARIA_LABEL": "Selected option is", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.ARIA_LABEL.NONE": "none", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.QUANTITY": "Select return quantity", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.RETURN": "Select a reason for return", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO": "Depending on the item, you'll either receive a return label, or the seller will contact you with the next steps.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO.FEE": "A return fee may be deducted from your refund.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.LEARN_MORE": "Learn more", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.QUANTITY": "Return quantity", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.REASON": "Return reason", "PAGES.ACCOUNT.ORDERS_HISTORY.ALERT_SUCCESS_CANCEL": "Item(s) successfully canceled", "PAGES.ACCOUNT.ORDERS_HISTORY.BILLING_ADDRESS": "Billing address", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_ORDER": "Cancel order", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_PRODUCT": "Cancel product", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_PRODUCTS": "Cancel products", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_RETURN_ORDER": "Cancel return request", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_RETURN_PRODUCT": "Cancel return request", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CANCEL_RETURN_PRODUCTS": "Cancel return request", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_CONTACT_SELLER": "Contact seller", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_CREDIT_NOTE": "Get credit notes", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_CREDIT_NOTE_HINT": "As soon as the seller uploads the credit note you can download it here.", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_INVOICE": "Get Invoices", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_INVOICE_HINT": "As soon as the seller uploads the invoice you can download it here.", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_RETURN_LABEL": "Get return labels", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_DOWNLOAD_RETURN_LABEL_HINT": "As soon as the seller uploads the return label you can download it here.", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_INVOICES": "Invoices", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_REDUCE_QUANTITY": "Reduce quantity", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_RETURN_INSTRUCTIONS": "Return instructions", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_RETURN_ORDER": "Return order", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_RETURN_PRODUCT": "Return product", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_RETURN_PRODUCT_TOOLTIP": "Option is not available anymore. For more details contact Customer support.", "PAGES.ACCOUNT.ORDERS_HISTORY.BUTTON_RETURN_SELLER_PRODUCTS": "Return products", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_DIALOG.CONFIRM_BUTTON": "Confirm Cancellation", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_DIALOG.TEXT": "Are you sure you want to cancel order? Note: only placed products will be canceled", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_DIALOG.TITLE": "Cancel order #{orderNumber}?", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_LINE_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_LINE_DIALOG.CONFIRM_BUTTON": "Confirm Cancellation", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_LINE_DIALOG.TEXT": "Are you sure you want to cancel product?", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_LINE_DIALOG.TITLE": "Cancel product {name}?", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_SELLER_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_SELLER_DIALOG.CONFIRM_BUTTON": "Confirm Cancellation", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_SELLER_DIALOG.TEXT": "Are you sure you want to cancel order? Note: only placed products will be canceled.", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_ORDER_SELLER_DIALOG.TITLE": "Cancel order by {name}?", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_RETURN_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_RETURN_DIALOG.SUBMIT_BUTTON": "Yes, discard", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_RETURN_DIALOG.TEXT": "Discard return request?", "PAGES.ACCOUNT.ORDERS_HISTORY.CANCEL_RETURN_DIALOG.TITLE": "You are going to discard the return request.", "PAGES.ACCOUNT.ORDERS_HISTORY.CREDIT_NOTES": "Credit notes", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.CREDIT_NOTES.TITLE": "Credit notes", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.DATE.TEXT": "uploaded on", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.INVOICES.TITLE": "Invoices", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.NOT_PROVIDED.TEXT": "Not provided", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.PRODUCTS.TITLE": "Product", "PAGES.ACCOUNT.ORDERS_HISTORY.DOWNLOAD_INVOICE_MODAL.TITLE": "Documents", "PAGES.ACCOUNT.ORDERS_HISTORY.FILTERS.PERIOD": "Period", "PAGES.ACCOUNT.ORDERS_HISTORY.FILTERS.PERIOD.ALL": "All", "PAGES.ACCOUNT.ORDERS_HISTORY.FILTERS.PERIOD.QUARTER": "Current quarter", "PAGES.ACCOUNT.ORDERS_HISTORY.FILTERS.PERIOD.WEEK": "Current week", "PAGES.ACCOUNT.ORDERS_HISTORY.FILTERS.PERIOD.YEAR": "Year", "PAGES.ACCOUNT.ORDERS_HISTORY.GRAND_TOTAL": "Grand Total", "PAGES.ACCOUNT.ORDERS_HISTORY.INVOICES": "Invoices", "PAGES.ACCOUNT.ORDERS_HISTORY.LOAD_MORE_BUTTON": "Show next orders", "PAGES.ACCOUNT.ORDERS_HISTORY.NO_ORDERS_IN_LIST": "No orders available", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_CANCELED_BY_LABEL": "Canceled by", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_CUSTOMER_SUPPORT.VALUE": "Customer support", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_DATE_ON_CONFIRMED_STATUS.LABEL": "started", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_DELIVERY_BY": "Delivery by", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_DELIVERY_DATE": "Expected delivery:", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_DELIVERY.NOT_PROVIDED.MSG": "Not provided", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_REASON_LABEL": "Reason", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_REJECTION_REASON_LABEL": "Rejection reason", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_RETURN_REASON_LABEL": "Return reason", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_RETURNED_BY.LABEL": "Requested by", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SHIPPING_DETAILS": "Shipping Details", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SHIPPING_DETAILS.CARRIER": "Delivery carrier", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SHIPPING_DETAILS.NOT_DEFINED": "Not defined", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SHIPPING_DETAILS.ORIGIN": "Country", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SHIPPING_DETAILS.TRACKING_ID": "Tracking ID", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_SOLD_BY": "Sold by", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE_STATUS_CHANGED_ON": "on", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE.DEFAULT_CANCELLATION_REASON": "Have changed my mind", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_LINE.FAILED.REASON.VALUE": "Payment failed", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_ORDERED_ON": "ordered on", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDER_ORDERED_ON (Copy)": "ordered on", "PAGES.ACCOUNT.ORDERS_HISTORY.ORDERING_DATE": "Ordering date", "PAGES.ACCOUNT.ORDERS_HISTORY.PAYED_BY": "Paid by", "PAGES.ACCOUNT.ORDERS_HISTORY.PAYMENT_AND_SHIPMENT.TITLE": "Payment & Shipping info", "PAGES.ACCOUNT.ORDERS_HISTORY.PAYMENT.PAYPAL_INS_FRANCE": "4X", "PAGES.ACCOUNT.ORDERS_HISTORY.PRICE": "(VAT {exclude, select, true{Excl.} other{Incl.}})", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_DISCOUNT": "Discount", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_PPI": "Price per item", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_PRICE": "Price", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_QUANTITY": "Quantity", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_SHIPPING": "Shipping", "PAGES.ACCOUNT.ORDERS_HISTORY.PRODUCT_TOTAL_PRICE": "Total Price", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_CONFIRM_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_CONFIRM_DIALOG.CONFIRM_BUTTON": "Confirm Cancellation", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_CONFIRM_DIALOG.TEXT": "It will stay in Orders, you can go back to it anytime.", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_CONFIRM_DIALOG.TITLE": "Cancel Product {productName}?", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_DIALOG.CONFIRM_BUTTON": "Update quantity", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_DIALOG.PICKER.NEW": "New Quantity", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_DIALOG.PICKER.OLD": "were ordered", "PAGES.ACCOUNT.ORDERS_HISTORY.QUANTITY_PRODUCT_DIALOG.TITLE": "Update quantity", "PAGES.ACCOUNT.ORDERS_HISTORY.RATEPAY_NOTICE.EXTRA": "The due date for RatePay Invoice is 14 Days after you received the shipping notice from our seller.", "PAGES.ACCOUNT.ORDERS_HISTORY.RATEPAY_NOTICE.TEXT": "Please use the payment information provided by RatePay via Email. You will find the RatePAY IBAN as well as the RatePAY ID. Both are necessary so we are able to recognize your payment.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_INSTRUCTIONS.TEXT": "<p><b>Please note the following steps when returning the parcels:</b></p>\n        <p class='mt-gutter'><b>1. For parcel returns please download the return labels</b></p>\n        <p>You can print the labels and affix them to the corresponding return shipment.\n            Please use a separate package for each label you have received.\n            If the return label is not directly available, do not worry. Your request has been automatically submitted\n            to the seller and you will receive the return label from your seller.</p>\n        <p><b>Important note:</b> Whether the seller grants a right of withdrawal, please refer to the seller’s\n   Terms and Conditions</p>\n\n\n        <p class='mt-gutter'><b>2. Do you want to return items to different sellers?</b></p>\n        <p>If you are returning products from different sellers, please make sure to use the appropriate labels.</p>\n\n        <p class='mt-gutter'><b>3. Does your return contain any hazardous goods?</b></p>\n        <p>If your return contains hazardous goods, please make sure to affix the attached warning label to the top of\n            the\n            parcel.\n            You are not sure what hazardous goods are? Please have a look at our <a target='_blank' href={linkToFAQs}>FAQs</a></p>\n\n\n        <p class='mt-gutter'><b>4. Bring parcels to a delivery carrier</b></p>\n        <p>Please bring the parcels to the corresponding carrier. If you return products to different sellers, please\n            bring\n            them to the appropriate carrier.</p>\n\n        <p class='mt-gutter'><b>5. Get a refund</b></p>\n        <p class='mb-gutter-xs'>The Seller will review your parcels and will confirm the return. You will be able\n            to download a receipt of\n            refund in your order history and get a refund as soon as your return has been processed.</p>\n        <p>Please note that in exceptional cases our customer service may contact you to clarify any outstanding issues.\n            If you have any further questions, please have a look at our <a target='_blank' href={linkToFAQs}>FAQs</a> or contact our\n            hotline service.</p>\n\n        <p class='mt-gutter'><b>Please note the following steps when returning freight items:</b></p>\n\n        <p class='mt-gutter'><b>1. Your request will be forwarded to the seller</b></p>\n        <p>Your request has been automatically submitted to the seller. There is no further action required from your\n            site.</p>\n        <p><b>Important note:</b>Whether the seller grants a right of withdrawal, please refer to the seller’s\n    Terms and Conditions</p>\n\n        <p class='mt-gutter'><b>2. You will get a call from the seller or the freight company</b></p>\n        <p>You will decide on a mutual date for the pick-up of your return.</p>\n\n        <p class='mt-gutter'><b>3. Did you request returns for freight items to different sellers?</b></p>\n        <p>If you requested returns for freight items to different sellers, each seller will take care of the returns\n            individually.</p>\n\n        <p class='mt-gutter'><b>4. Prepare items for pick-up</b></p>\n        <p>Please make sure that the goods are properly packed for pick-up at the day of the pick-up.</p>\n\n        <p class='mt-gutter'><b>5. Get a refund</b></p>\n        <p class='mb-gutter-xs'>The Seller will review and confirm the return.\n            You will be able to download a receipt of refund in your order\n            history and get a refund as soon as your return has been processed.</p>\n        <p> Please note that in exceptional cases our customer service may contact you to clarify any outstanding issues\n            If you have any further questions, please have a look at our <a target='_blank' href={linkToFAQs}>FAQs</a> or contact our\n            hotline service.</p>", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_LABELS": "Return labels", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_ORDER_DIALOG.TITLE": "Return order #{orderNumber}?", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_CONFIRM_DIALOG.TEXT": "You will begin return process", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_CONFIRM_DIALOG.TITLE": "Are you sure?", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.CONFIRM_BUTTON": "Return product", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.CONFIRM_BUTTON_2": "Yes, request return", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.QUANTITY": "Quantity for return", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.QUANTITY_WERE": "{count} {count, plural, one{was ordered} other{were ordered}}", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.REASON_ERROR": "Please select a reason from the drop-down list.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.REASON_LABEL": "Return reason", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.REASON_PLACEHOLDER": "Please let us know why it didn’t fit", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.REFUND_FEE_1": "A return fee may be deducted from your refund.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.REFUND_FEE_2": "Learn more", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.TEXT": "Seller will review your request and come back to you shortly.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_DIALOG.TITLE": "Return Product", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_SUCCESS_ALERT.TEXT": "We will inform You about seller confirmation via email.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCT_SUCCESS_ALERT.TITLE": "Return request was created.", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.BODY": "Seller will review your return request and go back to you shortly", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.CANCEL_BUTTON": "Cancel", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.CONFIRM_BUTTON": "Return order", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.SELECT_LABEL": "Return reason", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.SELECT_REASONS_PLACEHOLDER": "Please, let us know why it didn’t fit", "PAGES.ACCOUNT.ORDERS_HISTORY.RETURN_PRODUCTS_DIALOG.TITLE": "Return products from {shopName}?", "PAGES.ACCOUNT.ORDERS_HISTORY.SHIPPING": "Shipping", "PAGES.ACCOUNT.ORDERS_HISTORY.SHIPPING_ADDRESS": "Shipping address", "PAGES.ACCOUNT.ORDERS_HISTORY.SUBTOTAL": "Subtotal", "PAGES.ACCOUNT.ORDERS_HISTORY.SUBTOTAL_SHIPPING": "Subtotal and Shipping", "PAGES.ACCOUNT.ORDERS_HISTORY.SUCCESS_REFUND_REQUEST_MESSAGE": "Return is successfully requested", "PAGES.ACCOUNT.ORDERS_HISTORY.TABLE.COLUMN.AMOUNT.CAPTION": "Total amount", "PAGES.ACCOUNT.ORDERS_HISTORY.TABLE.COLUMN.CREATED-AT.CAPTION": "Created on", "PAGES.ACCOUNT.ORDERS_HISTORY.TABLE.COLUMN.ORDER-NUMBER.CAPTION": "Order number", "PAGES.ACCOUNT.ORDERS_HISTORY.TABLE.COLUMN.ORDER-SELLER.CAPTION": "<PERSON><PERSON>", "PAGES.ACCOUNT.ORDERS_HISTORY.TABLE.COLUMN.STATUS.CAPTION": "Order status", "PAGES.ACCOUNT.ORDERS_HISTORY.TITLE": "Orders", "PAGES.ACCOUNT.ORDERS_HISTORY.TOTAL": "Total", "PAGES.ACCOUNT.ORDERS_HISTORY.TOTAL_DISCOUNT": "Total Discount", "PAGES.ACCOUNT.ORDERS_HISTORY.TOTAL_SHIPPING": "Shipping", "PAGES.ACCOUNT.ORDERS_HISTORY.VAS_LABEL": "Installation services for", "PAGES.ACCOUNT.ORDERS_HISTORY.VAS_LABEL_FOR": "für", "PAGES.ACCOUNT.ORDERS_HISTORY.VAS.PRODUCT_PPS": "Price per service", "PAGES.ACCOUNT.ORDERS_HISTORY.VAT": "VAT", "PAGES.ACCOUNT.ORDERS_HISTORY.VAT_EXCL": "(VAT Excl.)", "PAGES.ACCOUNT.ORDERS_HISTORY.VAT_INCL": "(VAT Incl.)", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.DELETE_ACTION.ERROR": "Unable to delete the saved payment method. Please try again later", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.DELETE_ACTION.SUCCESS": "The saved payment method has successfully been removed", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.DELETE_BUTTON.TITLE": "Remove saved payment method", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.EMPTY.HEADER": "No saved payment details", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.EMPTY.SUBHEADER": "You have not yet saved a payment method to this account. When completing future orders on our Onlineshop you will have the option to save a payment method during our checkout process.", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.EXPIRE.TEXT": "Expires", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.HEADER": "Payment details", "PAGES.ACCOUNT.PAYMENT_METHODS.LISTING.SUBHEADER": "Your saved payment methods for use on our Onlineshop.", "PAGES.ACCOUNT.PAYMENT_METHODS.TITLE": "Onlineshop payment methods", "PAGES.ACCOUNT.SHOPPING_LIST.FILTERS.BUTTON.SHOW": "Show {count} Products", "PAGES.ACCOUNT.SUPPORT.TITLE": "Support", "PAGES.ACCOUNT.YOUR_ASSORTMENT.ALL_PRODUCTS": "All products", "PAGES.ACCOUNT.YOUR_ASSORTMENT.FILTERS.CATEGORIES.TITLE": "Categories", "PAGES.ACCOUNT.YOUR_ASSORTMENT.FILTERS.CLEAR_ALL_TAG.TEXT": "Clear All", "PAGES.ACCOUNT.YOUR_ASSORTMENT.FILTERS.TITLE.FILTERS": "Filter", "PAGES.ACCOUNT.YOUR_ASSORTMENT.NO_PRODUCTS_IN_LIST": "No data available", "PAGES.ACCOUNT.YOUR_ASSORTMENT.PRODUCTS_IN_LIST": "No data available", "PAGES.ACCOUNT.YOUR_ASSORTMENT.SEARCH_PRODUCT_COUNT": "{count} {count, plural, =1{product} other{products}}", "PAGES.ACCOUNT.YOUR_ASSORTMENT.SEARCH_RESULT_FOR": "Search result for \"{searchKeyword}\"", "PAGES.ACCOUNT.YOUR_ASSORTMENT.SEARCH.PLACEHOLDER_TEXT": "Find products", "PAGES.ACCOUNT.YOUR_ASSORTMENT.TOOLBAR.FILTERS_BUTTON.TEXT": "Filters", "PAGES.ASSISTED_SALES.OVERVIEW.EMPTY_STATE.ORDERS.TEXT": "You have not completed any assisted sales orders. Once you have orders placed they will appear here.", "PAGES.ASSISTED_SALES.OVERVIEW.EMPTY_STATE.ORDERS.TITLE": "No orders available", "PAGES.ASSISTED_SALES.OVERVIEW.EMPTY_STATE.PAYMENT_LINKS.TEXT": "All payment links created in the last 14 days have been opened by the customer.", "PAGES.ASSISTED_SALES.OVERVIEW.EMPTY_STATE.PAYMENT_LINKS.TITLE": "Payment Links Status", "PAGES.ASSISTED_SALES.TABLE.COLUMN.ACTIONS.TEXT": "Actions", "PAGES.ASSISTED_SALES.TABLE.COLUMN.COMPANY_NAME.TEXT": "Company Name", "PAGES.ASSISTED_SALES.TABLE.COLUMN.COPY_PAYMENT_LINK_FAILED.TEXT": "Copy Payment link Failed", "PAGES.ASSISTED_SALES.TABLE.COLUMN.COPY_PAYMENT_LINK.TEXT": "Copy Payment Link", "PAGES.ASSISTED_SALES.TABLE.COLUMN.COPYING_PAYMENT_LINK.TEXT": "Copying Payment Link", "PAGES.ASSISTED_SALES.TABLE.COLUMN.CREATED_ON.TEXT": "Created On", "PAGES.ASSISTED_SALES.TABLE.COLUMN.CUSTOMER_NAME.TEXT": "Customer Name", "PAGES.ASSISTED_SALES.TABLE.COLUMN.EMAIL_EXPIRED.TEXT": "Email expired", "PAGES.ASSISTED_SALES.TABLE.COLUMN.EMAIL_SENT.TEXT": "Email sent", "PAGES.ASSISTED_SALES.TABLE.COLUMN.LINK_EXPIRED.TEXT": "Link expired", "PAGES.ASSISTED_SALES.TABLE.COLUMN.METHOD.TEXT": "Method", "PAGES.ASSISTED_SALES.TABLE.COLUMN.METRO_CARD_NUMBER.TEXT": "Metro Card Number", "PAGES.ASSISTED_SALES.TABLE.COLUMN.OPENED.TEXT": "Opened", "PAGES.ASSISTED_SALES.TABLE.COLUMN.ORDER_DATE.TEXT": "Order Date", "PAGES.ASSISTED_SALES.TABLE.COLUMN.PAYMENT_LINK_COPIED.TEXT": "Payment Link Copied", "PAGES.ASSISTED_SALES.TABLE.COLUMN.RESEND_EMAIL_FAILED.TEXT": "Resend email failed", "PAGES.ASSISTED_SALES.TABLE.COLUMN.RESEND_EMAIL.TEXT": "Resend email", "PAGES.ASSISTED_SALES.TABLE.COLUMN.SENDING_EMAIL.TEXT": "Sending email", "PAGES.ASSISTED_SALES.TABLE.COLUMN.STATUS.TEXT": "Status", "PAGES.ASSISTED_SALES.TABLE.COLUMN.UNOPENED.TEXT": "Unopened", "PAGES.AUTH.ADD_BUSINESS_DETAIL.MAKRO.LINK_TEXT": "Makro.es", "PAGES.AUTH.ADD_BUSINESS_DETAILS.CONDITIONS.LINK_TEXT": "conditions of use for the marketplace", "PAGES.AUTH.ADD_BUSINESS_DETAILS.CONDITIONS.PREFIX": "; as well as to the", "PAGES.AUTH.ADD_BUSINESS_DETAILS.CONDITIONS.SUFFIX": " and of ", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.TAXNUMBERNOTUNIQUE": "The company with this tax identification code is already registered.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_ID_BUSINESS_MISMATCH": "Your VAT ID does not seem to belong to the selected business. Please enter the VAT ID of your business.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_ID_INVALID": "VAT ID is invalid", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_ID_INVALID_FOR_ZERO_TAX": "VAT ID is invalid for 0% Tax Rate", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_NUMBER_BLOCKED": "Looks like your company is obliged to do split payments. The METRO Onlineshop cannot accept payments from companies that are obliged to do split payments, unfortunately.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_NUMBER_BLOCKED.TITLE": "Sorry, you can’t register this company", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_NUMBER_NOT_VALID": "We were unable to validate your business details, please make sure you have entered the correct information.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_NUMBER_NOT_VALID_ON_VAT_ID_CHECK": "We were unable to validate your VAT ID, Please make sure your VAT ID contains country code (IT) and 11 digits.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VAT_NUMBER_NOT_VALID.TITLE": "Validation was unsuccessful", "PAGES.AUTH.ADD_BUSINESS_DETAILS.ERROR.VATNUMBERNOTUNIQUE": "The company with this vat identification code is already registered.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.COOKIES_TEXT": "cookie information policies", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.COOKIES.POSTFIX": " of Makro and Metro will apply.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.COOKIES.PREFIX": "of Makro and Metro will apply.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.METRO.PREFIX": "and", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.PRIVACY.LINK_TEXT": "The privacy policies of Makro and Metro will apply.", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.PRIVACY.PREFIX": "; as well as to the ", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.TNC.LINK_TEXT": "Digital Account Terms of Use and Registration", "PAGES.AUTH.ADD_BUSINESS_DETAILS.LEGAL_TEXT.TNC.PREFIX": "The Digital Account is owned by Makro Wholesale Self-Service and Metro Markets. We process your personal data together as co-responsible for the processing for the activities linked to it. By clicking \"Register\", you agree to the", "PAGES.AUTH.ADD_BUSINESS_DETAILS.MAKRO.LINK_TEXT_1": "<PERSON><PERSON><PERSON> ", "PAGES.AUTH.ADD_BUSINESS_DETAILS.MAKRO.SUFFIX": "for the use of", "PAGES.AUTH.ADD_BUSINESS_DETAILS.WARNING_INFO.ADD_VALID_VAT_ID_FOR_ZERO_TAX": "Please add a valid VAT ID for 0% Tax Rate, otherwise regular Tax Rate will apply.", "PAGES.AUTH.AFTER_EMAIL_VALIDATION.ELEMENTS.MESSAGE": "The validation email has been sent to the provided email address.", "PAGES.AUTH.AFTER_EMAIL_VALIDATION.TITLE": "The validation email has been sent!", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.CHECK_SPAM_NOTIFICATION": "Please check your Spam folder or", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.DID_NOT_GET_EMAIL_QUESTION": "Did not get our email?", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.LOGIN_BUTTON": "Return to login", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.MESSAGE": "Restore password instructions have been sent to the provided email address.", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.NOTIFICATION": "Restore password instructions have been sent to the provided email address.", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.ELEMENTS.TRY_AGAIN_NOTIFICATION": "try again", "PAGES.AUTH.AFTER_PASSWORD_RESTORATION.TITLE": "The email has been sent!", "PAGES.AUTH.ASSISTED_SALES.ACCESS_ORDER_REVIEW": "Access Order Overview", "PAGES.AUTH.ASSISTED_SALES.ACCESS_ORDER_REVIEW.ACCESS_OVERVIEW": "Access Assisted Sales Overview", "PAGES.AUTH.ASSISTED_SALES.ACCESS_ORDER_REVIEW.NOTICE": "You are about to log out from the current customer account and end impersonation, do you want to continue?", "PAGES.AUTH.ASSISTED_SALES.ACCESS_ORDER_REVIEW.RESUME_CURRENT_SESSION": "Resume current session", "PAGES.AUTH.CHECK.VAT.SUB.TITLE": "Please enter your VAT ID so we can confirm your company", "PAGES.AUTH.CONTEXT_SWITCH.ERROR": "There was a problem while switching to the newly created account, please refresh the page.", "PAGES.AUTH.CUSTOMERS.BUTTON.IMPERSONATE.TEXT": "Impersonate", "PAGES.AUTH.CUSTOMERS.BUTTON.IMPERSONATE.TEXT.BLOCKED": "Blocked", "PAGES.AUTH.CUSTOMERS.BUTTON.IMPERSONATE.TEXT.PENDING": "Pending", "PAGES.AUTH.CUSTOMERS.SEARCH_BUTTON.TEXT": "Search", "PAGES.AUTH.CUSTOMERS.SEARCH_FIELD.PLACEHOLDER.TEXT": "Enter a customer METRO card number, name, or company name", "PAGES.AUTH.CUSTOMERS.SEARCH_PAGE.TITLE.TEXT": "Customer Search", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.ACTION.TEXT": "Action", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.ADDRESS.TEXT": "Address", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.ASSISTED.TEXT": "Assisted", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.COMPANY_NAME.TEXT": "Company Name", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.EMAIL.TEXT": "Email", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.FULL_NAME.TEXT": "Customer Name", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.HAS_DIGITAL_IDENTITY.TEXT": "Digital identity", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.LINK.TEXT": "Link", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.METRO_CARD.TEXT": "Metro Card", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.ORDER_NUMBER.TEXT": "Order Number", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.ORDER_VALUE.TEXT": "Order Value", "PAGES.AUTH.CUSTOMERS.TABLE.COLUMN.PHONE.TEXT": "Phone", "PAGES.AUTH.CUSTOMERS.TABLE.EMPTY.HINT.TEXT": "Use the search bar to find a customer account", "PAGES.AUTH.CUSTOMERS.TABLE.EMPTY.HINT.TITLE": "Customer Impersonation", "PAGES.AUTH.CUSTOMERS.TABLE.ERROR_OCCURED_HINT.TEXT": "Please refresh the page and try search again.", "PAGES.AUTH.CUSTOMERS.TABLE.ERROR_OCCURED.TEXT": "An error occurred.", "PAGES.AUTH.CUSTOMERS.TABLE.IMPERSONATION.NOT.ALLOWED.HINT.TEXT": "You do not have the right permissions associated with your account to proceed.  You can <b>request</b> the <b>access rights</b> from your manager through <b>IDAM</b>.", "PAGES.AUTH.CUSTOMERS.TABLE.IMPERSONATION.NOT.ALLOWED.HINT.TITLE": "You are unable to proceed.", "PAGES.AUTH.CUSTOMERS.TABLE.NO_RESULTS_FOUND_FOR.TEXT": "No results found for", "PAGES.AUTH.CUSTOMERS.TABLE.NO_RESULTS_FOUND_NOTICE.TEXT": "We were unable to find the customer you searched for. Try searching for a different term.", "PAGES.AUTH.CUSTOMERS.TABLE.NO_RESULTS_FOUND.TEXT": "No results found", "PAGES.AUTH.CUSTOMERS.THANK_YOU_PAGE.BUTTON.EMPLOYEE_SIGNOUT": "Employee sign out", "PAGES.AUTH.CUSTOMERS.THANK_YOU_PAGE.BUTTON.NEW_IMPERSONATION": "Start with a new customer", "PAGES.AUTH.CUSTOMERS.THANK_YOU_PAGE.HEADING": "Order is placed successfully!", "PAGES.AUTH.CUSTOMERS.THANK_YOU_PAGE.SUBTEXT": "A confirmation email has been sent to the customer.", "PAGES.AUTH.DIFFERENT_COUNTRY_NOTICE.ACTION": "Go to METRO { country2 }", "PAGES.AUTH.DIFFERENT_COUNTRY_NOTICE.TEXT": "You are trying to login using a <strong>{ country2 }</strong> account.", "PAGES.AUTH.DIFFERENT_COUNTRY_NOTICE.TEXT2": "Please choose how you would like to proceed:", "PAGES.AUTH.ELEMENTS.CHECK_SPAM": "Please check your Spam folder or", "PAGES.AUTH.ELEMENTS.DID_NOT_GET_EMAIL_QUESTION": "Did not get our email?", "PAGES.AUTH.ELEMENTS.RETURN_TO_LOGIN": "Return to login", "PAGES.AUTH.EMAIL_VALIDATION.ELEMENTS.NOTIFICATION": "Please enter the E-Mail address you registered with. We will resend your confirmation E-Mail.", "PAGES.AUTH.EMAIL_VALIDATION.ELEMENTS.RESEND_EMAIL_BUTTON": "Send validation email", "PAGES.AUTH.EMAIL_VALIDATION.TITLE": "Resend Confirmation E-Mail", "PAGES.AUTH.EMAIL.LOGIN.OR.REGISTER.SUB.TITLE": "Please enter your <b>email address</b> to proceed, you can use your email to <b>login or sign up.</b>", "PAGES.AUTH.EMAIL.LOGIN.REDIRECT.SUB.TITLE": "Looks like you already have an account, please wait while we redirect you or <a>Login here</a>.", "PAGES.AUTH.EMPLOYEE.ALERT.IMPERSONATION_STILL_ACTIVE.CONTENT": "You’re still impersonating { user }", "PAGES.AUTH.EMPLOYEE.ALERT.IMPERSONATION_STILL_ACTIVE.TITLE": "Impersonation Active", "PAGES.AUTH.EMPLOYEE.CHECKOUT_CART_PAGE.BUTTON.CHECKOUT_BEHALF_CUSTOMER": "Checkout on customer’s behalf", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.ALERT.ERROR.DESCRIPTION.TEXT": "An account already exists with this email: {email} ", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.ALERT.ERROR.DESCRIPTION.TEXT1": "Something went wrong, please try again.", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.ALERT.ERROR.TITLE.TEXT": "Failed to create account:", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.ALERT.SUCCESS.DESCRIPTION.TEXT": "You can now place an order on behalf of this customer", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.ALERT.SUCCESS.TITLE.TEXT": "Account created successfully:", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.DESCRIPTION.TEXT": "Please enter the customer’s email address to create their digital account. A confirmation email will be sent to the customer.", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.EXIT.BUTTON.TEXT": "Cancel", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.FOOTER_NOTICE.TEXT": "By clicking “Confirm & Impersonate” a digital account will be created on behalf of the customer. The customer will receive an email notification confirming account creation. This email will be used as the customer username.", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.SUBMIT.BUTTON.TEXT": "Confirm & Impersonate", "PAGES.AUTH.EMPLOYEE.CREATE_DIGITAL_ACCOUNT.DAILOG.TITLE.TEXT": "Create a customer digital account", "PAGES.AUTH.EMPLOYEE.EXTEND.IMPERSONATION.SESSION.DAILOG.CONTENT": "Your session is about to end, please select continue if you would like to proceed further with this customer.", "PAGES.AUTH.EMPLOYEE.EXTEND.IMPERSONATION.SESSION.DAILOG.CONTINUE.BUTTON.TEXT": "Continue", "PAGES.AUTH.EMPLOYEE.EXTEND.IMPERSONATION.SESSION.DAILOG.INITIAL.TITLE": "Customer Impersonation Timeout", "PAGES.AUTH.EMPLOYEE.FLYOUT.ACCOUNT.TEXT": "Employee account", "PAGES.AUTH.EMPLOYEE.FLYOUT.CUSTOMER_COMPANY.TEXT": "Customer company", "PAGES.AUTH.EMPLOYEE.FLYOUT.EMPLOYEE.TEXT": "Employee", "PAGES.AUTH.EMPLOYEE.FLYOUT.IMPERSONATING.TEXT": "Customer Impersonation", "PAGES.AUTH.EMPLOYEE.FLYOUT.ONLINE_SHOP_INVOICES.TEXT": "Order History", "PAGES.AUTH.EMPLOYEE.FLYOUT.PERSONAL_ACCOUNT.TEXT": "Customer account", "PAGES.AUTH.EMPLOYEE.FLYOUT.STOP_IMPERSONATION.TEXT": "Stop impersonating customer", "PAGES.AUTH.EMPLOYEE.IMPERSONATE_PAYMENT_ERROR.DAILOG.INITIAL.BUTTON.TEXT": "Start with a customer", "PAGES.AUTH.EMPLOYEE.IMPERSONATE_PAYMENT_ERROR.DAILOG.INITIAL.CONTENT": "There was an error confirming this order as another impersonation session was active.", "PAGES.AUTH.EMPLOYEE.IMPERSONATE_PAYMENT_ERROR.DAILOG.INITIAL.TITLE": "Failed to complete your order.", "PAGES.AUTH.EMPLOYEE.ORDER_HISTORY_PAGE.DIALOG.BODY_TEXT.IMPERSONATION_ACTIVE_ALERT": "You tried leaving the customer order history page while still impersonating.", "PAGES.AUTH.EMPLOYEE.ORDER_HISTORY_PAGE.DIALOG.HEADING.IMPERSONATION_ACTIVE_ALERT": "Impersonation still active", "PAGES.AUTH.EMPLOYEE.ORDER_REVIEW_PAGE.DIALOG.FAILED_ORDER.BODY": "There was an error confirming this order as another impersonation session was active.", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.BUTTON.TEXT": "Share payment link", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.EMPLOYEE_SIGN_OUT.BUTTON.TEXT": "Employee sign out", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.ERROR.CONTENT": "There is an error creating the link. Please return and try sharing the link again.", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.ERROR.TITLE": "Failed to create the payment link", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.INITIAL.CONTENT": "You are about to share the payment process for this cart with the following customer", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.INITIAL.FOOTER_NOTICE": "Click “Share payment link” to send an email notification to the customer to complete the payment process.", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.INITIAL.SUBMIT_BUTTON.TEXT": "Confirm & Share", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.INITIAL.TITLE": "Share payment link", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.RETURN_TO_CART.BUTTON.TEXT": "Return to Cart", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.RETURN_TO_PAYMENT.BUTTON.TEXT": "Return to Payment", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.START_WITH_NEW_CUSTOMER.BUTTON.TEXT": "Start with a new customer", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.SUCCESS.CONTENT": "The customer will receive an email to complete the payment process for this order.", "PAGES.AUTH.EMPLOYEE.SHARE_PAYMENT_LINK.DAILOG.SUCCESS.TITLE": "Payment link shared successfully! ", "PAGES.AUTH.KVK.ERROR.KVK_NUMBER_NOT_VALID.MESSAGE": "We were unable to validate your KvK number,\nplease make sure you have entered the correct KvK number.", "PAGES.AUTH.KVK.ERROR.KVK_NUMBER_NOT_VALID.TITLE": "Validation was unsuccessful.", "PAGES.AUTH.LOGIN_FLOW_TYPE.QUESTION": "Do you want to skip password?", "PAGES.AUTH.LOGIN_FLOW_TYPE.QUESTION.INFO": "You can login using a secure login link instead of your password.", "PAGES.AUTH.LOGIN_FLOW_TYPE.QUESTION.PASSWORD": "Login using password", "PAGES.AUTH.LOGIN_FLOW_TYPE.QUESTION.PASSWORDLESS": "Login with a secure link", "PAGES.AUTH.LOGIN_FLOW_TYPE.TITLE": "<PERSON><PERSON>", "PAGES.AUTH.LOGIN_QUESTION": "Do you already have an existing account?", "PAGES.AUTH.LOGIN.MY_METRO_NOTIFICATION.TEXT": "You can use email or account name and password from your existing myMETRO account to login!", "PAGES.AUTH.LOGIN.NOTICE": "Direct advertising can be sent to you by email. You can object to this via the link in the email. There are no costs beyond the basic tariff for the objection.", "PAGES.AUTH.LOGIN.TITLE": "Log In", "PAGES.AUTH.LOGOUT.TITLE": "Logout | Metro Markets", "PAGES.AUTH.MAINTENANCE.ERROR": "This function is temporarily unavailable. To improve your experience, we are currently undergoing scheduled maintenance. Please try again in one hour.", "PAGES.AUTH.NOTIFICATIONS.ACCOUNT_VALIDATION": "A confirmation will be sent to your registered email.", "PAGES.AUTH.NOTIFICATIONS.FAILED_LOGIN": "Login or password is incorrect. Please try again.", "PAGES.AUTH.NOTIFICATIONS.RESEND_ACCOUNT_VALIDATION": "If you didn't validate your email on registration please check your mailbox or", "PAGES.AUTH.NOTIFICATIONS.RESEND_ACCOUNT_VALIDATION_LINK": "resend the validation email", "PAGES.AUTH.NOTIFICATIONS.SUBMIT_ERROR.TAXNUMBERNOTUNIQUE": "The company with this tax identification code (CIF/NIF/NIE) is already registered. \nPlease enter the number again or <a href=\"http://support-es.metro-marketplace.eu/es/support/home\" target=\"_blank\">contact our customer service.</a>\n", "PAGES.AUTH.NOTIFICATIONS.SUBMIT_ERROR.VATNUMBERNOTUNIQUE": "A company is already registered with this VAT number. Please make sure you enter the correct code or contact our <a href=\"https://support-it.metro-marketplace.eu/it/support/home \" target=\"_blank\">customer service</a>.", "PAGES.AUTH.NOTIFICATIONS.SUCCESS_REGISTRATION": "Account was successfully created!", "PAGES.AUTH.PASSWORD_RESTORATION.ELEMENTS.FORM_NOTIFICATION": "Please enter email address you used on registration.\n We will send you restore password instructions.", "PAGES.AUTH.PASSWORD_RESTORATION.ELEMENTS.RESTORE_PASSWORD_BUTTON": "Send Instruction", "PAGES.AUTH.PASSWORD_RESTORATION.TITLE": "Forgot password?", "PAGES.AUTH.PASSWORDLESS_INTERMEDIATE.TITLE": "Redirecting, please wait.", "PAGES.AUTH.PASSWORDLESS_SIGNUP.ADD_BUSINESS_DETAILS.BUTTON": "Submit", "PAGES.AUTH.PASSWORDLESS_SIGNUP.TITLE": "Register", "PAGES.AUTH.PAYMENT_LINK.PREPARING.CART.MESSAGE_TEXT": "We are currently preparing your cart.", "PAGES.AUTH.PAYMENT_LINK.WAITING.MESSAGE_TEXT": "This may take a moment…", "PAGES.AUTH.REGISTER_EMAIL.TITLE": "Create a new account", "PAGES.AUTH.REGISTER.AGREE_TO_TNC": "Agree with Terms and Conditions", "PAGES.AUTH.REGISTER.COMPANY_NAMES.PRIVACY.PREFIX": "Makro and Metro's", "PAGES.AUTH.REGISTER.COMPANY_NAMES.PRIVACY.SUFFIX": " policies will apply.", "PAGES.AUTH.REGISTER.CONDITIONS.LINK_TEXT": "conditions of use for the marketplace", "PAGES.AUTH.REGISTER.CONDITIONS.PREFIX": "; as well as to the ", "PAGES.AUTH.REGISTER.CONDITIONS.SUFFIX": " and of ", "PAGES.AUTH.REGISTER.CREATE_BUSINESS": "I would like to add my business details", "PAGES.AUTH.REGISTER.EXTRA_LINKS": "<a href=\"{CUSTOMER_SUPPORT}\" target=\"_blank\">Customer Support</a>", "PAGES.AUTH.REGISTER.HAVE_BUSINESS": "Do you have a business?", "PAGES.AUTH.REGISTER.HAVE_BUSINESS_CTA": "If you have a business you can add it to your account", "PAGES.AUTH.REGISTER.HAVE_BUSINESS_TOOLTIP": "You can always add a business to your account later.", "PAGES.AUTH.REGISTER.LEGAL_TEXT": "By clicking <b>Register</b>, I accept the  <a [routerLink]=\"tnc_link\">Terms of use and purchase</a>.  The applicable privacy policy can be found here: <a href=\"{ privacy_link }\">Privacy Policy</a>", "PAGES.AUTH.REGISTER.LEGAL_TEXT.COOKIES_TEXT": "cookie information policies", "PAGES.AUTH.REGISTER.LEGAL_TEXT.COOKIES.POSTFIX": " of Makro and Metro will apply.", "PAGES.AUTH.REGISTER.LEGAL_TEXT.COOKIES.PREFIX": "of Makro and Metro will apply.", "PAGES.AUTH.REGISTER.LEGAL_TEXT.METRO.PREFIX": "and", "PAGES.AUTH.REGISTER.LEGAL_TEXT.PRIVACY.LINK_TEXT": "The privacy policies of Makro and Metro will apply.", "PAGES.AUTH.REGISTER.LEGAL_TEXT.PRIVACY.PREFIX": "; as well as to the", "PAGES.AUTH.REGISTER.LEGAL_TEXT.PWD": "You can always add a password to your account after placing your order or you can log in next time with just your email using a secure login link.", "PAGES.AUTH.REGISTER.LEGAL_TEXT.TNC": "By clicking one of the register options above, I accept the <a href=\"{ MY_METRO_AGB }\" target=\"_blank\">myMETRO Terms and Conditions</a>, the <a href=\"{ COMPANION_APP_AGB }\" target=\"_blank\">Terms and Conditions for the use of the Companion App</a> and, for the use of the Onlineshop, the <a href=\"{ METRO_MARKETS_AGB }\" target=\"_blank\">Terms and Conditions of METRO Markets GmbH</a>. The cancellation policy for consumers can be found <a href=\"{ CANCELLATION_POLICY }\" target=\"_blank\">here</a>. You are not obliged to make any payment for registration. The applicable data protection provisions can be found <a href=\"{ DATA_PROTECTION }\" target=\"_blank\">here</a>.", "PAGES.AUTH.REGISTER.LEGAL_TEXT.TNC.LINK_TEXT": "Digital Account Terms of Use and Registration", "PAGES.AUTH.REGISTER.LEGAL_TEXT.TNC.PREFIX": "The Digital Account is owned by Makro Wholesale Self-Service and Metro Markets. We process your personal data together as co-responsible for the processing for the activities linked to it. By clicking \"Register\", you agree to the", "PAGES.AUTH.REGISTER.MAKRO.LINK_TEXT": "Makro.es", "PAGES.AUTH.REGISTER.MAKRO.LINK_TEXT_1": "<PERSON><PERSON><PERSON>", "PAGES.AUTH.REGISTER.MAKRO.SUFFIX": "for the use of", "PAGES.AUTH.REGISTER.MARTKETING_EMAIL_NOTICE": "METRO Markets GmbH can send you direct marketing emails for similar products and services of the online marketplace. You can object to this via the link in the email. There are no costs over and above the basic tariff for the objection.", "PAGES.AUTH.REGISTER.SAVE.LEGAL_ADDRESS": "Use the legal address as <b>shipping</b> and <b>billing address</b>", "PAGES.AUTH.REGISTER.TITLE": "Create a new account", "PAGES.AUTH.REGISTRATION_QUESTION": "New to METRO?", "PAGES.AUTH.REGISTRATION_TYPE.BUSINESS_METRO.TITLE": "Register a business with a METRO card", "PAGES.AUTH.REGISTRATION_TYPE.BUSINESS.TITLE": "Register a business account", "PAGES.AUTH.REGISTRATION_TYPE.PRIVATE.TITLE": "Register a private account", "PAGES.AUTH.REGISTRATION_TYPE.SUBTITLE": "Select your profile according to your needs", "PAGES.AUTH.REGISTRATION_TYPE.TITLE": "Register", "PAGES.AUTH.REGISTRATION.ADD_BUSINESS_ACCOUNT.LABEL": "I want to add a business to my account now.", "PAGES.AUTH.REGISTRATION.ADD_BUSINESS_DETAILS.INFO_TEXT": "Fill in the following information to complete your business registration.", "PAGES.AUTH.REGISTRATION.ADD_BUSINESS_DETAILS.TITLE": "Add business details", "PAGES.AUTH.REGISTRATION.ADD_BUSINESS_DETAILS.TOOLTIP_INFO_TEXT": "If the business details do not match the shipping or billing address, you are able to correct this information at a later stage.", "PAGES.AUTH.REGISTRATION.ALERT.EMAIL_EXISTS.TITLE": "This email is already in use. Please log in instead.", "PAGES.AUTH.REGISTRATION.ALERT.SUCCESS.MESSAGE": "A confirmation will be sent to your registered email.", "PAGES.AUTH.REGISTRATION.ALERT.SUCCESS.TITLE": "Account was successfully created!", "PAGES.AUTH.REGISTRATION.BRANCH_SELECTION.BRANCH_NUMBER": "Branch Number", "PAGES.AUTH.REGISTRATION.BRANCH_SELECTION.INFO_TEXT": "Please choose the business you want to associate with your account.", "PAGES.AUTH.REGISTRATION.BRANCH_SELECTION.RSIN": "RSIN", "PAGES.AUTH.REGISTRATION.BRANCH_SELECTION.TITLE": "Business Registration", "PAGES.AUTH.REGISTRATION.BUSINESS.TITLE": "Register as New Business", "PAGES.AUTH.REGISTRATION.CHECK_KVK.INFO_TEXT": "Please enter your KvK number so we can confirm your business registration.", "PAGES.AUTH.REGISTRATION.CHECK_KVK.TITLE": "Business Registration", "PAGES.AUTH.REGISTRATION.CHECK_TAX_ID.TITLE": "Business Registration", "PAGES.AUTH.REGISTRATION.CHECK_VAT.TITLE": "VAT ID", "PAGES.AUTH.REGISTRATION.CONSUMER.TITLE": "Register as a Consumer", "PAGES.AUTH.REGISTRATION.CREATE.LINK": "Create", "PAGES.AUTH.REGISTRATION.LEGAL_LINK.PRIVACY_POLICY.TEXT": "The applicable privacy policy can be found here:", "PAGES.AUTH.REGISTRATION.LEGAL_LINK.TERMS_AND_CONDITIONS.TEXT": "By clicking Register, I accept the", "PAGES.AUTH.REGISTRATION.METRO_CARD.CONFIRMATION_TEXT": "I confirm that I'm an entrepreneur according to § 14 BGB", "PAGES.AUTH.REGISTRATION.METRO_CARD.METRO_CARD_NUMBER_HELP_LINK": "Where to find?", "PAGES.AUTH.REGISTRATION.METRO_CARD.TITLE": "Register with METRO Card", "PAGES.AUTH.REGISTRATION.PASSWORDLESS_BUSINESS_DETAILS.TITLE": "Add business details", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE_PRIVATE_OR_BUSINESS.TITLE": "Select your profile", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.ADD_BUSINESS.INFO": "You can also add your business later on at any moment.", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.ADD_METRO.INFO": "If you have a Metro card select the following option", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.BUSINESS.CUSTOMER": "Continue as a new business customer", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.BUSINESS.CUSTOMER.TITLE": "Business Customer", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.METRO.CUSTOMER": "Continue with existing METRO card", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.PRIVATE_OR_BUSINESS_QUESTION": "Do you want to place order on behalf of a private account or business account? ", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.PRIVATE_OR_BUSINESS_QUESTION.LINE_1": "Do you want to place order on behalf of a private account", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.PRIVATE_OR_BUSINESS_QUESTION.LINE_2": "or business account?", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.PRIVATE.CUSTOMER": "Continue as private customer", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.PRIVATE.CUSTOMER.TITLE": "Private customer", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.QUESTION": "Do you have a METRO card?", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.QUESTION.HAVE_CARD": "Yes, continue with METRO card", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.QUESTION.INFO": "If you already have a METRO card for your business, you don’t need to register it from scratch.", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.QUESTION.NO_CARD": "NO, continue without METRO card", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.SELECT_PROFILE_TITLE": "Select your profile", "PAGES.AUTH.REGISTRATION.REGISTRATION_TYPE.TITLE": "METRO card", "PAGES.AUTH.REGISTRATION.TIER_TWO_METRO.TITLE": "Do you already have a METRO Card?", "PAGES.AUTH.REGISTRATION.TITLE": "Create a Digital Account", "PAGES.AUTH.REGISTRATION.TYPE_BUSINESS_TITLE": "Registration as business customer with METRO Card", "PAGES.AUTH.REGISTRATION.TYPE_CONSUMER_TITLE": "Registration as private customer", "PAGES.AUTH.REGISTRATION.TYPE_NEW_BUSINESS_TITLE": "Registration as new business customer", "PAGES.AUTH.REGISTRATION.TYPES.BUSINESS": "As New Business", "PAGES.AUTH.REGISTRATION.TYPES.BUSINESS_DESCRIPTION": "Without METRO Card", "PAGES.AUTH.REGISTRATION.TYPES.COMING_SOON": "Will be available soon", "PAGES.AUTH.REGISTRATION.TYPES.CONSUMER": "As a Consumer", "PAGES.AUTH.REGISTRATION.TYPES.METRO_CARD": "With METRO Card", "PAGES.AUTH.REGISTRATION.VALIDATION.EMAIL_EXISTS": "Your account already exists. Please login to your account in the next step.", "PAGES.AUTH.REMEMBER_PASSWORD_QUESTION": "Remember your password?", "PAGES.AUTH.SECURE_LOGIN_NOTICE.CHECK_INBOX": "Please check your inbox!", "PAGES.AUTH.SECURE_LOGIN_NOTICE.EMAIL_SENT": "Login link sent successfully.", "PAGES.AUTH.SECURE_LOGIN_NOTICE.EMAIL_SENT_CONTENT": "We have sent a secure login link to your email, please check your inbox.", "PAGES.AUTH.SECURE_LOGIN_NOTICE.LINK_NOTE": "<PERSON> might take a few seconds to arrive.<br>", "PAGES.AUTH.SECURE_LOGIN_NOTICE.LINK_NOTE_1": "<PERSON> might take a few seconds to arrive.", "PAGES.AUTH.SECURE_LOGIN_NOTICE.NOTICE.MESSAGE": "Make sure to open the link in the same device and browser you used to request the link to login.", "PAGES.AUTH.SECURE_LOGIN_NOTICE.NOTICE.TITLE": "Please keep device same", "PAGES.AUTH.SECURE_LOGIN_NOTICE.RESEND_LINK": "I didn’t get the secure login link", "PAGES.AUTH.SECURE_LOGIN_NOTICE.SUBHEADER": "We have sent an email with a secure login link to:\n<b>{email}</b>", "PAGES.AUTH.SECURE_LOGIN_NOTICE.TITLE": "Check your inbox", "PAGES.AUTH.SUBSIDIARIES.THANK_YOU_PAGE.HEADING": "Order request successfully submitted!", "PAGES.AUTH.VALIDATE_EMAIL.TITLE": "Login / Register", "PAGES.BRAND_PLP.CATEGORY_COUNT": "from {count} categories", "PAGES.BRAND_PLP.PRODUCT_COUNT": "Discover {count} products", "PAGES.CHECKOUT.ADDRESS.CONFIRM_ACCOUNT_CREDENTIALS.DAILOG.CONTENT.TEXT": "In order to change account information for this order you must first confirm your account credentials for: <b>{ email }</b>", "PAGES.CHECKOUT.ADDRESS.CONFIRM_ACCOUNT_CREDENTIALS.DAILOG.TITLE.TEXT": "Confirm account credentials", "PAGES.LEGAL.ABOUT_US.CONTENT_PARA1": "Als METRO Markets kombinieren wir innovative Technologie mit Leidenschaft und jahrzehntelanger Erfahrung im Bereich B2B.", "PAGES.LEGAL.ABOUT_US.CONTENT_PARA2": "Unsere Entwicklung: Ein One-Stop-Shop, der Seller mit Profikunden verbindet und den Geschäftsbeziehungen zwischen Einkaufs- und Verkaufsseite eine Plattform bietet. Mit der Bereitstellung unseres Online-B2B-Marktplatzes möchten wir die Zukunft des E-Commerce für Unternehmen verändern. Durch die komfortablen Einkaufsmöglichkeiten sollen sich unsere Kunden auf das konzentrieren, was sie am besten können – andere zu begeistern!", "PAGES.LEGAL.ABOUT_US.ROUTE": "About us", "PAGES.LEGAL.AGB.ROUTE": "Terms of Use and Purchase", "PAGES.LEGAL.CANCELLATION_POLICY.ROUTE": "Cancellation policy", "PAGES.LEGAL.CONDITION_OF_SALE": "General condition of sale", "PAGES.LEGAL.COOKIES.ROUTE": "Cookies", "PAGES.LEGAL.DATA_PROTECTION.ROUTE": "Privacy Policy", "PAGES.LEGAL.DISPOSAL_AND_ENV.ROUTE": "Disposal and Environmental Regulations", "PAGES.LEGAL.ENVIRONMENTAL_PROVISION.ROUTE": "Environmental provisions", "PAGES.LEGAL.IMPRINT.ROUTE": "Imprint", "PAGES.LEGAL.PRIVACY_POLICY.NEW.ROUTE": "Privacy policy", "PAGES.LEGAL.PRIVACY_POLICY.ROUTE": "Privacy policy", "PAGES.LEGAL.REGISTRATION_PRIVACY_POLICY": "Registration Privacy Policy", "PAGES.LEGAL.TERMS_AND_CONDITIONS.NEW.ROUTE": "Terms of Use and Purchase", "PAGES.LEGAL.TERMS_AND_CONDITIONS.ROUTE": "Terms of Use and Purchase", "PAGES.PRODUCT.LABEL.BY": "by", "payment_link_shared.best_regards": "Best regards,", "payment_link_shared.customer": "Customer", "payment_link_shared.greetings": "Dear {customer_full_name},", "payment_link_shared.make_payment_call_to_action": "To complete your order please make the payment.", "payment_link_shared.order_is_ready": "Your order is ready, please click on the button below to complete the payment of your order.", "payment_link_shared.order_placed_at": "Order Time & Date", "payment_link_shared.pay_and_complete_button": "Pay and complete your order", "payment_link_shared.products_not_reserved_notice": "Please remember the products are not reserved until payment is made so make the payment as soon as possible to make sure products don’t go out of stock.", "payment_link_shared.subject": "Your order is waiting for you!", "payment_link_shared.your_metro_customer_service": "Your METRO customer service", "PRODUCT.CARD.A11Y.ADD_TO_CART": "Add {quantity} {productName} to cart", "PRODUCT.CARD.A11Y.STRIKETHROUGH.PRICE": "Price before discount", "PRODUCT.CARD.OFFER.DISCOUNT": "Discount", "PRODUCTS_GRID.PRODUCT": "{count} {count, plural, =1{product} other{products}}", "PURCHASES_GROUP_TITLE": "Purchases", "QUANTITY_PICKER.A11Y.UPDATED_QUANTITY": "Quantity updated to", "QUANTITY_PICKER.BUTTON.DECREASE_QUANTITY": "Decrease quantity", "QUANTITY_PICKER.BUTTON.DELETE_ITEM": "Remove the item from the cart", "QUANTITY_PICKER.BUTTON.INCREASE_QUANTITY": "Increase quantity", "REACT_404_PAGE.CTA": " Back to homepage", "REACT_404_PAGE.HEADER_TEXT": "To METRO homepage", "REACT_404_PAGE.TEXT": "Please go back to the home page or use the search to continue", "REACT_404_PAGE.TITLE": "Sorry, the page could not be found.", "REACT.CHECKOUT.ADDRESS.NIF_FIELD.DESCRIPTION": "Add your NIF to have it included on the order invoice.", "REACT.CHECKOUT.ADDRESS.NIF_FIELD.TITLE": "NIF Invoice", "REACT.CHECKOUT.ADDRESS.PHONE_NUMBER.TITLE": "Contact Details", "REACT.CHECKOUT.APP.CHECKOUT.COMMON.PHONE.VALIDATION": "A valid phone number is required. A valid phone number should  between 6 and 15 digits.", "REACT.CHECKOUT.APP.CHECKOUT.COMMON.PHONE.VALIDATION.WITH_COUNTRYCODE": "A valid phone number, between {min} and {max} digits including country code, is required.", "REACT.CORE.ALL_PAGES.PRICE_TYPE.VAT.LABEL": "({type} VAT)", "REACT.CORE.ALL_PAGES.PRICE_TYPE.VAT.LABEL.CONSUMER": "with", "REACT.CORE.ALL_PAGES.PRICE_TYPE.VAT.LABEL.OTHER": "without", "REACT.HOME.HOME_PAGE.TOP_PRODUCTS.EXPLORE_LABEL": "Explore {other}", "REACT.HOME.HOME_PAGE.TOP_PRODUCTS.EXPLORE_LABEL.OTHER": "All Products", "REACT.PAGES.AUTH.ALERT.IDAM.OUTAGE": "We're sorry, our <b>login/registration services</b> are currently <b>out of service</b>, please try again later.", "REACT.REGULAR.ERROR_MESSAGES.PHONE.MAXLENGTH": "The phone number should consist of 6 to 15 numbers", "REACT.REGULAR.ERROR_MESSAGES.PHONE.MIN_MAX_LENGTH.WITH_COUNTRYCODE": "The phone number should consist of {min} to {max} numbers including country code.", "REACT.REGULAR.ERROR_MESSAGES.PHONE.MINLENGTH": "The phone number should consist of 6 to 15 numbers", "REACT.REGULAR.ERROR_MESSAGES.PHONE.REQUIRED": "Enter a telephone number", "REGION_SELECTOR_DELIVERY_OFFERS_TEXT": "Show only deliverable offers in {region} ", "REGULAR.ACCEPT": "Accept", "REGULAR.ACCOUNT_STATUSES.BLOCKED": "Blocked", "REGULAR.ACCOUNT_STATUSES.VALIDATED": "Validated", "REGULAR.ACCOUNT_STATUSES.VALIDATION": "Waiting for validation", "REGULAR.AND": "and", "REGULAR.BLOCKED_VAT_ID.ALERT.MESSAGE": "Please enter your company VAT ID, not the VAT ID of a buying group.", "REGULAR.BLOCKED_VAT_ID.ALERT.TITLE": "Invalid VAT ID", "REGULAR.BUSINESS_INFO": "Business Information", "REGULAR.CANCEL": "Cancel", "REGULAR.CHANGE": "Change", "REGULAR.CONTACT_PERSON": "Company Representative", "REGULAR.CONTINUE": "Continue", "REGULAR.CREATE_ACCOUNT": "Create an account", "REGULAR.CS.FAQ_Headings_About_my_order": "About My Order", "REGULAR.CS.FAQ_Headings_Customer Account_and_Registration": "Customer Account & Registration", "REGULAR.CS.FAQ_Headings_Data _Protection_&_Privacy": "Data Protection & Privacy", "REGULAR.CS.FAQ_Headings_General Information": "General Information", "REGULAR.CS.FAQ_Headings_Payment": "Payment", "REGULAR.CS.FAQ_Headings_Returns_and_Refunds": "Returns & Refunds", "REGULAR.CS.FAQ_Headings_Services": "Services", "REGULAR.CS.FAQ_Headings_Shipping_and_Tracking": "Shipping & Tracking", "REGULAR.DATA_PROTECTION_DECLARATION": "Privacy Policy", "REGULAR.EMAIL": "E-Mail", "REGULAR.ERROR_MESSAGES.BUSINESSNAME.REQUIRED": "Enter a company name", "REGULAR.ERROR_MESSAGES.BUYER.AGE": "You must be at least 18 years old.", "REGULAR.ERROR_MESSAGES.CARDNUMBER": "Not valid", "REGULAR.ERROR_MESSAGES.CITY.REQUIRED": "Enter the city/town", "REGULAR.ERROR_MESSAGES.COMPANYNAME.REQUIRED": "Enter a company name", "REGULAR.ERROR_MESSAGES.DATE_OF_BIRTH.INVALID": "Please enter a valid date of birth.", "REGULAR.ERROR_MESSAGES.EMAIL.INVALID": "You have entered an invalid email / Please enter a valid email", "REGULAR.ERROR_MESSAGES.ENTREPRENEURSTATUSCONFIRMATION.MANDATORY": "Please, confirm that you're an entrepreneur according to § 14 BGB", "REGULAR.ERROR_MESSAGES.FIRSTNAME.REQUIRED": "Enter a name", "REGULAR.ERROR_MESSAGES.HOUSENUMBER.REQUIRED": "Enter a house number", "REGULAR.ERROR_MESSAGES.LASTNAME.REQUIRED": "Enter a last name", "REGULAR.ERROR_MESSAGES.LEGALADDRESSPOSTALCODE.PATTERN": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.LEGALADDRESSPROVINCE.PATTERN": "Please make sure you fill in province abbreviations like BG, AN, CN…", "REGULAR.ERROR_MESSAGES.MANDATORY": "The field cannot be empty", "REGULAR.ERROR_MESSAGES.MAXLENGTH": "The field should consist maximum { requiredLength } symbols", "REGULAR.ERROR_MESSAGES.MAXLENGTH_ES": "The field should consist maximum { requiredLength } symbols", "REGULAR.ERROR_MESSAGES.MINLENGTH": "Requires at least { requiredLength } symbols", "REGULAR.ERROR_MESSAGES.MINLENGTH_ES": "Requires at least { requiredLength } symbols", "REGULAR.ERROR_MESSAGES.NEW_PASSWORD_CONFIRMATION.EQUAL_WITH_NEW": "New passwords don't match", "REGULAR.ERROR_MESSAGES.NEW_PASSWORD.EQUAL_WITH_OLD": "The new password should not be equal to the old password", "REGULAR.ERROR_MESSAGES.NEWEMAIL.PATTERN": "Not valid", "REGULAR.ERROR_MESSAGES.NEWPASSWORD.PATTERN": "All characters are allowed except for space", "REGULAR.ERROR_MESSAGES.NOT_ONLY_SYMBOLS": "Looks like you entered not valid symbols", "REGULAR.ERROR_MESSAGES.PASSWORD.PATTERN": "All characters are allowed except for space", "REGULAR.ERROR_MESSAGES.PASSWORD.SECURITY.CAPITAL": "At least one Capital letter", "REGULAR.ERROR_MESSAGES.PASSWORD.SECURITY.DIGIT": "At least one digit", "REGULAR.ERROR_MESSAGES.PASSWORD.SECURITY.LENGTH": "Requires 8-50 characters", "REGULAR.ERROR_MESSAGES.PASSWORD.SECURITY.PATTERN": "The password must contain: minimum 8 characters, only latin alphabet, a minimum of 1 upper case letter and 1 special character.", "REGULAR.ERROR_MESSAGES.PASSWORD.SECURITY.SPECIAL": "At least one special character", "REGULAR.ERROR_MESSAGES.PASSWORDSEQUALITY": "The new password should not be equal to the old password", "REGULAR.ERROR_MESSAGES.PASSWORDSMATCH": "New passwords don't match", "REGULAR.ERROR_MESSAGES.PATTERN": "Looks like you entered invalid symbols", "REGULAR.ERROR_MESSAGES.PHONE.MAXLENGTH": "The phone number should consist of 6 to 15 numbers", "REGULAR.ERROR_MESSAGES.PHONE.MINLENGTH": "The phone number should consist of 6 to 15 numbers", "REGULAR.ERROR_MESSAGES.PHONE.PATTERN": "The phone number should consist only numbers", "REGULAR.ERROR_MESSAGES.PHONE.REQUIRED": "Enter a telephone number", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH": "The postcode should consist of 15 symbols", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_DE": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_ES": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_FR": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_IT": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_NL": "The postcode should consist of 4-digit numbers and 2 letters.", "REGULAR.ERROR_MESSAGES.POSTALCODE.MAXLENGTH_PT": "The postcode should consist of 4-digit numbers, a hyphen and 3-digit numbers.", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_DE": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_ES": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_FR": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_IT": "The postcode should consist of 5-digit numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_NL": "The postcode should consist of 4-digit numbers and 2 letters.", "REGULAR.ERROR_MESSAGES.POSTALCODE.MINLENGTH_PT": "The postcode should consist of 4-digit numbers, a hyphen and 3-digit numbers.", "REGULAR.ERROR_MESSAGES.POSTALCODE.PATTERN": "The postal code should consist only numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.PATTERN_DE": "The postal code should consist only numbers", "REGULAR.ERROR_MESSAGES.POSTALCODE.PATTERN_ES": "Invalid zip code", "REGULAR.ERROR_MESSAGES.POSTALCODE.PATTERN_PT": "The address you have entered is inside an area where we currently don't deliver. Please add a new shipping address.", "REGULAR.ERROR_MESSAGES.POSTALCODE.REQUIRED": "Enter a postal code", "REGULAR.ERROR_MESSAGES.REQUIRED": "The field cannot be empty", "REGULAR.ERROR_MESSAGES.SECURITY": "The password must meet the following security requirements", "REGULAR.ERROR_MESSAGES.STREET.REQUIRED": "Enter the street", "REGULAR.ERROR_MESSAGES.TAXIDENTIFICATION.PATTERN": "Please make sure your VAT ID contains 9 digits.", "REGULAR.ERROR_MESSAGES.TAXNUMBER.REQUIRED": "Enter a tax number", "REGULAR.ERROR_MESSAGES.VATNUMBER.PATTERN": "Please make sure your VAT ID contains country code (DE) and 9 digits.", "REGULAR.ERROR_MESSAGES.VERIFICATIONPOSTALCODE.LENGTH": "Not valid", "REGULAR.ERROR_MESSAGES.VERIFICATIONPOSTALCODE.PATTERN": "Not valid", "REGULAR.FIELD_SECTION.TIER_TWO_REGISTRATION.LEGAL_ADDRESS": "Business legal address", "REGULAR.FIELDS.ADDITIONAL_INFO_FOR_TIER_TWO_SIGNUP.LABEL": "Additional Information (Optional)", "REGULAR.FIELDS.ADDRESS_ADDITIONAL_INFORMATION.LABEL": "Additional information (optional)", "REGULAR.FIELDS.ADDRESS_ADDITIONAL_INFORMATION.PLACEHOLDER": "Unit, building, floor, ...", "REGULAR.FIELDS.ADDRESS_LINE_2.LABEL": "Address Line 2 (optional)", "REGULAR.FIELDS.ADDRESS_LINE_2.PLACEHOLDER": "Unit, building, floor, ...", "REGULAR.FIELDS.ADDRESS_PROVINCE.LABEL": "Province", "REGULAR.FIELDS.ADDRESS_SEARCH.LABEL": "Address search", "REGULAR.FIELDS.ADDRESS_SEARCH.NO_RESULTS": "No result for \"{searchTerm}\"", "REGULAR.FIELDS.ADDRESS_SEARCH.PLACEHOLDER": "Start typing to find your business address...", "REGULAR.FIELDS.BUSINESS.DETAILS.LABEL": "Business Name", "REGULAR.FIELDS.BUSINESS.NAME.HINT": "Add full company name including SRLS, SAS, SNC etc.", "REGULAR.FIELDS.BUSINESS.NAME.LABEL": "Business name", "REGULAR.FIELDS.BUSINESS.NAME.PLACEHOLDER": "Enter full company name e.g. Bistro S.R.L", "REGULAR.FIELDS.CFI.LABEL": "CFI/DNI/NIE", "REGULAR.FIELDS.CITY.LABEL": "City", "REGULAR.FIELDS.COMPANY_NAME.LABEL": "Business Name", "REGULAR.FIELDS.CONTINUE.WITH.METRO.OPTION.NO.LABEL": "NO, continue without METRO card", "REGULAR.FIELDS.CONTINUE.WITH.METRO.OPTION.YES.LABEL": "Yes, continue with METRO card", "REGULAR.FIELDS.COUNTRY_FRANCE.LABEL": "France", "REGULAR.FIELDS.COUNTRY_GERMANY.LABEL": "Germany", "REGULAR.FIELDS.COUNTRY_ITALY.LABEL": "Italy", "REGULAR.FIELDS.COUNTRY_NETHERLANDS.LABEL": "Netherlands", "REGULAR.FIELDS.COUNTRY_PORTUGAL.LABEL": "Portugal", "REGULAR.FIELDS.COUNTRY_SPAIN.LABEL": "Spain", "REGULAR.FIELDS.COUNTRY.LABEL": "Country", "REGULAR.FIELDS.CURRENT_EMAIL.LABEL": "Current Email", "REGULAR.FIELDS.CURRENT_PASSWORD": "Current password", "REGULAR.FIELDS.CUSTOMER_EMAIL.LABEL": "Customer <PERSON><PERSON>", "REGULAR.FIELDS.CUSTOMER_EMAIL.PLACEHOLDER": "Please enter the customer’s email address", "REGULAR.FIELDS.DAY_OF_DATE": "DD", "REGULAR.FIELDS.DEFAULT_BILLING.LABEL": "Set as default billing", "REGULAR.FIELDS.DEFAULT_SHIPPING.LABEL": "Set as default shipping", "REGULAR.FIELDS.E_INVOICE_NUMBER.LABEL": "E-invoice number (Optional)", "REGULAR.FIELDS.EMAIL_FOR_LOGIN.LABEL": "Email / Account name", "REGULAR.FIELDS.EMAIL_FOR_LOGIN.PLACEHOLDER": "Please enter email or myMETRO account name", "REGULAR.FIELDS.EMAIL.LABEL": "E-Mail", "REGULAR.FIELDS.EMAIL.PLACEHOLDER": "Please enter email", "REGULAR.FIELDS.FIRST_NAME.LABEL": "First Name", "REGULAR.FIELDS.HAS.METRO.LABEL": "If you already have a METRO Card for the Business, you don't need to register it from scratch", "REGULAR.FIELDS.HAS.METRO.OPTION.NO.LABEL": "No, I do not have a METRO Card yet.", "REGULAR.FIELDS.HAS.METRO.OPTION.YES.LABEL": "Yes, I already have a METRO Card.", "REGULAR.FIELDS.HORECA.LABEL": "Are you in Gastronomy / HoReCa business?", "REGULAR.FIELDS.HORECA.OPTION.NO.LABEL": "No", "REGULAR.FIELDS.HORECA.OPTION.YES.LABEL": "Yes", "REGULAR.FIELDS.HOUSE_NUMBER": "House Number", "REGULAR.FIELDS.KVK_NUMBER_FOR_TIER_TWO_SIGNUP.LABEL": "KvK Number", "REGULAR.FIELDS.LAST_NAME.LABEL": "Last Name", "REGULAR.FIELDS.METRO_CARD_NUMBER.LABEL": "Metro card number", "REGULAR.FIELDS.MONTH_OF_DATE": "MM", "REGULAR.FIELDS.NEW_EMAIL.LABEL": "New Email", "REGULAR.FIELDS.NEW_PASSWORD": "New password", "REGULAR.FIELDS.PASSWORD.HELP_TEXT": "Requires 8-50 characters", "REGULAR.FIELDS.PASSWORD.LABEL": "Password", "REGULAR.FIELDS.PASSWORD.PLACEHOLDER": "Please enter your password", "REGULAR.FIELDS.PASSWORD.RESTORATION_LINK": "Forgot your password?", "REGULAR.FIELDS.PEC_EMAIL.LABEL": "E-mail PEC", "REGULAR.FIELDS.PHONE.LABEL": "Phone Number (optional)", "REGULAR.FIELDS.POSTAL_CODE.LABEL": "Zip Code", "REGULAR.FIELDS.PROVINCE.LABEL": "Province", "REGULAR.FIELDS.PROVINCE.PLACEHOLDER": "Please add abbreviations like BG, AN, CN…", "REGULAR.FIELDS.REENTERED_NEW_PASSWORD": "Re-enter new password", "REGULAR.FIELDS.SET_AS_DEFAULT_BILLING.LABEL": "Set as default billing", "REGULAR.FIELDS.SET_AS_DEFAULT_SHIPPING.LABEL": "Set as default shipping", "REGULAR.FIELDS.SPANISH_TAX_NUMBER.LABEL": "CIF/NIF/NIE", "REGULAR.FIELDS.STREET": "Street", "REGULAR.FIELDS.TAX_NUMBER": "Tax Number", "REGULAR.FIELDS.TAX_NUMBER.LABEL": "Tax Number (optional)", "REGULAR.FIELDS.TIER_TWO_REGISTRATION.LEGAL_ADDRESS": "Business legal address", "REGULAR.FIELDS.VAT_ID_ITALY.LABEL": "VAT ID", "REGULAR.FIELDS.VAT_NUMBER_FOR_TIER_TWO_SIGNUP.LABEL": "VAT ID", "REGULAR.FIELDS.VAT_NUMBER.LABEL": "VAT Number (optional)", "REGULAR.FIELDS.VERIFICATION_POSTAL_CODE.LABEL": "ZIP code (Business address)", "REGULAR.FIELDS.YEAR_OF_DATE": "YYYY", "REGULAR.LEGAL_FORM_TYPE.INDEPENDENT_FREELANCERS_PROFESSIONALS": "Independent freelancers/professionals", "REGULAR.LEGAL_FORM_TYPE.PLACEHOLDER": "--- Please select ---", "REGULAR.LEGAL_FORM_TYPE.PRIVATE_COMPANY": "Private company", "REGULAR.LEGAL_FORM_TYPE.PUBLIC_COMPANY": "Public company", "REGULAR.LEGAL_FORM_TYPE.PUBLIC_PRIVATE_PARTNERSHIP": "Public private partnership", "REGULAR.LOGIN": "Log In", "REGULAR.MAGIC_LINK_EXPIRED.ALERT.MESSAGE": "Login link is valid for 1 hour and can only be used once. Please proceed to checkout to request a new login link.", "REGULAR.MAGIC_LINK_EXPIRED.ALERT.TITLE": "Login link has expired.", "REGULAR.MANDATORY_FIELD": "Mandatory field", "REGULAR.METRO_CARD_NUMBER": "METRO Card Number", "REGULAR.NOTIFICATIONS.BLOCKED_ACCOUNT": "Your account is blocked. Please contact support team.", "REGULAR.NOTIFICATIONS.PASSWORD_SECURITY_CONFIRMED": "Your password is secure!", "REGULAR.PASSWORD": "Password", "REGULAR.REGISTER": "Register", "REGULAR.REGISTRATION": "Register", "REGULAR.REMOVE": "Remove", "REGULAR.RESET_PASSWORD_EMAIL_SENT.SUCCESS.ALERT.MESSAGE": "We sent you an email with a link to set your password.", "REGULAR.RESET_PASSWORD_EMAIL_SENT.SUCCESS.ALERT.TITLE": "Check your email.", "REGULAR.RESET_PASSWORD.FAILURE.ALERT.MESSAGE": "An error occurred requesting the password set link. Please try again.", "REGULAR.RESET_PASSWORD.FAILURE.ALERT.TITLE": "Error requesting the link", "REGULAR.RESET_PASSWORD.SUCCESS.ALERT.MESSAGE": "Now you can access your account using password.", "REGULAR.RESET_PASSWORD.SUCCESS.ALERT.TITLE": "Password is successfully saved!", "REGULAR.RETURN_TO_PREVIOUS_PAGE": "Back", "REGULAR.SAVE": "Save Changes", "REGULAR.TAX_NUMBER": "Tax Number", "REGULAR.TERMS_AND_CONDITIONS": "Terms of use and purchase", "REGULAR.TRY_AGAIN": "Try again", "REGULAR.VAT_NUMBER": "VAT Number", "RETAIL.MEDIA.CAROUSEL.TITLE": "You may also like", "SELLER.SELLER_PAGE.ABOUT_TAB.TITLE": "About", "SELLER.SELLER_PAGE.ADDITIONALINFORMATION_FIELD.LABEL": "Additional information: ", "SELLER.SELLER_PAGE.ADDRESS_FIELD.LABEL": "Address: ", "SELLER.SELLER_PAGE.ALL_PRODUCTS_BUTTON": "View all products", "SELLER.SELLER_PAGE.ALL_PRODUCTS_INFO": "Here you can access all the products on the marketplace that are currently offered by this seller.\n", "SELLER.SELLER_PAGE.ALLPRODUCTS_TAB.TITLE": "All products", "SELLER.SELLER_PAGE.CANCELLATIONPOLICY_TAB.TITLE": "Cancellation Policy", "SELLER.SELLER_PAGE.COMPANY_FIELD.LABEL": "Company name: ", "SELLER.SELLER_PAGE.COMPANY_REPRESENTATIVES.LABEL": "Company representatives: ", "SELLER.SELLER_PAGE.CONTACT_BUTTON": "Help Center", "SELLER.SELLER_PAGE.DATAPOLICY_TAB.TITLE": "Data protection declaration", "SELLER.SELLER_PAGE.EMAIL_FIELD.LABEL": "E-Mail: ", "SELLER.SELLER_PAGE.FAX_FIELD.LABEL": "Fax: ", "SELLER.SELLER_PAGE.PHONE_FIELD.LABEL": "Phone: ", "SELLER.SELLER_PAGE.SELF_CERTIFICATION_FIELD.LABEL": "The Seller has committed to only offer products or services that comply with the applicable rules of Union law.", "SELLER.SELLER_PAGE.SUPERVISORYAUTHORITY_FIELD.LABEL": "Supervisory authority: ", "SELLER.SELLER_PAGE.TAKEBACKINFORMATION_FIELD.LABEL": "Take-Back Service Information:", "SELLER.SELLER_PAGE.TAKEBACKINFORMATION_TAB.TITLE": "Take-Back Service", "SELLER.SELLER_PAGE.TERMSANDCONDITIONS_TAB.TITLE": "Terms and Conditions", "SELLER.SELLER_PAGE.TRADE_FIELD.LABEL": "Trade register id: ", "SELLER.SELLER_PAGE.VAT_FIELD.LABEL": "VAT id: ", "SELLER.SELLER_PAGE.WEEEREGNUM_FIELD.LABEL": "WEEE registration number: ", "SHARED.ADDRESS.ADDRESS_2.LABEL": "Address Line 2 (optional)", "SHARED.ADDRESS.COMPANY_NAME.LABEL": "Business Name", "SHARED.ADDRESS.COUNTRY.SHIPPING.NOTE": "We're currently shipping only to Germany", "SHARED.ADDRESS.COUNTRY.SHIPPING.NOTE_ES": "We're currently shipping only to Spain", "SHARED.ADDRESS.MANAGE_ADDRESS.TOOLTIP": "Manage addresses", "SHARED.ADDRESS.VALIDATION.IBAN_NOT_VALID": "Not valid, IBAN should start with DE and be equal to 22 characters", "SHARED.ADDRESS.VALIDATION.IBAN_NOT_VALID.FRANCE": "Must be 25 characters long.", "SHARED.ADDRESS.VALIDATION.IBAN_NOT_VALID.GERMANY": "Must be 22 characters long.", "SHARED.ADDRESS.VALIDATION.INVALID_IBAN": "Must be { length } characters long.", "SHARED.ADDRESS.VALIDATION.INVALID_IBAN_VALUE": "IBAN must be valid.", "SHARED.ALT.ENERGY_EFFICIENCY_LABEL": "Energy efficiency label", "SHARED.BRAND_LIST.HEADLINE": "Our top brands", "SHARED.BRAND_LIST.SUBLINE": "Dive into our extraordinary brand world: discover our leading brands", "SHARED.DISCOVER_MORE.LABEL": "Discover more", "SHARED.INPUT.VALIDATION.ERROR_REQUIRED": "The field cannot be empty", "SHARED.PAGE_CANCEL_DIALOG.BUTTON.DISCARD": "Discard All Changes", "SHARED.PAGE_CANCEL_DIALOG.BUTTON.SAVE": "Save Changes", "SHARED.PAGE_CANCEL_DIALOG.MESSAGE": "You are trying to undo your changes.", "SHARED.PAGE_CANCEL_DIALOG.TITLE": "Are you sure you want to cancel changes?", "SHARED.PAGE_LEAVING_DIALOG.BUTTON.DISCARD": "Discard All Changes", "SHARED.PAGE_LEAVING_DIALOG.BUTTON.SAVE": "Save Changes", "SHARED.PAGE_LEAVING_DIALOG.MESSAGE": "You're trying to leave the page without saving any changes. Please, choose what to do with current changes", "SHARED.PAGE_LEAVING_DIALOG.TITLE": "Are you sure you want to leave this page without any changes?", "SHARED.QUANTITY_PICKER.LABEL.IN_STOCK": "In stock", "SHARED.QUANTITY_PICKER.LABEL.QUANTITY": "Quantity", "SHARED.SHOW_DETAILS.LABEL": "Show details", "SHARED.SHOW_LESS.LABEL": "Show less", "SHARED.SHOW_MORE.LABEL": "Show more", "SHARED.SPONSORED.LABEL.TEXT": "Sponsored", "SHARED.USP.DELIVERY.HEADLINE": "Delivery to the place you prefer", "SHARED.USP.DELIVERY.SUBLINE": "Comfort and efficiency: place your order online\nand we'll take care of the rest!", "SHARED.USP.DISCOUNT.HEADLINE": "Volume discounts", "SHARED.USP.DISCOUNT.SUBLINE": "Save with volume discounts and get up to 20% on many of our products", "SHARED.USP.EQUIPMENT.HEADLINE": "Variety of professional equipment", "SHARED.USP.EQUIPMENT.SUBLINE": "With us you will find thousands of products from various top brands.", "SHARED.USP.MAIN_HEADLINE": "Boost your business with METRO", "SHARED.USP.MAIN_SUBLINE": "Our service, dedicated to professionals, is designed to meet the needs of our customers", "SHOPPING_LIST_NAV_TITLE": "Lists", "SL_PAGE_LISTING_ECO_FEE_DISCLAIMER": "Eco-participation included.", "SL_PAGE_LISTING_EMPTY_BOX_BUTTON_TEXT": "Explore products to add to list", "SL_PAGE_LISTING_EMPTY_BOX_MESSAGE": "Start adding products directly from product pages to make your shopping experience easier and more organized.", "SL_PAGE_LISTING_EMPTY_BOX_TITLE": "Your shopping list is empty", "SL_PAGE_LISTING_ITEM_ADD_TO_CART": "Add to Basket", "SL_PAGE_LISTING_ITEM_ADD_TO_CART_TOOLTIP": "This seller has only {quantity} of these available.", "SL_PAGE_LISTING_ITEM_DELIVERY_FREE": "Free shipping", "SL_PAGE_LISTING_ITEM_DELIVERY_TEXT": "Delivery", "SL_PAGE_LISTING_ITEM_MSRP_LABEL": "MSRP", "SL_PAGE_LISTING_ITEM_NOT_AVAILABLE": "Currently unavailable", "SL_PAGE_LISTING_ITEM_PROMOTION": "Deal", "SL_PAGE_LISTING_ITEM_STRIKE_THROUGH_PRICE": "Was", "SL_PAGE_LISTING_ITEM_VAT_EXCL": "Excl. VAT", "SL_PAGE_LISTING_ITEM_VAT_INCL": "incl. VAT", "SL_PAGE_LISTING_ITEMS_TEXT": "Items", "SL_PAGE_LISTING_LOAD_MORE_TEXT": "Show more products", "SL_PAGE_LISTING_MINIMUM_QUANTITY": "Minimum quantity: ", "SL_PAGE_LISTING_OFFER_EXPLORE_SIMILAR_BUTTON": "See available offers", "SL_PAGE_LISTING_OFFER_NOT_AVAILABLE_BUTTON": "Explore Similar Products", "SL_PAGE_LISTING_PRICE_DISCLAIMER": "The prices displayed are the current prices shown on the marketplace.", "SL_PAGE_LISTING_TITLE": "Shopping List", "SL_PAGE_LISTING_UNDO_REMOVAL_ITEM": "was removed from your shopping list.", "SL_PAGE_MULTI_LIST_ADD_LIST": "Create New List", "SL_PAGE_MULTI_LIST_CREATE_LIST_BUTTON_NAME": "Save", "SL_PAGE_MULTI_LIST_CREATE_LIST_FIELD_NAME": "List Name", "SL_PAGE_MULTI_LIST_CREATE_LIST_MAX_VALIDATION_ERROR_MESSAGE": "List name can be a maximum of 25 characters.", "SL_PAGE_MULTI_LIST_CREATE_LIST_SUB_TITLE": "Please enter name of the new list", "SL_PAGE_MULTI_LIST_CREATE_LIST_TITLE": "Neue Liste erstellen", "SL_PAGE_MULTI_LIST_CREATE_LIST_VALIDATION_ERROR_MESSAGE": "Please enter a name for the list", "SL_PAGE_MULTI_LIST_EMPTY_BOX_BUTTON_TEXT": "Create your shopping list", "SL_PAGE_MULTI_LIST_EMPTY_BOX_MESSAGE": "Add products directly from the product pages to make your shopping experience easier and more transparent.", "SL_PAGE_MULTI_LIST_EMPTY_BOX_TITLE": "You haven’t created any lists yet", "SL_PAGE_MULTI_LIST_TITLE": "Lists", "SL_PAGE_MULTI_LIST_UPDATE_LIST_SUB_TITLE": "Please enter the new list name", "SL_PAGE_MULTI_LIST_UPDATE_LIST_TITLE": "Rename Your List", "SL_PAGE_MULTI_LIST_VIEW_LESS": "View Less", "SL_PAGE_MULTI_LIST_VIEW_MORE": "View All", "SL_PAGE_OTHER_LISTS_TITLE": "Other lists", "SL_PAGE_POPUP_CLOSE_TEXT": "Close", "SL_PAGE_PRODUCT_SELLER_INFO_TEXT": "Sold By", "SL_PAGE_REMOVE_TEXT": "Remove {itemName} from {shoppingListName} list.", "SL_PAGE_SKIP_LINK_CURRENT_LIST_ITEMS": "Current list items", "SL_PAGE_TITLE": "Shopping List | METRO Markets", "SL_PAGE.MULTI_LIST.DELETE_LIST.CANCEL_BUTTON": "Cancel", "SL_PAGE.MULTI_LIST.DELETE_LIST.CONFIRMATION": "Are you sure you want to delete {listName}?", "SL_PAGE.MULTI_LIST.DELETE_LIST.DELETE_BUTTON": "Delete", "SL_PAGE.MULTI_LIST.DELETE_LIST.TITLE": "Delete Your List", "SUBSIDIARY_ASSORTMENT.NOTIFICATION.TITLE": "{keyAccountName} Holders: ", "SUBSIDIARY_ASSORTMENT.POPUP.MESSAGE": "Hey {username}, your {keyAccountName} account comes with some special offers. Click to check out your tailored pricing and assortment on our `Your assortment` page.", "SUBSIDIARY_ASSOTMENT.NOTIFICATION.MESSAGE": "Explore your tailored pricing and assortment on the `Your assortment` page. Follow the <span class=\"link__word\">link</span> to view your dedicated offers", "SUSIDIARY_ASSORTMENT.NOTIFICATION.BUTTON.LABEL": "Explore offers", "SUSIDIARY_ASSORTMENT.POPUP.BUTTON_LABEL": "See Offers", "SUSIDIARY_ASSORTMENT.POPUP.TITLE": "Time to explore your tailored assortment", "TIER.ONE.DIALOG.GO_TO_FORGOT_PASSWORD": "Reset Password", "TIER.ONE.DIALOG.GO_TO_FORGOT_PASSWORD.TITLE": "If you don’t remember your password", "TIER.ONE.DIALOG.GO_TO_LOGIN": "<PERSON><PERSON>", "TIER.ONE.DIALOG.GO_TO_LOGIN.TITLE": "If you know your password", "TIER.ONE.DIALOG.LOGIN.OR.PASSWORD.BODY_1": "This email ", "TIER.ONE.DIALOG.LOGIN.OR.PASSWORD.BODY_2": "is already connected to an account. To access your account please use the following options:", "TIER.ONE.DIALOG.LOGIN.OR.PASSWORD.TITLE": "Account already exists", "TITLE.PAGE.ACCOUNT.ADDRESS_BOOK": "Address Book | METRO Markets", "TITLE.PAGE.ACCOUNT.ADDRESS_BOOK.CREATE": "Add New Address | METRO Markets", "TITLE.PAGE.ACCOUNT.ADDRESS_BOOK.EDIT": "Change Address | METRO Markets", "TITLE.PAGE.ACCOUNT.AFTER_CHANGE_EMAIL": "The validation email has been sent! | METRO Markets", "TITLE.PAGE.ACCOUNT.APPROVE_ORDERS": "Approval Overview | METRO", "TITLE.PAGE.ACCOUNT.BUY_AGAIN": "Buy Again | METRO", "TITLE.PAGE.ACCOUNT.INFO": "Account Information | METRO Markets", "TITLE.PAGE.ACCOUNT.INFO.ADD_BUSINESS_INFO": "Add Business Info | METRO Markets", "TITLE.PAGE.ACCOUNT.INFO.CHANGE_EMAIL": "Change Email | METRO Markets", "TITLE.PAGE.ACCOUNT.INFO.CHANGE_PASSWORD": "Change Password | METRO Markets", "TITLE.PAGE.ACCOUNT.INFO.EDIT_BUSINESS_INFO": "Change Business Information | METRO Markets", "TITLE.PAGE.ACCOUNT.INFO.EDIT_PRIVATE_INFO": "Change Private Information | METRO Markets", "TITLE.PAGE.ACCOUNT.ORDERS_HISTORY": "Orders | METRO", "TITLE.PAGE.ACCOUNT.PAYMENT_METHODS": "Onlineshop payment methods | METRO Marktplatz", "TITLE.PAGE.ACCOUNT.SHOPPING_LIST": "Your assortment | METRO", "TITLE.PAGE.AUTH.BRANCH_SELECTION.BUSINESS": "Business Registration | METRO Marketplace", "TITLE.PAGE.AUTH.CUSTOMER_SEARCH": "Customer Search", "TITLE.PAGE.AUTH.DIFFERENT_COUNTRY_NOTICE": "Welcome to Metro { country }", "TITLE.PAGE.AUTH.LOGIN": "Log In | METRO Markets", "TITLE.PAGE.AUTH.REGISTRATION": "Create an account | METRO Markets", "TITLE.PAGE.AUTH.REGISTRATION.BUSINESS": "Register as New Business | METRO Markets", "TITLE.PAGE.AUTH.REGISTRATION.CONSUMER": "Register as a Consumer | METRO Markets", "TITLE.PAGE.AUTH.REGISTRATION.METRO_CARD": "Register with METRO Card | METRO Markets", "TITLE.PAGE.AUTH.VAT_ID_CHECK.BUSINESS": "VAT ID", "TITLE.PAGE.CATALOG": "All categories | METRO Markets", "TITLE.PAGE.CATALOG.EXTENSION": "METRO Markets", "TITLE.PAGE.CATEGORY": "{slug} | METRO", "TITLE.PAGE.CHECKOUT.ADDRESSES.BILLING": "Select a Billing Address | METRO Markets", "TITLE.PAGE.CHECKOUT.ADDRESSES.BILLING.CREATE": "Enter a New Address | METRO Markets", "TITLE.PAGE.CHECKOUT.ADDRESSES.BILLING.EDIT": "Update this Billing Address | METRO Markets", "TITLE.PAGE.CHECKOUT.ADDRESSES.SHIPPING": "Select a Shipping Address | METRO Markets", "TITLE.PAGE.CHECKOUT.ADDRESSES.SHIPPING.CREATE": "Enter a New Address | METRO Markets", "TITLE.PAGE.CHECKOUT.ADDRESSES.SHIPPING.EDIT": "Update this Shipping Address | METRO Markets", "TITLE.PAGE.CHECKOUT.CART": "Cart | METRO Markets", "TITLE.PAGE.CHECKOUT.ORDER": "Review your order | METRO Markets", "TITLE.PAGE.CHECKOUT.STEP.ADDRESS": "Review your order | METRO Markets", "TITLE.PAGE.CHECKOUT.STEP.PAYMENT": "Review your order | METRO Markets", "TITLE.PAGE.CHECKOUT.STEP.REVIEW": "Review your order | METRO Markets", "TITLE.PAGE.CHECKOUT.STEP.THANKYOU": "Review your order | METRO Markets", "TITLE.PAGE.CUSTOM_CATEGORY_HUB": "| METRO Markets", "TITLE.PAGE.CUSTOM_HUB": "| METRO Markets", "TITLE.PAGE.HOME": "METRO Marketplace - The B2B Online Shop for Professionals", "TITLE.PAGE.LANDING_PAGE": "{slug} | METRO", "TITLE.PAGE.LEGAL.ABOUT": "About us | METRO Markets", "TITLE.PAGE.LEGAL.CANCELLATION_POLICY": "Cancelation policy | METRO Markets", "TITLE.PAGE.LEGAL.CONDITIONS": "Terms of Use and Purchase | METRO Markets", "TITLE.PAGE.LEGAL.DISPOSAL": "Disposal and Environmental Regulations | METRO Markets", "TITLE.PAGE.LEGAL.IMPRINT": "Imprint | METRO Markets", "TITLE.PAGE.LEGAL.PRIVACY_POLICY": "Privacy policy | METRO Markets", "TITLE.PAGE.NOT_FOUND": "Sorry, we couldn't find this page | METRO Markets", "TITLE.PAGE.PAYMENT_LINK": "METRO Marketplace - Payment Link", "TITLE.PAGE.PDP": "| METRO Markets", "TITLE.PAGE.SEARCH": "Buy online | Search results | METRO", "TITLE.PAGE.SEARCH_IMAGE": "Buy online | Search results by image | METRO", "TITLE.PAGE.SELLER": "| METRO Markets", "TITLE.PAGE.SERVER_ERROR": "Technical issue | METRO Markets", "TITLE.PAGE.WEEE": "Recovery of old devices | METRO Marketplace", "TOOLTIP.ALL_PAGES.ARIA_LABEL": "More information", "WEEE.FEEDBACK.BODY.ERROR": "Unfortunately this did not work. Please try again or contact our customer service.", "WEEE.FEEDBACK.BODY.EXPIRED": "Unfortunately, this link is no longer valid. Please note that the link is only valid for 24 hours after the email containing it has been sent. (This does not apply to weekends).\nIn addition, the link is no longer valid if you have previously refused to collect an old device or a collection has already been requested.", "WEEE.FEEDBACK.BODY.SUCCESS.NO": "Thank you for your feedback. We have informed our team or the marketplace seller that you do not wish to return an old device.", "WEEE.FEEDBACK.BODY.SUCCESS.YES": "Thank you for your feedback. Our team or the marketplace seller will contact you shortly to organize the collection of your old device.", "WEEE.FEEDBACK.TITLE.ERROR": "An error has occurred", "WEEE.FEEDBACK.TITLE.EXPIRED": "Link no longer valid", "WEEE.FEEDBACK.TITLE.SUCCESS.NO": "<PERSON><PERSON><PERSON> received successfully", "WEEE.FEEDBACK.TITLE.SUCCESS.YES": "Request received successfully", "YOUR_ASSORTMENT_PAGE_LISTING_ITEM_ADD_TO_CART_TOOLTIP": "This seller has only {quantity} of these available.", "YOUR_ASSORTMENT_PAGE_LISTING_LOAD_MORE_TEXT": "Show more products"}