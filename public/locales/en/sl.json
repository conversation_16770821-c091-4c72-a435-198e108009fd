{"CATALOG.DETAILS_PAGE.BUYBOX.ECO.FEE.INFORMATION": "Eco-contribution included in price, if applicable.", "CATALOG.DETAILS_PAGE.BUYBOX.EPR.INFORMATION": "Do you want to recycle your old product? ", "CATALOG.DETAILS_PAGE.BUYBOX.LEARN_MORE_TITLE": "Learn more", "CORE.ALL_PAGES.PRODUCT_PROMO.LABEL.VOLUME_PRICING": "Buy {volumePriceQuantity} and save {volumePricePercentage}%", "PAGES.ACCOUNT.BUY_AGAIN.TITLE": "Buy Again", "SL_PAGE_LISTING_ECO_FEE_DISCLAIMER": "Eco-participation included.", "SL_PAGE_LISTING_EMPTY_BOX_BUTTON_TEXT": "Explore products to add to list", "SL_PAGE_LISTING_EMPTY_BOX_MESSAGE": "Start adding products directly from product pages to make your shopping experience easier and more organized.", "SL_PAGE_LISTING_EMPTY_BOX_TITLE": "Your shopping list is empty", "SL_PAGE_LISTING_ITEM_ADD_TO_CART": "Add to Basket", "SL_PAGE_LISTING_ITEM_ADD_TO_CART_TOOLTIP": "This seller has only {quantity} of these available.", "SL_PAGE_LISTING_ITEM_DELIVERY_FREE": "Free shipping", "SL_PAGE_LISTING_ITEM_DELIVERY_TEXT": "Delivery", "SL_PAGE_LISTING_ITEM_MSRP_LABEL": "MSRP", "SL_PAGE_LISTING_ITEM_NOT_AVAILABLE": "Currently unavailable", "SL_PAGE_LISTING_ITEM_PROMOTION": "Deal", "SL_PAGE_LISTING_ITEM_STRIKE_THROUGH_PRICE": "Was", "SL_PAGE_LISTING_ITEM_VAT_EXCL": "Excl. VAT", "SL_PAGE_LISTING_ITEM_VAT_INCL": "incl. VAT", "SL_PAGE_LISTING_ITEMS_TEXT": "Items", "SL_PAGE_LISTING_LOAD_MORE_TEXT": "Show more products", "SL_PAGE_LISTING_MINIMUM_QUANTITY": "Minimum quantity: ", "SL_PAGE_LISTING_OFFER_EXPLORE_SIMILAR_BUTTON": "See available offers", "SL_PAGE_LISTING_OFFER_NOT_AVAILABLE_BUTTON": "Explore Similar Products", "SL_PAGE_LISTING_PRICE_DISCLAIMER": "The prices displayed are the current prices shown on the marketplace.", "SL_PAGE_LISTING_TITLE": "Shopping List", "SL_PAGE_LISTING_UNDO_REMOVAL_ITEM": "was removed from your shopping list.", "SL_PAGE_MULTI_LIST_ADD_LIST": "Create New List", "SL_PAGE_MULTI_LIST_CREATE_LIST_BUTTON_NAME": "Save", "SL_PAGE_MULTI_LIST_CREATE_LIST_FIELD_NAME": "List Name", "SL_PAGE_MULTI_LIST_CREATE_LIST_MAX_VALIDATION_ERROR_MESSAGE": "List name can be a maximum of 25 characters.", "SL_PAGE_MULTI_LIST_CREATE_LIST_SUB_TITLE": "Please enter name of the new list", "SL_PAGE_MULTI_LIST_CREATE_LIST_TITLE": "Neue Liste erstellen", "SL_PAGE_MULTI_LIST_CREATE_LIST_VALIDATION_ERROR_MESSAGE": "Please enter a name for the list", "SL_PAGE_MULTI_LIST_EMPTY_BOX_BUTTON_TEXT": "Create your shopping list", "SL_PAGE_MULTI_LIST_EMPTY_BOX_MESSAGE": "Add products directly from the product pages to make your shopping experience easier and more transparent.", "SL_PAGE_MULTI_LIST_EMPTY_BOX_TITLE": "You haven’t created any lists yet", "SL_PAGE_MULTI_LIST_TITLE": "Lists", "SL_PAGE_MULTI_LIST_UPDATE_LIST_SUB_TITLE": "Please enter the new list name", "SL_PAGE_MULTI_LIST_UPDATE_LIST_TITLE": "Rename Your List", "SL_PAGE_MULTI_LIST_VIEW_LESS": "View Less", "SL_PAGE_MULTI_LIST_VIEW_MORE": "View All", "SL_PAGE_OTHER_LISTS_TITLE": "Other lists", "SL_PAGE_POPUP_CLOSE_TEXT": "Close", "SL_PAGE_PRODUCT_SELLER_INFO_TEXT": "Sold By", "SL_PAGE_REMOVE_TEXT": "Remove {itemName} from {shoppingListName} list.", "SL_PAGE_TITLE": "Shopping List | METRO Markets", "SL_PAGE.MULTI_LIST.DELETE_LIST.CANCEL_BUTTON": "Cancel", "SL_PAGE.MULTI_LIST.DELETE_LIST.CONFIRMATION": "Are you sure you want to delete {listName}?", "SL_PAGE.MULTI_LIST.DELETE_LIST.DELETE_BUTTON": "Delete", "SL_PAGE.MULTI_LIST.DELETE_LIST.TITLE": "Delete Your List"}