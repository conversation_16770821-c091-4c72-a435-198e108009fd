import { PayloadAction, createSlice } from '@reduxjs/toolkit'

import { ShoppingListItemOffers } from '@modules/shopping-list/types'

import { fetchGlobalPromotion } from './thunk/fetchGlobalPromotion'
import { VASAddToCartData } from '@modules/product/types'

interface SuccessModal {
  visible: boolean
  quantity: number | null
  vasData?: VASAddToCartData[]
}
export interface ProductState {
  highlightedVariantImageUrl: string | null
  detailsSection: boolean
  generalInfoAccordion: boolean
  promoStories: any | null
  parsedCriteoViewItem: any | null
  successModal: SuccessModal | null
  shouldUpdateShoppingList: boolean
  shoppingList: ShoppingListItemOffers[] | null
  itemShoppingList: ShoppingListItemOffers[] | null
}

export const initialState: ProductState = {
  highlightedVariantImageUrl: null,
  detailsSection: false,
  generalInfoAccordion: false,
  promoStories: null,
  parsedCriteoViewItem: null,
  successModal: {
    visible: false,
    quantity: null,
    vasData: null
  },
  shoppingList: null,
  itemShoppingList: null,
  shouldUpdateShoppingList: false
}

const setHighlightedVariantImageUrlReducer = (
  state: ProductState,
  action: PayloadAction<string | null>
) => {
  state.highlightedVariantImageUrl = action.payload
}

const setSuccessModalReducer = (
  state: ProductState,
  action: PayloadAction<SuccessModal>
) => {
  state.successModal = action.payload
}

const setSuccessModalVisibilityReducer = (
  state: ProductState,
  action: PayloadAction<boolean>
) => {
  state.successModal.visible = action.payload
}

const setShouldUpdateShoppingListReducer = (
  state: ProductState,
  action: PayloadAction<boolean>
) => {
  state.shouldUpdateShoppingList = action.payload
}

export const ProductSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setHighlightedVariantImage: setHighlightedVariantImageUrlReducer,
    setCriteoViewItem: (state, action) => {
      state.parsedCriteoViewItem = action.payload
    },
    setShoppinglists: (state, action) => {
      state.shoppingList = action.payload
    },
    setItemShoppingList: (state, action) => {
      state.itemShoppingList = action.payload
    },
    toggleGeneralInfo: (
      state: ProductState,
      action: PayloadAction<boolean>
    ) => {
      state.generalInfoAccordion = action.payload
        ? true
        : !state.generalInfoAccordion
    },
    toggleDetailsSection: (
      state: ProductState,
      action: PayloadAction<boolean>
    ) => {
      state.detailsSection = action.payload
        ? true
        : !state.detailsSection
    },
    setSuccessModal: setSuccessModalReducer,
    setSuccessModalVisibility: setSuccessModalVisibilityReducer,
    setShouldUpdateShoppingList: setShouldUpdateShoppingListReducer
  },
  extraReducers: (builder) => {
    builder.addCase(fetchGlobalPromotion.fulfilled, (state, action) => {
      state.promoStories = action.payload
    })
  }
})

export const {
  setHighlightedVariantImage,
  setCriteoViewItem,
  setShoppinglists,
  setItemShoppingList,
  toggleGeneralInfo,
  toggleDetailsSection,
  setSuccessModal,
  setSuccessModalVisibility,
  setShouldUpdateShoppingList
} = ProductSlice.actions

export const productReducer = ProductSlice.reducer
