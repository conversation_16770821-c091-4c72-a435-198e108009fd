import { useDispatch, useSelector } from 'react-redux'

import { AppDispatch, RootState } from '@core/redux/store'

import { setHighlightedVariantImage } from './productSlice'

export const useProduct = () => {
  const dispatch = useDispatch<AppDispatch>()

  const getHighlightedVariantImageUrlSelector = (state: RootState) =>
    state?.product?.highlightedVariantImageUrl || null

  return {
    highlightedVariantImageUrl: useSelector(
      getHighlightedVariantImageUrlSelector
    ),
    setHighlightedVariantImageUrl: (imageUrl: string | null) =>
      dispatch(setHighlightedVariantImage(imageUrl)),
    successModal: useSelector(
      (state: RootState) => state.product?.successModal
    ),
    shouldUpdateShoppingList: useSelector(
      (state: RootState) => state.product?.shouldUpdateShoppingList
    ),
    itemShoppingList: useSelector(
      (state: RootState) => state.product?.itemShoppingList
    ),
    detailsSection: useSelector(
      (state: RootState) => state.product?.detailsSection
    )
  }
}
