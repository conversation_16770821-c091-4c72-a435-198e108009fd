import { messageCenterFeatureFlags } from './messageCenterFeatureFlags'
import {
  paymentFeatureFlags,
  paymentFeaturesWithJSON
} from './paymentFeaureFlags'

export const FeatureFlag = {
  FF_EXTENDED_DATALAYER: 'FF_EXTENDED_DATALAYER',
  FF_REACT_COOKIE_CONSENT: 'FF_REACT_COOKIE_CONSENT',
  FF_REACT_FLYOUT_MENU: 'FF_REACT_FLYOUT_MENU',
  FF_REACT_GTM: 'FF_REACT_GTM',
  FF_REACT_OPTIMIZE: 'FF_REACT_OPTIMIZE',
  FF_REACT_SENTRY: 'FF_REACT_SENTRY',
  FF_SHOW_SHIPPING_COSTS_IN_MINICART_ENABLED:
    'FF_SHOW_SHIPPING_COSTS_IN_MINICART_ENABLED',
  FF_BLACK_FRIDAY_COUNTDOWN: 'FF_BLACK_FRIDAY_COUNTDOWN',
  FF_DISABLE_SUPPORT_PHONE_NO: 'FF_DISABLE_SUPPORT_PHONE_NO',
  FF_SYNC_SSO: 'FF_SYNC_SSO',
  FF_CART_REQUEST_CANCELLATION: 'FF_CART_REQUEST_CANCELLATION',
  FF_WEB_COMPONENTS_BASE_URL: 'FF_WEB_COMPONENTS_BASE_URL',
  FF_COMBINE_AUTH_BUTTON_SPINNER: 'FF_COMBINE_AUTH_BUTTON_SPINNER',
  FF_ENABLE_AUTH_URL_FROM_HOME_PAGE: 'FF_ENABLE_AUTH_URL_FROM_HOME_PAGE',
  FF_CHECKOUT_WEB_APP_BUYER_ITALY_PROVINCES:
    'FF_CHECKOUT_WEB_APP_BUYER_ITALY_PROVINCES',
  FF_CHECKOUT_INITIALIZE_V2: 'FF_CHECKOUT_INITIALIZE_V2',
  FF_MACO_ALL_OFFERS: 'FF_MACO_ALL_OFFERS',
  FF_TEMP_SHIP_113_SHOW_SHIPPING_COSTS: 'FF_TEMP_SHIP_113_SHOW_SHIPPING_COSTS',
  FF_NEW_METRO_FREE_SHIPPING_WORDING: 'FF_NEW_METRO_FREE_SHIPPING_WORDING',
  FF_LIMITED_STOCK_LABEL: 'FF_LIMITED_STOCK_LABEL',
  FF_REACT_ENABLE_RANDOMIZATION_SEO_WIDGET_LINKS:
    'FF_REACT_ENABLE_RANDOMIZATION_SEO_WIDGET_LINKS',
  FF_MACO_STORYBLOK_DEFAULT: 'FF_MACO_STORYBLOK_DEFAULT',
  FF_REACT_ACCOUNT_INFO_V2: 'FF_REACT_ACCOUNT_INFO_V2',
  FF_EXTERNAL_PDP: 'FF_EXTERNAL_PDP',
  FF_GA_E_COMMERCE_INFO_CLEANUP: 'FF_GA_E_COMMERCE_INFO_CLEANUP',
  FF_SL_REACT_PDP_REQUEST_URL: 'FF_SL_REACT_PDP_REQUEST_URL',
  FF_CALLBACK_OPTION_IN_PDP: 'FF_CALLBACK_OPTION_IN_PDP',
  FF_SHOW_PHONE_NO_IN_CALLBACK: 'FF_SHOW_PHONE_NO_IN_CALLBACK',
  FF_STORYBLOK_IS_IN_EDITOR_CHECK: 'FF_STORYBLOK_IS_IN_EDITOR_CHECK',
  FF_REACT_PDP_STORYBLOK_CONTENT: 'FF_REACT_PDP_STORYBLOK_CONTENT',
  FF_ENABLE_VARIANTS: 'FF_ENABLE_VARIANTS',
  FF_MACO_FR_FOOTER_ENABLE_SOFORT: 'FF_MACO_FR_FOOTER_ENABLE_SOFORT',
  FF_REACT_MACO_OLD_TOP_BANNERS: 'FF_REACT_MACO_OLD_TOP_BANNERS',
  FF_MACO_NEW_NAVIGATION_MENU_ENABLE: 'FF_MACO_NEW_NAVIGATION_MENU_ENABLE',
  FF_ZERO_VAT: 'FF_ZERO_VAT',
  FF_BALEARIC_ISLAND_SHIPPING: 'FF_BALEARIC_ISLAND_SHIPPING',
  FF_REACT_BUY_AGAIN_CART: 'FF_REACT_BUY_AGAIN_CART',
  FF_BOE_ECK_SHOPPING_LIST: 'FF_BOE_ECK_SHOPPING_LIST',
  FF_CART_INITIALIZE_CHECKOUT_V2: 'FF_CART_INITIALIZE_CHECKOUT_V2',
  FF_ADD_TO_CART_ON_APP_CART: 'FF_ADD_TO_CART_ON_APP_CART',
  FF_PRODUCT_CARDS_PLPS_RESTYLE: 'PRODUCT_CARDS_PLPS_RESTYLE',
  FF_PDP_TABLEWARE_FEATURES: 'FF_PDP_TABLEWARE_FEATURES',
  FF_SL_ENABLE_SUBSIDIARY_REDIRECTION: 'FF_SL_ENABLE_SUBSIDIARY_REDIRECTION',
  FF_SL_BACK_URL_BUGFIX: 'FF_SL_BACK_URL_BUGFIX',
  FF_SL_OTP_LOGIN_FLOW: 'FF_SL_OTP_LOGIN_FLOW',
  FF_PRODUCT_CARDS_CAROUSELS_RESTYLE: 'PRODUCT_CARDS_CAROUSELS_RESTYLE',
  FF_REACT_PDP_PROMO_BANNERS: 'FF_REACT_PDP_PROMO_BANNERS',
  FF_SL_BUYER_ACC_ADDRESS_BOOK: 'FF_SL_BUYER_ACC_ADDRESS_BOOK',
  FF_SL_ADDRESS_STEP_PHONE_NUM_AND_NIF_ID:
    'FF_SL_ADDRESS_STEP_PHONE_NUM_AND_NIF_ID',
  FF_SL_PHONE_NUMBER_VALIDATION: 'FF_SL_PHONE_NUMBER_VALIDATION',
  FF_REACT_BUY_AGAIN_CART_CAROUSEL: 'FF_REACT_BUY_AGAIN_CART_CAROUSEL',
  FF_REACT_BUY_AGAIN_CART_CAROUSEL_TRAFFIC_ALLOCATION:
    'FF_REACT_BUY_AGAIN_CART_CAROUSEL_TRAFFIC_ALLOCATION',
  FF_SL_ADDRESS_BOOK_MODIFY: 'FF_SL_ADDRESS_BOOK_MODIFY',
  FF_CHECKOUT_ADDRESS_VALIDATION_V2: 'FF_CHECKOUT_ADDRESS_VALIDATION_V2',
  FF_SL_DISABLE_SILENT_LOGIN: 'FF_SL_DISABLE_SILENT_LOGIN',
  FF_GOOGLE_RECOMMENDERS: 'FF_GOOGLE_RECOMMENDERS',
  FF_SL_ENABLE_SOFT_LOGIN: 'FF_SL_ENABLE_SOFT_LOGIN',
  FF_SL_REACT_TIER2_BUSINESS_DETAILS_ADDRESS_AUTOCOMPLETE:
    'FF_SL_REACT_TIER2_BUSINESS_DETAILS_ADDRESS_AUTOCOMPLETE',
  REGION_SELECTOR: 'REGION_SELECTOR',
  FF_PLP_REACT_MIGRATION: 'FF_PLP_REACT_MIGRATION',
  FF_CCS_GENESYS_ACTIVATION: 'FF_CCS_GENESYS_ACTIVATION',
  FF_MACO_SPONSORING_WIDGET: 'MACO_SPONSORING_WIDGET',
  FF_SL_REACT_ENABLE_CUSTOM_FLYOUT: 'FF_SL_REACT_ENABLE_CUSTOM_FLYOUT',
  FF_RETAIL_MEDIA_CAROUSEL: 'FF_RETAIL_MEDIA_CAROUSEL',
  FF_SL_ENABLE_FALLBACK_FLYOUT_FOR_HEADQUATERS:
    'FF_SL_ENABLE_FALLBACK_FLYOUT_FOR_HEADQUATERS',
  FF_CART_FORCE_SEARCH2_PROD_URL_ENABLED:
    'FF_CART_FORCE_SEARCH2_PROD_URL_ENABLED',
  FF_SL_GENERATE_SOFT_TOKEN: 'FF_SL_GENERATE_SOFT_TOKEN',
  FF_SL_RAVELIN_LOGIN: 'FF_SL_RAVELIN_LOGIN',
  FF_SL_FINGERPRINT_HASH: 'FF_SL_FINGERPRINT_HASH',
  FF_SL_SAVE_PHONE_NUMBER: 'FF_SL_SAVE_PHONE_NUMBER',
  FF_NL_DISCOUNT_5: 'FF_NL_DISCOUNT_5',
  FF_BOE_SHOPPING_LIST: 'FF_BOE_SHOPPING_LIST',
  FF_BOE_BUY_AGAIN_V3_REACT: 'FF_BOE_BUY_AGAIN_V3_REACT',
  ZRP_BLOOMREACH_ID: 'ZRP_BLOOMREACH_ID',
  FF_SL_SOFT_LOGIN_TESTING_ACCOUNTS: 'FF_SL_SOFT_LOGIN_TESTING_ACCOUNTS',
  PLP_SPONSORED_PRODUCTS: 'PLP_SPONSORED_PRODUCTS',
  FF_CCS_MESSAGE_CENTER_MP: 'FF_CCS_MESSAGE_CENTER_MP',
  FF_MULTIPLE_SHOPPING_LIST: 'FF_MULTIPLE_SHOPPING_LIST',
  FF_BOE_MULTIPLE_SHOPPING_LIST: 'FF_BOE_MULTIPLE_SHOPPING_LIST',
  FF_BOE_HEADER_APPROVALS_DASHBOARD_LINK:
    'FF_BOE_HEADER_APPROVALS_DASHBOARD_LINK',
  FF_OPTIMIZELY_NEW_ID: 'FF_OPTIMIZELY_NEW_ID',
  FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT:
    'FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT',
  ODR_TMP_1722_HEADER_SHOW_ORDERS_INSTEAD_OF_BUY_AGAIN:
    'ODR_TMP_1722_HEADER_SHOW_ORDERS_INSTEAD_OF_BUY_AGAIN',
  PLP_CAROUSELS_CONFIG: 'PLP_CAROUSELS_CONFIG',
  SEARCH_VERSION_EXPERIMENT_CONFIG: 'SEARCH_VERSION_EXPERIMENT_CONFIG',
  SEARCH_VERSION_EXPERIMENT_VARIANT_CONFIG:
    'SEARCH_VERSION_EXPERIMENT_VARIANT_CONFIG',
  FF_PDP_VIDEO: 'FF_PDP_VIDEO',
  AI_SEARCH_CONFIG: 'AI_SEARCH_CONFIG',
  FF_SNL_VAT_VALIDATION_NON_CROSS_BORDER:
    'FF_SNL_VAT_VALIDATION_NON_CROSS_BORDER',
  FF_PDP_WEEE: 'FF_PDP_WEEE',
  FF_SL_USE_COUNTRY_WITH_SF_V2: 'FF_SL_USE_COUNTRY_WITH_SF_V2',
  FF_STORE_AVAILABILITY_ON_PDP: 'FF_STORE_AVAILABILITY_ON_PDP',
  FF_USE_SPI_PROXY_FOR_STORE_AVAILABILITY:
    'FF_USE_SPI_PROXY_FOR_STORE_AVAILABILITY',
  TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST:
    'TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST',
  ...paymentFeatureFlags,
  ...messageCenterFeatureFlags
} as const

export const featuresWithPercentage: FeatureFlagName[] = [
  FeatureFlag.FF_SL_OTP_LOGIN_FLOW
]
export const featuresWithNumbers: FeatureFlagName[] = []

/**
 * IMPORTANT: This is a list of feature flags that hold JSON values.
 * Add the feature flag here if you need the value parsed as JSON.
 * Otherwise, it will be treated as a boolean.
 */
export const featuresWithJSON: FeatureFlagName[] = [
  FeatureFlag.FF_SL_PHONE_NUMBER_VALIDATION,
  FeatureFlag.ZRP_BLOOMREACH_ID,
  FeatureFlag.FF_SL_SOFT_LOGIN_TESTING_ACCOUNTS,
  FeatureFlag.FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT,
  FeatureFlag.PLP_CAROUSELS_CONFIG,
  FeatureFlag.AI_SEARCH_CONFIG,
  FeatureFlag.SEARCH_VERSION_EXPERIMENT_CONFIG,
  FeatureFlag.SEARCH_VERSION_EXPERIMENT_VARIANT_CONFIG,
  ...paymentFeaturesWithJSON
]

export type FeatureFlagName = (typeof FeatureFlag)[keyof typeof FeatureFlag]

export type FeatureFlagValue = {
  [key in FeatureFlagName]: boolean
}
