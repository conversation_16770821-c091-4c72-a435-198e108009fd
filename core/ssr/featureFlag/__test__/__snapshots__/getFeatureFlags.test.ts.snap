// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Config Cat should return FeatureFlagValue and clear cache 1`] = `
Object {
  "AI_SEARCH_CONFIG": true,
  "CHECKOUT_UA_INITIATIVE": true,
  "FF_ADD_TO_CART_ON_APP_CART": true,
  "FF_ALLOW_CARD_TOKENIZATION": true,
  "FF_ALLOW_CARD_TOKENIZATION_SEPADD": true,
  "FF_BALEARIC_ISLAND_SHIPPING": true,
  "FF_BLACK_FRIDAY_COUNTDOWN": true,
  "FF_BOE_BUY_AGAIN_V3_REACT": true,
  "FF_BOE_ECK_SHOPPING_LIST": true,
  "FF_BOE_HEADER_APPROVALS_DASHBOARD_LINK": true,
  "FF_BOE_MULTIPLE_SHOPPING_LIST": true,
  "FF_BOE_SHOPPING_LIST": true,
  "FF_CALLBACK_OPTION_IN_PDP": true,
  "FF_CART_FORCE_SEARCH2_PROD_URL_ENABLED": true,
  "FF_CART_INITIALIZE_CHECKOUT_V2": true,
  "FF_CART_REQUEST_CANCELLATION": true,
  "FF_CCS_GENESYS_ACTIVATION": true,
  "FF_CCS_MESSAGE_CENTER_MP": true,
  "FF_CCS_MESSAGE_CENTER_ORDER_C2A": true,
  "FF_CHECKOUT_ADDRESS_VALIDATION_V2": true,
  "FF_CHECKOUT_GET_PAYMENT_METHODS_V2": true,
  "FF_CHECKOUT_INITIALIZE_V2": true,
  "FF_CHECKOUT_PAYMENT_METHOD_SORT": true,
  "FF_CHECKOUT_PAYMENT_RATEPAY_MOBILENUMBER": true,
  "FF_CHECKOUT_PAYMENT_STEP_PREFETCH": true,
  "FF_CHECKOUT_PAYMENT_VALIDATE_IBAN": true,
  "FF_CHECKOUT_RATEPAY_DEVICE_FINGERPRINT": true,
  "FF_CHECKOUT_RATEPAY_LEGAL_TERMS": true,
  "FF_CHECKOUT_WEB_APP_BUYER_ITALY_PROVINCES": true,
  "FF_COMBINE_AUTH_BUTTON_SPINNER": true,
  "FF_DISABLE_SUPPORT_PHONE_NO": true,
  "FF_ENABLE_AUTH_URL_FROM_HOME_PAGE": true,
  "FF_ENABLE_VARIANTS": true,
  "FF_EXTENDED_DATALAYER": true,
  "FF_EXTERNAL_PDP": true,
  "FF_GA_E_COMMERCE_INFO_CLEANUP": true,
  "FF_GOOGLE_RECOMMENDERS": true,
  "FF_LIMITED_STOCK_LABEL": true,
  "FF_MACO_ALL_OFFERS": true,
  "FF_MACO_FR_FOOTER_ENABLE_SOFORT": true,
  "FF_MACO_NEW_NAVIGATION_MENU_ENABLE": true,
  "FF_MACO_STORYBLOK_DEFAULT": true,
  "FF_MULTIPLE_SHOPPING_LIST": true,
  "FF_NEW_METRO_FREE_SHIPPING_WORDING": true,
  "FF_NL_DISCOUNT_5": true,
  "FF_OPTIMIZELY_NEW_ID": true,
  "FF_PAYMENT_STEP_IN_REACT": true,
  "FF_PDP_TABLEWARE_FEATURES": true,
  "FF_PDP_VIDEO": true,
  "FF_PDP_WEEE": true,
  "FF_PLP_REACT_MIGRATION": true,
  "FF_REACT_ACCOUNT_INFO_V2": true,
  "FF_REACT_BUY_AGAIN_CART": true,
  "FF_REACT_BUY_AGAIN_CART_CAROUSEL": true,
  "FF_REACT_BUY_AGAIN_CART_CAROUSEL_TRAFFIC_ALLOCATION": true,
  "FF_REACT_COOKIE_CONSENT": false,
  "FF_REACT_ENABLE_RANDOMIZATION_SEO_WIDGET_LINKS": true,
  "FF_REACT_FLYOUT_MENU": true,
  "FF_REACT_GTM": true,
  "FF_REACT_MACO_OLD_TOP_BANNERS": true,
  "FF_REACT_OPTIMIZE": true,
  "FF_REACT_PDP_PROMO_BANNERS": true,
  "FF_REACT_PDP_STORYBLOK_CONTENT": true,
  "FF_REACT_SENTRY": true,
  "FF_RETAIL_MEDIA_CAROUSEL": true,
  "FF_SHOW_PHONE_NO_IN_CALLBACK": true,
  "FF_SHOW_SHIPPING_COSTS_IN_MINICART_ENABLED": true,
  "FF_SL_ADDRESS_BOOK_MODIFY": true,
  "FF_SL_ADDRESS_STEP_PHONE_NUM_AND_NIF_ID": true,
  "FF_SL_BACK_URL_BUGFIX": true,
  "FF_SL_BUYER_ACC_ADDRESS_BOOK": true,
  "FF_SL_DISABLE_SILENT_LOGIN": true,
  "FF_SL_ENABLE_FALLBACK_FLYOUT_FOR_HEADQUATERS": true,
  "FF_SL_ENABLE_SOFT_LOGIN": true,
  "FF_SL_ENABLE_SUBSIDIARY_REDIRECTION": true,
  "FF_SL_FINGERPRINT_HASH": true,
  "FF_SL_GENERATE_SOFT_TOKEN": true,
  "FF_SL_OTP_LOGIN_FLOW": true,
  "FF_SL_PHONE_NUMBER_VALIDATION": true,
  "FF_SL_RAVELIN_LOGIN": true,
  "FF_SL_REACT_ENABLE_CUSTOM_FLYOUT": true,
  "FF_SL_REACT_PDP_REQUEST_URL": true,
  "FF_SL_REACT_TIER2_BUSINESS_DETAILS_ADDRESS_AUTOCOMPLETE": true,
  "FF_SL_SAVE_PHONE_NUMBER": true,
  "FF_SL_SOFT_LOGIN_TESTING_ACCOUNTS": true,
  "FF_SL_USE_COUNTRY_WITH_SF_V2": true,
  "FF_SNL_VAT_VALIDATION_NON_CROSS_BORDER": true,
  "FF_STORE_AVAILABILITY_ON_PDP": true,
  "FF_STORYBLOK_IS_IN_EDITOR_CHECK": true,
  "FF_SYNC_SSO": true,
  "FF_TEMP_SHIP_113_SHOW_SHIPPING_COSTS": true,
  "FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT": true,
  "FF_USE_SPI_PROXY_FOR_STORE_AVAILABILITY": true,
  "FF_WEB_COMPONENTS_BASE_URL": true,
  "FF_ZERO_VAT": true,
  "MACO_SPONSORING_WIDGET": true,
  "ODR_TMP_1722_HEADER_SHOW_ORDERS_INSTEAD_OF_BUY_AGAIN": true,
  "PLP_CAROUSELS_CONFIG": true,
  "PLP_SPONSORED_PRODUCTS": true,
  "PRODUCT_CARDS_CAROUSELS_RESTYLE": true,
  "PRODUCT_CARDS_PLPS_RESTYLE": true,
  "REGION_SELECTOR": true,
  "SEARCH_VERSION_EXPERIMENT_CONFIG": true,
  "SEARCH_VERSION_EXPERIMENT_VARIANT_CONFIG": true,
  "TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST": true,
  "ZRP_BLOOMREACH_ID": true,
  "ff_temp_co_date_of_birth": true,
  "ff_temp_co_stepper_ga_track": true,
}
`;

exports[`Config Cat should return FeatureFlagValue instance with correct values 1`] = `
Object {
  "AI_SEARCH_CONFIG": true,
  "CHECKOUT_UA_INITIATIVE": true,
  "FF_ADD_TO_CART_ON_APP_CART": true,
  "FF_ALLOW_CARD_TOKENIZATION": true,
  "FF_ALLOW_CARD_TOKENIZATION_SEPADD": true,
  "FF_BALEARIC_ISLAND_SHIPPING": true,
  "FF_BLACK_FRIDAY_COUNTDOWN": true,
  "FF_BOE_BUY_AGAIN_V3_REACT": true,
  "FF_BOE_ECK_SHOPPING_LIST": true,
  "FF_BOE_HEADER_APPROVALS_DASHBOARD_LINK": true,
  "FF_BOE_MULTIPLE_SHOPPING_LIST": true,
  "FF_BOE_SHOPPING_LIST": true,
  "FF_CALLBACK_OPTION_IN_PDP": true,
  "FF_CART_FORCE_SEARCH2_PROD_URL_ENABLED": true,
  "FF_CART_INITIALIZE_CHECKOUT_V2": true,
  "FF_CART_REQUEST_CANCELLATION": true,
  "FF_CCS_GENESYS_ACTIVATION": true,
  "FF_CCS_MESSAGE_CENTER_MP": true,
  "FF_CCS_MESSAGE_CENTER_ORDER_C2A": true,
  "FF_CHECKOUT_ADDRESS_VALIDATION_V2": true,
  "FF_CHECKOUT_GET_PAYMENT_METHODS_V2": true,
  "FF_CHECKOUT_INITIALIZE_V2": true,
  "FF_CHECKOUT_PAYMENT_METHOD_SORT": true,
  "FF_CHECKOUT_PAYMENT_RATEPAY_MOBILENUMBER": true,
  "FF_CHECKOUT_PAYMENT_STEP_PREFETCH": true,
  "FF_CHECKOUT_PAYMENT_VALIDATE_IBAN": true,
  "FF_CHECKOUT_RATEPAY_DEVICE_FINGERPRINT": true,
  "FF_CHECKOUT_RATEPAY_LEGAL_TERMS": true,
  "FF_CHECKOUT_WEB_APP_BUYER_ITALY_PROVINCES": true,
  "FF_COMBINE_AUTH_BUTTON_SPINNER": true,
  "FF_DISABLE_SUPPORT_PHONE_NO": true,
  "FF_ENABLE_AUTH_URL_FROM_HOME_PAGE": true,
  "FF_ENABLE_VARIANTS": true,
  "FF_EXTENDED_DATALAYER": true,
  "FF_EXTERNAL_PDP": true,
  "FF_GA_E_COMMERCE_INFO_CLEANUP": true,
  "FF_GOOGLE_RECOMMENDERS": true,
  "FF_LIMITED_STOCK_LABEL": true,
  "FF_MACO_ALL_OFFERS": true,
  "FF_MACO_FR_FOOTER_ENABLE_SOFORT": true,
  "FF_MACO_NEW_NAVIGATION_MENU_ENABLE": true,
  "FF_MACO_STORYBLOK_DEFAULT": true,
  "FF_MULTIPLE_SHOPPING_LIST": true,
  "FF_NEW_METRO_FREE_SHIPPING_WORDING": true,
  "FF_NL_DISCOUNT_5": true,
  "FF_OPTIMIZELY_NEW_ID": true,
  "FF_PAYMENT_STEP_IN_REACT": true,
  "FF_PDP_TABLEWARE_FEATURES": true,
  "FF_PDP_VIDEO": true,
  "FF_PDP_WEEE": true,
  "FF_PLP_REACT_MIGRATION": true,
  "FF_REACT_ACCOUNT_INFO_V2": true,
  "FF_REACT_BUY_AGAIN_CART": true,
  "FF_REACT_BUY_AGAIN_CART_CAROUSEL": true,
  "FF_REACT_BUY_AGAIN_CART_CAROUSEL_TRAFFIC_ALLOCATION": true,
  "FF_REACT_COOKIE_CONSENT": false,
  "FF_REACT_ENABLE_RANDOMIZATION_SEO_WIDGET_LINKS": true,
  "FF_REACT_FLYOUT_MENU": true,
  "FF_REACT_GTM": true,
  "FF_REACT_MACO_OLD_TOP_BANNERS": true,
  "FF_REACT_OPTIMIZE": true,
  "FF_REACT_PDP_PROMO_BANNERS": true,
  "FF_REACT_PDP_STORYBLOK_CONTENT": true,
  "FF_REACT_SENTRY": true,
  "FF_RETAIL_MEDIA_CAROUSEL": true,
  "FF_SHOW_PHONE_NO_IN_CALLBACK": true,
  "FF_SHOW_SHIPPING_COSTS_IN_MINICART_ENABLED": true,
  "FF_SL_ADDRESS_BOOK_MODIFY": true,
  "FF_SL_ADDRESS_STEP_PHONE_NUM_AND_NIF_ID": true,
  "FF_SL_BACK_URL_BUGFIX": true,
  "FF_SL_BUYER_ACC_ADDRESS_BOOK": true,
  "FF_SL_DISABLE_SILENT_LOGIN": true,
  "FF_SL_ENABLE_FALLBACK_FLYOUT_FOR_HEADQUATERS": true,
  "FF_SL_ENABLE_SOFT_LOGIN": true,
  "FF_SL_ENABLE_SUBSIDIARY_REDIRECTION": true,
  "FF_SL_FINGERPRINT_HASH": true,
  "FF_SL_GENERATE_SOFT_TOKEN": true,
  "FF_SL_OTP_LOGIN_FLOW": true,
  "FF_SL_PHONE_NUMBER_VALIDATION": true,
  "FF_SL_RAVELIN_LOGIN": true,
  "FF_SL_REACT_ENABLE_CUSTOM_FLYOUT": true,
  "FF_SL_REACT_PDP_REQUEST_URL": true,
  "FF_SL_REACT_TIER2_BUSINESS_DETAILS_ADDRESS_AUTOCOMPLETE": true,
  "FF_SL_SAVE_PHONE_NUMBER": true,
  "FF_SL_SOFT_LOGIN_TESTING_ACCOUNTS": true,
  "FF_SL_USE_COUNTRY_WITH_SF_V2": true,
  "FF_SNL_VAT_VALIDATION_NON_CROSS_BORDER": true,
  "FF_STORE_AVAILABILITY_ON_PDP": true,
  "FF_STORYBLOK_IS_IN_EDITOR_CHECK": true,
  "FF_SYNC_SSO": true,
  "FF_TEMP_SHIP_113_SHOW_SHIPPING_COSTS": true,
  "FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT": true,
  "FF_USE_SPI_PROXY_FOR_STORE_AVAILABILITY": true,
  "FF_WEB_COMPONENTS_BASE_URL": true,
  "FF_ZERO_VAT": true,
  "MACO_SPONSORING_WIDGET": true,
  "ODR_TMP_1722_HEADER_SHOW_ORDERS_INSTEAD_OF_BUY_AGAIN": true,
  "PLP_CAROUSELS_CONFIG": true,
  "PLP_SPONSORED_PRODUCTS": true,
  "PRODUCT_CARDS_CAROUSELS_RESTYLE": true,
  "PRODUCT_CARDS_PLPS_RESTYLE": true,
  "REGION_SELECTOR": true,
  "SEARCH_VERSION_EXPERIMENT_CONFIG": true,
  "SEARCH_VERSION_EXPERIMENT_VARIANT_CONFIG": true,
  "TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST": true,
  "ZRP_BLOOMREACH_ID": true,
  "ff_temp_co_date_of_birth": true,
  "ff_temp_co_stepper_ga_track": true,
}
`;
