import { CountryCode } from '@core/types/countryCode'
import { getBaseHref } from '@core/utils/getBaseHref'

import { isIntegration } from '@modules/shared/utils/environmentDetection'

import { config } from '../config'

export type ProxyEndpoint =
  | 'server-logger'
  | 'cart/get-cart-quantity'
  | 'cart/get-cart'
  | 'cart/post-public-cart'
  | 'cart/delete-offer'
  | 'cart/delete-vas'
  | 'cart/post-offer'
  | 'cart/patch-offer'
  | 'cart/post-offer-inventory'
  | 'cart/post-checkout-initialize'
  | 'cart/cart-merge'
  | 'cart/get-check-discount-code'
  | 'cart/post-share-payment-link'
  | 'cart/get-notifications'
  | 'cart/patch-delivery-option'
  | 'cart/post-services'
  | 'cart/delete-services'
  | 'account/order-history/get-return-reasons'
  | 'account/order-history/return-order-line'
  | 'account/order-history/cancel-return-order-line'
  | 'account/order-history/cancel-order'
  | 'account/order-history/cancel-order-line'
  | 'account/order-history/get-link-download-document'
  | 'account/order-history/get-login-to-sso-url'
  | 'account/order-history/get-orders'
  | 'address/get-personal-address-query'
  | 'address/get-business-address-query'
  | 'address/save-address'
  | 'address/get-countries'
  | 'address/get-provinces'
  | 'address/post-address-validate'
  | 'address/save-phone-number'
  | 'buy-again/get-buy-again-products'
  | 'buy-again/get-products-spi-v2'
  | 'assisted-sales/resend-payment-link'
  | 'assisted-sales/get-shared-payment-links'
  | 'assisted-sales/get-completed-orders'
  | 'auth/validate-otp'
  | 'auth/otp-celebrate'
  | 'auth/generate-otp'
  | 'auth/flyout/custom-flyout'
  | 'auth/flyout/context-switch'
  | 'auth/flyout/status'
  | 'auth/key-accounts/get-subsidiaries'
  | 'auth/key-accounts/search-branch'
  | 'auth/key-accounts/create-subsidiary'
  | 'auth/key-accounts/create-digital-account'
  | 'auth/key-accounts/delete-branch'
  | 'auth/business-details/save'
  | 'approve-orders/get-approve-orders'
  | 'auth/soft-login'
  | 'shopping-list/add'
  | 'shopping-list/remove'
  | 'shopping-list/get-item-offers'
  | 'shopping-list/create'
  | 'shopping-list/update-name'
  | 'shopping-list/delete'
  | 'shopping-list/details'
  | 'shopping-list/item'
  | 'approve-orders/order-approve'
  | 'approve-orders/order-reject'
  | 'shopping-list/list'
  | 'shopping-list/get-lists'
  | 'shopping-list/bulk-update'
  | 'address/validate-vat'
  | 'auth/generate-sso-login-link'
  | 'auth/ravelin-account-take-over'
  | 'address/save-vat'
  | 'auth/session'
  | 'your-assortment/get-products'
  | 'your-assortment/get-categories'
  | 'payment/get-payment-method'
  | 'message-center/get-chats'
  | 'message-center/get-chat-subjects'
  | 'message-center/post-chats'
  | 'message-center/post-messages'
  | 'message-center/upload-urls'
  | 'message-center/get-messages'
  | 'message-center/get-order'
  | 'message-center/get-eligibility'
  | 'product/availability'
  | 'product/stores'

export const CUSTOMER_NEWSLETTER_CONSENT_URL_CRM = `${config.svcCRMBaseUrl}/api/v1/public/exponea/customers/consent`
export const CUSTOMER_EVENTS_URL_CRM = `${config.svcCRMBaseUrl}/api/v1/public/exponea/customers/events`
export const CUSTOMER_VISITORS_EVENT_URL_CRM = `${config.svcCRMBaseUrl}/api/v1/public/exponea/visitors/event`
export const getProxyEndpoint = (
  market: CountryCode,
  endpoint: ProxyEndpoint
) => `${getBaseHref(market)}/app-api/${endpoint}`

export const getCountriesCDN = (market?: CountryCode) => {
  if (isIntegration()) {
    return `https://fe-cdn.infra.metro-markets.cloud/json/countries-${market}.json`
  }
  if (config.nodeEnv !== 'production') {
    return getProxyEndpoint(market, 'address/get-countries')
  }
  return `${config.cdnBaseUrl}/json/countries${market ? `-${market}` : ''}.json`
}

export const getProvincesCDN = (market: CountryCode) => {
  return getProxyEndpoint(market, 'address/get-provinces')
}
