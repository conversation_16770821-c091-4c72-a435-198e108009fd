import * as Sentry from '@sentry/nextjs'

import { withSessionRoute } from '@core/auth/session'
import { withoutCache } from '@core/services/cache/withoutCache'
import { getUserErrorMessageFromError } from '@core/services/errorHandler/errorMessages'
import { http } from '@core/services/http/ssrHttpRequest'
import { createRequestContext } from '@core/utils/createRequestContext.util'

import {
  X_CORRELATION_ID,
  X_SESSION_ID
} from '@shared/constants/http.constants'

import { API_MINI_CART_REMOTE_QUANTITY, AppCartAPI } from '@modules/homepage/constants'

async function getCartQuantity(req, res) {
  const { market, locale, accessToken, correlationId, sessionId } =
    createRequestContext(req)

  try {
    const { data } = await http<{ quantity: number }>(
      AppCartAPI.getCartQuantity(),
      {
        headers: {
          [X_CORRELATION_ID]: correlationId,
          [X_SESSION_ID]: sessionId
        }
      },
      {
        accessToken,
        market,
        locale
      }
    )
    res.status(200).json(data)
  } catch (error) {
    if (error?.response?.status === 401) {
      Sentry.captureMessage('User session', {
        user: req.session?.user
      })
    }

    const message = getUserErrorMessageFromError(error)
    const statusCode = error?.response?.status ?? 500
    res.status(statusCode).json({ message, correlationId })
  }
}

// eslint-disable-next-line import/no-default-export
export default withSessionRoute(withoutCache(getCartQuantity))
