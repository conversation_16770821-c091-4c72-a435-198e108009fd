import { mockOffers } from '@msw/mocks/cartMocks/mockOffers'
import axios from 'axios'
import <PERSON><PERSON><PERSON><PERSON>pter from 'axios-mock-adapter'
import { createMocks } from 'node-mocks-http'

import getCartQuantity from '@pages/api/cart/get-cart-quantity'

describe('getCartQuantity', () => {
  let mock: MockAdapter
  beforeEach(() => {
    mock = new MockAdapter(axios)
  })

  afterEach(() => {
    mock.reset()
    jest.clearAllMocks()
  })

  it('should be defined', () => {
    expect(getCartQuantity).toBeDefined()
  })

  it('should make a successful request', async () => {
    const { req, res } = getReqAndResMocks()

    mock.onGet().reply(200, { test: 'test' })

    await getCartQuantity(req, res)
    expect(mock.history.get[0].url).toContain(
      'https://service-buyer-gateway.pp-de.metro-marketplace.cloud/api/account/proxy/cart/v1/cart/quantity'
    )
  })

  it('should return an error if an error occurs', async () => {
    const { req, res } = getReqAndResMocks()

    // Mock the HTTP response
    mock.onGet().reply(500, {})
    await getCartQuantity(req, res)

    expect(res.statusCode).toBe(500)
    expect(res._getJSONData()).toHaveProperty('message')
  })
})

function getReqAndResMocks(): { req: any; res: any } {
  return createMocks({
    method: 'GET',
    headers: {
      'country-code': 'DE',
      'accept-language': 'de-DE',
      referer: 'https://local.mm.makro.de/'
    },
    session: {
      user: {
        accessToken: 'mockToken'
      }
    },
    query: {
      activeContext: 'personal'
    },
    body: {
      offer: mockOffers.offerWithVas.offer,
      vasId: mockOffers.offerWithVas.valueAddedServices[0].id
    }
  })
}
