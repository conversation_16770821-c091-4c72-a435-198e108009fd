import axios from 'axios'
import <PERSON><PERSON><PERSON><PERSON>pter from 'axios-mock-adapter'
import { createMocks } from 'node-mocks-http'

import getEligibility from '../get-eligibility'

let mock: MockAdapter

describe('getEligibility API Route', () => {
  beforeEach(() => {
    mock = new MockAdapter(axios)
  })

  afterEach(() => {
    mock.reset()
    jest.clearAllMocks()
  })

  it('should be defined', () => {
    expect(getEligibility).toBeDefined()
  })

  it('should successfully retrieve eligibility data when order is eligible', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    const expectedData = {
      status: 'success',
      message: 'Order [ON123] has been verified successfully.',
      data: {
        eligible: true
      }
    }
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(200, expectedData)

    await getEligibility(req, res)

    const request = mock.history.get[0]
    expect(request.url).toContain(
      `/api/account/proxy/message_center/seller-organizations/${organizationId}/orders/${orderNumber}/eligibility`
    )
    expect(request.params).toEqual(req.query)
    expect(res._getStatusCode()).toBe(200)
    expect(res._getJSONData()).toEqual(expectedData)
  })

  it('should successfully retrieve eligibility data when order is not eligible', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    const expectedData = {
      status: 'success',
      message: 'Order [ON123] is not eligible for message center.',
      data: {
        eligible: false
      }
    }
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(200, expectedData)

    await getEligibility(req, res)

    const request = mock.history.get[0]
    expect(request.url).toContain(
      `/api/account/proxy/message_center/seller-organizations/${organizationId}/orders/${orderNumber}/eligibility`
    )
    expect(res._getStatusCode()).toBe(200)
    expect(res._getJSONData()).toEqual(expectedData)
  })

  it('should handle 404 error when order is not found', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'INVALID_ORDER'
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(404, { message: 'Order not found' })

    await getEligibility(req, res)

    expect(res._getStatusCode()).toBe(404)
    const responseData = res._getJSONData()
    expect(responseData.message).toBeDefined()
  })

  it('should handle 401 unauthorized error', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'invalidToken' } }
    })

    mock.onGet().reply(401, { message: 'Unauthorized' })

    await getEligibility(req, res)

    expect(res._getStatusCode()).toBe(401)
    const responseData = res._getJSONData()
    expect(responseData.message).toBeDefined()
  })

  it('should handle 500 server error', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(500, { message: 'Internal server error' })

    await getEligibility(req, res)

    expect(res._getStatusCode()).toBe(500)
    const responseData = res._getJSONData()
    expect(responseData.message).toBeDefined()
  })

  it('should handle network error and return 500', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().networkError()

    await getEligibility(req, res)

    expect(res._getStatusCode()).toBe(500)
    const responseData = res._getJSONData()
    expect(responseData.message).toBeDefined()
  })

  it('should include correlation and session headers in the request', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    const expectedData = {
      status: 'success',
      message: 'Order [ON123] has been verified successfully.',
      data: {
        eligible: true
      }
    }
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { organizationId, orderNumber },
      headers: { 'country-code': 'DE', 'accept-language': 'de-DE' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(200, expectedData)

    await getEligibility(req, res)

    const request = mock.history.get[0]
    expect(request.headers).toHaveProperty('x-correlation-id')
    expect(request.headers).toHaveProperty('x-session-id')
    expect(request.headers['x-correlation-id']).toBeDefined()
    expect(request.headers['x-session-id']).toBeDefined()
  })

  it('should pass query parameters correctly', async () => {
    const organizationId = 'metro123'
    const orderNumber = 'ON123'
    const additionalParam = 'test-value'
    const expectedData = {
      status: 'success',
      data: { eligible: true }
    }
    
    const { req, res } = createMocks({
      method: 'GET',
      query: { 
        organizationId, 
        orderNumber, 
        additionalParam 
      },
      headers: { 'country-code': 'US', 'accept-language': 'en-US' },
      session: { user: { accessToken: 'mockToken' } }
    })

    mock.onGet().reply(200, expectedData)

    await getEligibility(req, res)

    const request = mock.history.get[0]
    expect(request.params).toEqual({
      organizationId,
      orderNumber,
      additionalParam
    })
  })
})
