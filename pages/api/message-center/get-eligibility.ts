import * as Sentry from '@sentry/nextjs'

import { withSessionRoute } from '@core/auth/session'
import { config } from '@core/config'
import { withoutCache } from '@core/services/cache/withoutCache'
import { getUserErrorMessageFromError } from '@core/services/errorHandler/errorMessages'
import { http } from '@core/services/http/ssrHttpRequest'
import { createRequestContext } from '@core/utils/createRequestContext.util'

import {
  X_CORRELATION_ID,
  X_SESSION_ID
} from '@modules/shared/constants/http.constants'

async function getEligibility(req, res) {
  const { market, locale, accessToken, correlationId, sessionId } =
    createRequestContext(req)
  const { organizationId, orderNumber } = req?.query

  try {
    const url = `${config.buyerGateway}/api/account/proxy/message_center/seller-organizations/${organizationId}/orders/${orderNumber}/eligibility`

    const { data } = await http(
      url,
      {
        method: 'get',
        params: req.query,
        headers: {
          [X_CORRELATION_ID]: correlationId,
          [X_SESSION_ID]: sessionId
        }
      },
      { accessToken, market, locale }
    )
    res.status(200).json(data)
  } catch (error) {
    Sentry.captureException(error)
    const message = getUserErrorMessageFromError(error)
    res.status(error.response?.status || 500).json({ message })
  }
}

// eslint-disable-next-line import/no-default-export
export default withSessionRoute(withoutCache(getEligibility))
