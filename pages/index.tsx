import {
  ISbContentMangmntAPI,
  StoryblokStory,
  setComponents //@ts-ignore
} from '@storyblok/react/rsc'
import { GetServerSidePropsContext, PreviewData } from 'next'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { NextParsedUrlQuery } from 'next/dist/server/request-meta'
import dynamic from 'next/dynamic'
import { ComponentType, useEffect, useState } from 'react'

import { Favicons } from '@core/components/Favicons'
import { Layout, PropsAllPages } from '@core/components/Layout/Layout'
import { useSubsidiaryRedirection } from '@core/hooks/useSubsidiaryRedirection'
import { TokenBag, useSyncSSO } from '@core/hooks/useSyncSSO'
import { useHomePageTranslation } from '@core/hooks/useTranslation'
import { useUserLoginType } from '@core/hooks/useUserLoginType'
import { AppStore } from '@core/redux/store'
import { getPagesCommonData } from '@core/services/data/getPagesCommonData'
import { APIResponse } from '@core/services/http/types'
import { commonServerSideAuthProcess, withWrappers } from '@core/ssr'
import {
  FeatureFlag,
  FeatureFlagName
} from '@core/ssr/featureFlag/featureFlag.enum'
import { getMarketFromContext } from '@core/ssr/marketDetection'
import { CountryCode } from '@core/types/countryCode'
import { getCountryCodeFromLocale } from '@core/types/countryCodeToLocale'
import { getMarketFromUrl } from '@core/utils/getMarketFromUrl.util'
import { isServerSide } from '@core/utils/isServerSide'

import { STORYBLOK_ROUTE_HOMEPAGE } from '@shared/constants/storyblok-routes'
import { getStoryblokData } from '@shared/services/get-storyblok-data'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS,
  optimizelyInstance
} from '@shared/utils/optimizely.utils'

import { useHomepageViewEventEffect } from '@modules/ga-tracking/hooks/useHomepageViewEventEffect'
import { TopBannersWrapperProps } from '@modules/homepage/components/Storyblok'
import { InternalLinkingWidget } from '@modules/homepage/components/internalLinkingWidget'
import { ALL_PAGES, HOME_PAGE, OK, STORYBLOK } from '@modules/product/constants'
import {
  Container,
  ContentCardGridProps,
  PopUpModal
} from '@modules/shared/components'
import { RegionResponse } from '@modules/shared/components/RegionSelector/types/region.type'
import {
  AbTestExperimentProps,
  AdvancedCarouselProps,
  BannerImageProps,
  BrandListWrappertProps,
  CategoryGridProps,
  ContentProductCarouselProps,
  CustomSignUpProps,
  GenericDynamicListProps,
  HeroBannerProps,
  MarginCollapserProps,
  NewsletterWrapperProps,
  PersonalizedContentProps,
  PopularLinksProps,
  QuickFiltersCarouselProps,
  RetailMediaCarouselProps,
  RichContentProps,
  SeoContentProps,
  SeoFaqContentProps,
  StoryblokDividerProps,
  TopBannersBV2Props,
  UspGridProps
} from '@modules/shared/components/Storyblok'

import nextI18NextConfig from '../next-i18next.config'

const GenericDynamicList: ComponentType<GenericDynamicListProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.GenericDynamicList
  )
)

const AbTestExperiment: ComponentType<AbTestExperimentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AbTestExperiment
  )
)

const AdvancedCarousel: ComponentType<AdvancedCarouselProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AdvancedCarousel
  )
)

const BannerImage: ComponentType<BannerImageProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.BannerImage)
)

const CategoryGrid: ComponentType<CategoryGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.CategoryGrid)
)

const Divider: ComponentType<StoryblokDividerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.StoryblokDivider
  )
)

const NewsletterWrapper: ComponentType<NewsletterWrapperProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.NewsletterWrapper
  )
)

const PersonalizedContent: ComponentType<PersonalizedContentProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.PersonalizedContent
    )
)

const PopularLinks: ComponentType<PopularLinksProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.PopularLinks)
)

const RichContent: ComponentType<RichContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.RichContent)
)

const UspGrid: ComponentType<UspGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.UspGrid)
)

const HeroBanner: ComponentType<HeroBannerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.HeroBanner)
)

const ContentProductCarousel: ComponentType<ContentProductCarouselProps> =
  dynamic(() =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.ContentProductCarousel
    )
  )

const TopBannersWrapper: ComponentType<TopBannersWrapperProps> = dynamic(() =>
  import('@modules/homepage/components/Storyblok').then(
    (mod) => mod.TopBannersWrapper
  )
)

const TopBannersBV2: ComponentType<TopBannersBV2Props> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.TopBannersBV2
  )
)

const SeoContent: ComponentType<SeoContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.SeoContent)
)

const SeoFaqContent: ComponentType<SeoFaqContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.SeoFaqContent
  )
)

const RetailMediaCarousel: ComponentType<RetailMediaCarouselProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.RetailMediaCarousel
    )
)

const QuickFiltersCarousel: ComponentType<QuickFiltersCarouselProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.QuickFiltersCarousel
    )
)

const ContentCardGrid: ComponentType<ContentCardGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.ContentCardGrid
  )
)

const MarginCollapser: ComponentType<MarginCollapserProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.MarginCollapser
  )
)

const BrandListWrapper: ComponentType<BrandListWrappertProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.BrandListWrapper
  )
)

const CustomSignUpWrapper: ComponentType<CustomSignUpProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.CustomSignUpWrapper
  )
)

interface Props extends PropsAllPages {
  featureFlags: Record<FeatureFlagName, boolean>
  storyblokHomepage: APIResponse<ISbContentMangmntAPI>
  marketOnServerSide: CountryCode
  tokenBag: TokenBag
  regionInfo?: APIResponse<RegionResponse | []>
}

export default function Home({
  featureFlags,
  storyblokHomepage,
  marketOnServerSide,
  tokenBag,
  regionInfo,
  ...commonProps
}: Props) {
  useSyncSSO(tokenBag)
  useUserLoginType()
  useHomepageViewEventEffect()
  useSubsidiaryRedirection()
  const isNewIdFF = featureFlags[FeatureFlag.FF_OPTIMIZELY_NEW_ID]
  const { t } = useHomePageTranslation()

  // Set dynamic storyblok components for homepage
  setComponents({
    Homepage: GenericDynamicList,
    seoDynamicWidget: InternalLinkingWidget,
    'recommended-products-carousel': ContentProductCarousel,
    'product-carousel': ContentProductCarousel,
    'category-grid': CategoryGrid,
    banner: BannerImage,
    newsletter: NewsletterWrapper,
    'personalized-content': PersonalizedContent,
    'hero-banner': HeroBanner,
    divider: Divider,
    'top-banners-v2': TopBannersBV2,
    'top-banners': TopBannersWrapper,
    abTestExperiment: AbTestExperiment,
    'rich-content': RichContent,
    'advanced-carousel': AdvancedCarousel,
    'popular-links': PopularLinks,
    'usp-grid': UspGrid,
    'seo-content': SeoContent,
    'faq-content': SeoFaqContent,
    'retail-media-carousel': RetailMediaCarousel,
    'quick-filters-carousel': QuickFiltersCarousel,
    'content-card-grid': ContentCardGrid,
    'margin-collapser': MarginCollapser,
    'brand-list': BrandListWrapper,
    'custom-sign-up': CustomSignUpWrapper
  })

  const [isOptPixelEnabled, setIsOptPixelEnabled] = useState<boolean>(null)

  useEffect(() => {
    if (!isServerSide()) {
      // TODO: Remove when We check it's working fine
      const optimizelyPixelVariant: Promise<string> =
        optimizelyInstance.getExperimentVariant<EXPERIMENT_VARIATIONS>(
          'testa',
          isNewIdFF
        )

      optimizelyPixelVariant.then((variant) => {
        setIsOptPixelEnabled(variant === EXPERIMENT_VARIATIONS.VARIANT_ONE)
      })
    }
  }, [])

  return (
    <>
      <Favicons market={marketOnServerSide} />
      <Layout
        {...commonProps}
        title={t('TITLE.PAGE.HOME')}
        regionInfo={regionInfo}
      >
        <Container className="pt-0">
          {isOptPixelEnabled && <p className={'opt_pixel'}></p>}
          {/* storyblok dynamic components */}
          {storyblokHomepage.type === OK && (
            <StoryblokStory
              bridgeOptions={null}
              story={storyblokHomepage.result?.story}
            />
          )}
        </Container>
        <PopUpModal />
      </Layout>
    </>
  )
}

const getServerSidePropsInternal =
  (store: AppStore) =>
    async (
      context: GetServerSidePropsContext<NextParsedUrlQuery, PreviewData> & {
        featureFlags: Record<FeatureFlagName, boolean>
      }
    ) => {
      const { req } = context

      const marketOnServerSide = getMarketFromUrl(req.headers.host)
      const locale = getCountryCodeFromLocale(getMarketFromContext(context))
      const [storyblokHomepage, allPagesData] = await Promise.all([
        getStoryblokData(context, STORYBLOK_ROUTE_HOMEPAGE),
        getPagesCommonData(context)
      ])
      const { regionInfo, ...propsNotMigratedYetToRedux } = allPagesData

    const { tokenBag } = await commonServerSideAuthProcess(
      store,
      context,
      allPagesData
    )
    // @INFO: This configuration will apply to the entire route
    const optimizelyData = {
      experimentKeys: [EXPERIMENT_NAMES.MACO_OPEN_PDP_IN_NEW_TAB],
      experimentAttributes: {
        market: marketOnServerSide
      }
    }
    return {
      props: {
        tokenBag,
        storyblokHomepage,
        marketOnServerSide,
        regionInfo,
        optimizelyData,
        ...propsNotMigratedYetToRedux,
        ...(await serverSideTranslations(
          locale,
          [ALL_PAGES, HOME_PAGE, STORYBLOK],
          nextI18NextConfig as any
        ))
      }
    }
  }

export const getServerSideProps = withWrappers(getServerSidePropsInternal)
