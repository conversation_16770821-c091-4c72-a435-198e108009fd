import {
  ISbContentMangmntAPI,
  StoryblokStory,
  setComponents //@ts-ignore
} from '@storyblok/react/rsc'
import { GetServerSidePropsContext, PreviewData } from 'next'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { NextParsedUrlQuery } from 'next/dist/server/request-meta'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { ComponentType } from 'react'
import sanitizeHtml from 'sanitize-html'

import { Favicons } from '@core/components/Favicons'
import { Layout, PropsAllPages } from '@core/components/Layout/Layout'
import { getAppBuyerUrl } from '@core/config/url'
import { TokenBag } from '@core/hooks/useSyncSSO'
import { AppStore } from '@core/redux/store'
import { getPagesCommonData } from '@core/services/data'
import { APIResponse } from '@core/services/http/types'
import { commonServerSideAuthProcess, withWrappers } from '@core/ssr'
import { FeatureFlagName } from '@core/ssr/featureFlag/featureFlag.enum'
import { CountryCode } from '@core/types'
import { getMarketFromUrl } from '@core/utils'

import {
  AbTestExperimentProps,
  AdvancedCarouselProps,
  BannerImageProps,
  Container,
  ContentCardGridProps,
  CustomSignUpProps,
  GenericDynamicListProps,
  HeadlineBannerProps,
  HeroBannerProps,
  MarginCollapserProps,
  NewsletterWrapperProps,
  PersonalizedContentProps,
  PopularLinksProps,
  QuickFiltersCarouselProps,
  RetailMediaCarouselProps,
  RichContentProps,
  SeoContentProps,
  SeoFaqContentProps,
  StoryblokDividerProps,
  UspGridProps
} from '@shared/components'
import { RegionResponse } from '@shared/components/RegionSelector/types/region.type'
import { STORYBLOK_ROUTE_LANDING } from '@shared/constants/storyblok-routes'
import { getStoryblokData } from '@shared/services/get-storyblok-data'

import {
  ALL_PAGES,
  HOME_PAGE,
  LANDING_PAGE_ROUTE,
  OK,
  STORYBLOK
} from '@modules/product/constants'

// eslint-disable-next-line no-restricted-imports
import nextI18NextConfig from '../../../next-i18next.config'

const GenericDynamicList: ComponentType<GenericDynamicListProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.GenericDynamicList
  )
)

const BannerImage: ComponentType<BannerImageProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.BannerImage)
)

const AdvancedCarousel: ComponentType<AdvancedCarouselProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AdvancedCarousel
  )
)

const NewsletterWrapper: ComponentType<NewsletterWrapperProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.NewsletterWrapper
  )
)

const PersonalizedContent: ComponentType<PersonalizedContentProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.PersonalizedContent
    )
)

const PopularLinks: ComponentType<PopularLinksProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.PopularLinks)
)

const RichContent: ComponentType<RichContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.RichContent)
)

const UspGrid: ComponentType<UspGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.UspGrid)
)

const HeroBanner: ComponentType<HeroBannerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.HeroBanner)
)

const HeadlineBanner: ComponentType<HeadlineBannerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.HeadlineBanner
  )
)

const SeoContent: ComponentType<SeoContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.SeoContent)
)

const SeoFaqContent: ComponentType<SeoFaqContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.SeoFaqContent
  )
)

const AbTestExperiment: ComponentType<AbTestExperimentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AbTestExperiment
  )
)

const Divider: ComponentType<StoryblokDividerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.StoryblokDivider
  )
)

const RetailMediaCarousel: ComponentType<RetailMediaCarouselProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.RetailMediaCarousel
    )
)

const QuickFiltersCarousel: ComponentType<QuickFiltersCarouselProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.QuickFiltersCarousel
    )
)

const ContentCardGrid: ComponentType<ContentCardGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.ContentCardGrid
  )
)

const MarginCollapser: ComponentType<MarginCollapserProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.MarginCollapser
  )
)

const CustomSignUpWrapper: ComponentType<CustomSignUpProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.CustomSignUpWrapper
  )
)

interface Props extends PropsAllPages {
  storyblokData: APIResponse<ISbContentMangmntAPI>
  marketOnServerSide: CountryCode
  tokenBag: TokenBag
  locale: string
  regionInfo?: APIResponse<RegionResponse | []>
}

// eslint-disable-next-line import/no-default-export
export default function LandingPage({
  storyblokData,
  marketOnServerSide,
  tokenBag,
  locale,
  regionInfo,
  ...commonProps
}: Readonly<Props>) {
  const router = useRouter()
  const webAppUrl = getAppBuyerUrl(marketOnServerSide)

  setComponents({
    page: GenericDynamicList,
    banner: BannerImage,
    'advanced-carousel': AdvancedCarousel,
    'usp-grid': UspGrid,
    newsletter: NewsletterWrapper,
    abTestExperiment: AbTestExperiment,
    divider: Divider,
    'personalized-content': PersonalizedContent,
    'popular-links': PopularLinks,
    'rich-content': RichContent,
    'hero-banner': HeroBanner,
    headlineBanner: HeadlineBanner,
    'seo-content': SeoContent,
    'faq-content': SeoFaqContent,
    'retail-media-carousel': RetailMediaCarousel,
    'quick-filters-carousel': QuickFiltersCarousel,
    'content-card-grid': ContentCardGrid,
    'margin-collapser': MarginCollapser,
    'custom-sign-up': CustomSignUpWrapper
  })

  const getOGUrl = (): string => {
    return `${webAppUrl}${LANDING_PAGE_ROUTE}${router.query.categorySlugOrId}`
  }

  return (
    <>
      <Favicons market={marketOnServerSide} />
      <Layout
        {...commonProps}
        title={storyblokData.result.story?.name}
        descriptionOG={sanitizeHtml(storyblokData.result.story?.name)}
        urlOG={getOGUrl()}
        regionInfo={regionInfo}
      >
        <Container className="pt-0">
          {/* storyblok dynamic components */}
          {storyblokData.type === OK && (
            <StoryblokStory
              bridgeOptions={null}
              story={storyblokData.result.story}
            />
          )}
        </Container>
      </Layout>
    </>
  )
}

const getServerSidePropsInternal =
  (store: AppStore) =>
  async (
    context: GetServerSidePropsContext<NextParsedUrlQuery, PreviewData> & {
      featureFlags: Record<FeatureFlagName, boolean>
    }
  ) => {
    const {
      params: { slug },
      req,
      locale
    } = context

    const marketOnServerSide = getMarketFromUrl(req.headers.host)

    const [allPagesData, storyblokData] = await Promise.all([
      getPagesCommonData(context),
      getStoryblokData(context, `${STORYBLOK_ROUTE_LANDING}/${slug}`)
    ])

    const isStoryOk =
      storyblokData?.type === OK && !!storyblokData?.result?.story

    if (!isStoryOk) {
      // eslint-disable-next-line no-console
      console.error('LANDING PAGE LOADING ERROR')
      return {
        notFound: true
      }
    }

    const { regionInfo, ...propsNotMigratedYetToRedux } = allPagesData
    const { tokenBag } = await commonServerSideAuthProcess(
      store,
      context,
      allPagesData
    )

    // @INFO: This configuration will apply to the entire route
    const optimizelyData = !isStoryOk
      ? {}
      : {
          experimentKeys: [], // Add keys for Experiments if needed
          experimentAttributes: {
            market: marketOnServerSide
          }
        }

    return {
      props: {
        tokenBag,
        storyblokData,
        marketOnServerSide,
        locale,
        regionInfo,
        optimizelyData,
        ...propsNotMigratedYetToRedux,
        ...(await serverSideTranslations(
          locale,
          [ALL_PAGES, HOME_PAGE, STORYBLOK],
          nextI18NextConfig as any
        ))
      }
    }
  }

export const getServerSideProps = withWrappers(getServerSidePropsInternal)
