import { ISbStoryData } from '@storyblok/react/dist/types/types'
import {
  ISbContentMangmntAPI,
  StoryblokStory,
  setComponents //@ts-ignore
} from '@storyblok/react/rsc'
import { GetServerSidePropsContext, PreviewData, Redirect } from 'next'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { NextParsedUrlQuery } from 'next/dist/server/request-meta'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { ComponentType } from 'react'
import sanitizeHtml from 'sanitize-html'

import { Favicons } from '@core/components/Favicons'
import { Layout, PropsAllPages } from '@core/components/Layout/Layout'
import { getAppBuyerUrl } from '@core/config/url'
import { TokenBag, useSyncSSO } from '@core/hooks/useSyncSSO'
import { useUserLoginType } from '@core/hooks/useUserLoginType'
import {
  setCategory,
  setCategoryError,
  setCategoryIsLoading
} from '@core/redux/features/category/CategorySlice'
import { useSelectedCategory } from '@core/redux/features/category/hooks/useSelectedCategory'
import { AppStore } from '@core/redux/store'
import { getPagesCommonData } from '@core/services/data'
import { APIResponse } from '@core/services/http/types'
import { commonServerSideAuthProcess, withWrappers } from '@core/ssr'
import { FeatureFlagName } from '@core/ssr/featureFlag/featureFlag.enum'
import { CountryCode, Price, PriceType } from '@core/types'
import { getMarketFromUrl } from '@core/utils'

import { Container, PopUpModal } from '@shared/components'
import { BreadcrumbsSwitch } from '@shared/components/Breadcrumbs'
import { RegionResponse } from '@shared/components/RegionSelector/types/region.type'
import {
  AbTestExperimentProps,
  AdvancedCarouselProps,
  BrandListWrappertProps,
  GenericDynamicListProps,
  HeadlineBannerProps,
  HeroBannerProps,
  NewsletterWrapperProps,
  PersonalizedContentProps,
  PopularLinksProps,
  RetailMediaCarouselProps,
  RichContentProps,
  SeoContentProps,
  SeoFaqContentProps,
  StoryblokDividerProps,
  UspGridProps
} from '@shared/components/Storyblok'
import { STORYBLOK_ROUTE_CATEGORY } from '@shared/constants/storyblok-routes'
import { getStoryblokData } from '@shared/services/get-storyblok-data'
import { getDefaultStory } from '@shared/utils'
import { EXPERIMENT_NAMES } from '@shared/utils/optimizely.utils'

import {
  BrandListProps,
  CategoryPlpGridProps,
  RelatedCategoriesProps,
  SubCategoryGridProps
} from '@modules/category/components'
import { getCategoryDetails } from '@modules/category/services/get-category-details'
import { getCategoryProducts } from '@modules/category/services/get-category-products'
import { Category } from '@modules/homepage/model/search-suggestion'
import {
  ALL_PAGES,
  CATEGORY_ROUTE,
  ERROR,
  HOME_PAGE,
  OK,
  PLPS_PAGES,
  STORYBLOK
} from '@modules/product/constants'
import { API_USAGE_TAGS } from '@modules/search/constants'
import { PlpFiltersProvider } from '@modules/search/hooks/usePlpFilters'

// eslint-disable-next-line no-restricted-imports
import nextI18NextConfig from '../../../next-i18next.config'

const GenericDynamicList: ComponentType<GenericDynamicListProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.GenericDynamicList
  )
)

const AbTestExperiment: ComponentType<AbTestExperimentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AbTestExperiment
  )
)

const AdvancedCarousel: ComponentType<AdvancedCarouselProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.AdvancedCarousel
  )
)

const Divider: ComponentType<StoryblokDividerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.StoryblokDivider
  )
)

const NewsletterWrapper: ComponentType<NewsletterWrapperProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.NewsletterWrapper
  )
)

const PersonalizedContent: ComponentType<PersonalizedContentProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.PersonalizedContent
    )
)

const PopularLinks: ComponentType<PopularLinksProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.PopularLinks)
)

const RichContent: ComponentType<RichContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.RichContent)
)

const UspGrid: ComponentType<UspGridProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.UspGrid)
)

const HeroBanner: ComponentType<HeroBannerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.HeroBanner)
)

const HeadlineBanner: ComponentType<HeadlineBannerProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.HeadlineBanner
  )
)

const SeoContent: ComponentType<SeoContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.SeoContent)
)

const SeoFaqContent: ComponentType<SeoFaqContentProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then(
    (mod) => mod.SeoFaqContent
  )
)

const RetailMediaCarousel: ComponentType<RetailMediaCarouselProps> = dynamic(
  () =>
    import('@modules/shared/components/Storyblok').then(
      (mod) => mod.RetailMediaCarousel
    )
)

const CategoryPlpGrid: ComponentType<CategoryPlpGridProps> = dynamic(() =>
  import('@modules/category/components').then((mod) => mod.CategoryPlpGrid)
)

const SubCategoryGrid: ComponentType<SubCategoryGridProps> = dynamic(() =>
  import('@modules/category/components').then((mod) => mod.SubCategoryGrid)
)

const RelatedCategories: ComponentType<RelatedCategoriesProps> = dynamic(() =>
  import('@modules/category/components').then((mod) => mod.RelatedCategories)
)

const BrandListWrapper: ComponentType<BrandListWrappertProps> = dynamic(() =>
  import('@modules/shared/components/Storyblok').then((mod) => mod.BrandListWrapper)
)

interface Props extends PropsAllPages {
  category: APIResponse<Category>
  priceType: PriceType
  storyblokData: APIResponse<ISbContentMangmntAPI>
  marketOnServerSide: CountryCode
  tokenBag: TokenBag
  locale: string
  regionInfo?: APIResponse<RegionResponse | []>
}

// eslint-disable-next-line import/no-default-export
export default function CategoryPlpPage({
  category,
  priceType,
  storyblokData,
  marketOnServerSide,
  tokenBag,
  locale,
  regionInfo,
  ...commonProps
}: Readonly<Props>) {
  useSyncSSO(tokenBag)
  useUserLoginType()
  const router = useRouter()
  const webAppUrl = getAppBuyerUrl(marketOnServerSide)
  const defaultStory: ISbStoryData = getDefaultStory(category?.result?.name)
  const { isCategoryLoading, selectedCategory } = useSelectedCategory()

  setComponents({
    autoCategoryPlp: GenericDynamicList,
    'sub-category-grid': SubCategoryGrid,
    'advanced-carousel': AdvancedCarousel,
    'usp-grid': UspGrid,
    newsletter: NewsletterWrapper,
    abTestExperiment: AbTestExperiment,
    divider: Divider,
    'personalized-content': PersonalizedContent,
    'popular-links': PopularLinks,
    'rich-content': RichContent,
    'hero-banner': HeroBanner,
    headlineBanner: HeadlineBanner,
    'products-catalog': CategoryPlpGrid,
    'seo-content': SeoContent,
    'faq-content': SeoFaqContent,
    'retail-media-carousel': RetailMediaCarousel,
    'brand-list': BrandListWrapper,
    'related-categories': RelatedCategories
  })

  const getOGUrl = (): string => {
    return `${webAppUrl}${CATEGORY_ROUTE}${router.query.categorySlugOrId}`
  }

  return (
    <>
      {category.type !== 'ERROR' && (
        <>
          <Favicons market={marketOnServerSide} />
          <Layout
            {...commonProps}
            title={selectedCategory?.name}
            descriptionOG={sanitizeHtml(selectedCategory?.name)}
            urlOG={getOGUrl()}
            regionInfo={regionInfo}
          >
            {/*@TODO: Add page skeleton when category is still loading*/}
            {!isCategoryLoading && selectedCategory && (
              <>
                <PlpFiltersProvider
                  query={router?.query}
                  market={marketOnServerSide}
                  priceTypeFromCookie={priceType}
                  usageTag={API_USAGE_TAGS.category_page}
                >
                  <Container className="pt-0">
                    <BreadcrumbsSwitch
                      source="category"
                      categoryItem={category.result}
                    />
                    {/* storyblok dynamic components */}
                    {storyblokData.type === OK && (
                      <StoryblokStory
                        bridgeOptions={null}
                        story={storyblokData.result?.story}
                      />
                    )}
                    {storyblokData.type === ERROR && (
                      <StoryblokStory
                        bridgeOptions={null}
                        story={defaultStory}
                      />
                    )}
                  </Container>
                </PlpFiltersProvider>
                <PopUpModal />
              </>
            )}
          </Layout>
        </>
      )}
    </>
  )
}

function hasRedirectProperty(obj: any): obj is { redirect: Redirect } {
  return 'redirect' in obj
}

const getServerSidePropsInternal =
  (store: AppStore) =>
  async (
    context: GetServerSidePropsContext<NextParsedUrlQuery, PreviewData> & {
      featureFlags: Record<FeatureFlagName, boolean>
    }
  ) => {
    const {
      params: { categorySlugOrId },
      req,
      locale
    } = context
    const marketOnServerSide = getMarketFromUrl(req.headers.host)

    store.dispatch(setCategoryIsLoading(true))

    const [allPagesData, category, products, storyblokData] = await Promise.all(
      [
        getPagesCommonData(context),
        getCategoryDetails(context),
        getCategoryProducts(context, { isCheckingProductCount: true }),
        getStoryblokData(
          context,
          `${STORYBLOK_ROUTE_CATEGORY}/${categorySlugOrId}`
        )
      ]
    )

    if (hasRedirectProperty(category)) {
      return category as { redirect: Redirect }
    }

    const isProductListOK =
      products?.type === OK && !!products?.result?.totalCount

    if (!isProductListOK) {
      store.dispatch(setCategoryError('CATEGORY LOADING ERROR'))
      store.dispatch(setCategoryIsLoading(false))

      return {
        notFound: true
      }
    }

    const { regionInfo, ...propsNotMigratedYetToRedux } = allPagesData
    const { tokenBag } = await commonServerSideAuthProcess(
      store,
      context,
      allPagesData
    )

    store.dispatch(setCategory(category?.result))
    store.dispatch(setCategoryIsLoading(false))

    // @INFO: This configuration will apply to the entire route
    const optimizelyData = !isProductListOK
      ? {}
      : {
          experimentKeys: [EXPERIMENT_NAMES.MACO_OPEN_PDP_IN_NEW_TAB],
          experimentAttributes: {
            market: marketOnServerSide
          }
        }

    return {
      props: {
        tokenBag,
        category,
        priceType: allPagesData?.priceTypeFromCookie || Price.b2b,
        storyblokData,
        marketOnServerSide,
        locale,
        regionInfo,
        optimizelyData,
        ...propsNotMigratedYetToRedux,
        ...(await serverSideTranslations(
          locale,
          [ALL_PAGES, HOME_PAGE, PLPS_PAGES, STORYBLOK],
          nextI18NextConfig as any
        ))
      }
    }
  }

export const getServerSideProps = withWrappers(getServerSidePropsInternal)
