import { BLACK_DEAL_TAGS } from '@modules/product/constants'
import { ProductIndexResponse } from '@modules/product/types'
import { ItemMarket } from '@modules/product/types/spi'

import { ProductIndexAttributes } from './product-index-attributes-mock'

export const ProductIndexMock: ProductIndexResponse = {
  idMarket: 'de',
  tags: ['mm', 'klimaanlagen-and-ventilatoren'],
  status: 1,
  offers: [
    {
      id: 'b03ba27c-774b-4040-952e-68b5e7be1eba',
      quantity: 67,
      freightForwarding: false,
      organization: {
        id: '118ede85-fd10-42aa-8ee5-6fbc2553de02',
        name: 'METRO Markets',
        shopName: 'METRO'
      },
      promotion: {
        active: true,
        endDate: '2023-06-30 21:59:00',
        nextChangeDate: null,
        items: [],
        itemsActive: []
      },
      businessModel: { value: 1 },
      offerServices: [
        {
          id: 'df9095b8-63af-4e5c-8e52-c6e9bd1c8c78',
          name: 'Premise',
          active: true
        }
      ],
      originRegionInfo: {
        region: { value: 'DE_MAIN' },
        vatRate: '19',
        price: { currency: 'EUR', vatRate: '19', net: '659', gross: '784.21' },
        msrpPrice: null,
        referencePrice: null,
        shipping: { time: { from: '1', to: '2' } },
        basePrice: null,
        unitPrice: null,
        volumePrices: []
      },
      destinationRegionInfo: {
        region: { value: 'DE_MAIN' },
        vatRate: '19',
        price: { currency: 'EUR', vatRate: '19', net: '659', gross: '784.21' },
        msrpPrice: null,
        referencePrice: null,
        shipping: {
          time: { from: '1', to: '2' },
          price: { currency: 'EUR', vatRate: '19', net: '6.59', gross: '7.21' }
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: []
      },
      labels: ['promotion'],
      shippingGroup: {
        id: '896de41d-ac34-48b2-b67d-6648604ff614',
        name: 'DE_Parcels',
        shippingCost: {
          destinationRegion: { value: 'DE_MAIN' },
          unitCost: {
            currency: 'EUR',
            vatRate: '19',
            net: '4.16',
            gross: '4.95'
          },
          extraUnitCost: {
            currency: 'EUR',
            vatRate: '19',
            net: '0',
            gross: '0'
          },
          thresholdEnabled: true,
          thresholdAmount: {
            currency: 'EUR',
            vatRate: '19',
            net: '49',
            gross: '58.31'
          }
        },
        shippingCosts: {
          DE_MAIN: {
            destinationRegion: {
              value: 'DE_MAIN'
            },
            unitCost: {
              currency: 'EUR',
              vatRate: '19.0',
              net: '4.16',
              gross: '4.95'
            },
            extraUnitCost: {
              currency: 'EUR',
              vatRate: '19.0',
              net: '0',
              gross: '0'
            },
            thresholdEnabled: true,
            thresholdAmount: {
              currency: 'EUR',
              vatRate: '19.0',
              net: '79',
              gross: '94.01'
            }
          }
        }
      }
    }
  ],
  bestOffer: {
    smart: {
      b2b: ['b03ba27c-774b-4040-952e-68b5e7be1eba'],
      b2c: ['b03ba27c-774b-4040-952e-68b5e7be1eba']
    },
    cheapest: {
      b2b: ['b03ba27c-774b-4040-952e-68b5e7be1eba'],
      b2c: ['b03ba27c-774b-4040-952e-68b5e7be1eba']
    }
  },
  bestOfferV2: {
    cheapest: {
      DE_MAIN: {
        b2b: ['b03ba27c-774b-4040-952e-68b5e7be1eba'],
        b2c: ['b03ba27c-774b-4040-952e-68b5e7be1eba']
      }
    }
  },
  promotion: {
    active: true,
    endDate: '2023-06-30 21:59:00',
    nextChangeDate: '2023-06-30 21:59:00',
    items: [
      {
        organizationId: '118ede85-fd10-42aa-8ee5-6fbc2553de02',
        itemId: '06c46e7f-66a6-4810-a36f-1d9361bf47bf',
        startDate: '2023-04-17 10:00:00',
        endDate: '2023-06-30 21:59:00',
        promoTag: 'mm',
        destinationRegionId: 'DE_MAIN'
      }
    ],
    itemsActive: [
      {
        organizationId: '118ede85-fd10-42aa-8ee5-6fbc2553de02',
        itemId: '06c46e7f-66a6-4810-a36f-1d9361bf47bf',
        startDate: '2023-04-17 10:00:00',
        endDate: '2023-06-30 21:59:00',
        promoTag: 'mm',
        destinationRegionId: 'DE_MAIN'
      }
    ]
  },
  category: {
    id: '9cb1e31e-80db-4203-80cb-d71874f252c9',
    name: 'Luftreiniger',
    slug: 'luftreiniger',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: []
  },
  breadcrumb: {
    id: '9cb1e31e-80db-4203-80cb-d71874f252c9',
    name: 'Luftreiniger',
    slug: 'luftreiniger',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: [
      {
        id: 'c0201fd7-e454-4380-8ae7-12672e1abdf6',
        name: 'Luftbehandlung',
        slug: 'luftbehandlung',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: []
      },
      {
        id: 'baaaf5ab-3228-4e7d-b255-8c12d180ada2',
        name: 'Raumklima',
        slug: 'raumklima',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: []
      },
      {
        id: 'f2ffd745-325f-49b0-be1c-605873728ab1',
        name: 'Hotel & Gastraum',
        slug: 'hotel-gastraum',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: []
      }
    ]
  },
  firstSaleOfferedAt: '2022-11-28T02:22:26+00:00',
  createdAt: '2022-07-07T09:58:02+00:00',
  updatedAt: '2023-05-24T22:09:04+00:00',
  originOfferUpdatedAt: '2023-05-24T22:08:41+00:00',
  originOfferInventoryUpdatedAt: '2023-05-24T22:08:41+00:00',
  item: {
    idItem: '06c46e7f-66a6-4810-a36f-1d9361bf47bf',
    hasVariants: true,
    mid: 'AAA0001003148',
    gtin: '5025155056127',
    mpn: '379491-01',
    name: 'Dyson PH04 Luftreiniger',
    description: null,
    descriptionLong:
      'Trockene Luft kann zu Dehydrierung f\u00fchren. Der Dyson Luftbefeuchter mitLuftreinigungsfunktion h\u00e4lt automatisch das richtige Feuchtigkeitsniveau in der Raumluftaufrecht. F\u00fcr eine angenehmere Umgebung das ganze Jahr.Der Dyson Purifier Humidify+Cool\u2122 verf\u00fcgt \u00fcber ein versiegeltes 360\u02da- Filtrationssystem. Es kombiniert einen Aktivkohlefilter zum Entfernen von Gasen und Ger\u00fcchen und einen versiegelten HEPA-Filter zum Entfernen von 99,95 % derultrafeinen Partikel.',
    brand: {
      id: '446b59ad-8ca5-480b-bd6e-3ae8452498a1',
      name: 'Dyson',
      slug: 'dyson'
    },
    brandV2: {
      id: '446b59ad-8ca5-480b-bd6e-3ae8452498a1',
      name: 'Dyson',
      slug: 'dyson'
    },
    manufacturer: { id: '1691340a-caf8-4526-b056-f8fdbe04ea55', name: 'Dyson' },
    keyFeatures: [
      'Vollst\u00e4ndige Versiegelung nach HEPA-13 Standard',
      'Erkennt und zersetzt Formaldehyd',
      'Vermischt und verteilt die Luft in drei Modi',
      'Luftreiniger von Dyson mit Befeuchtungs- und Ventilatorfunktion',
      'Mehr als 36 Stunden lang hygienische Befeuchtung'
    ],
    attributes: ProductIndexAttributes,
    artificialHighlights: [
      {
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Farbe',
        value: 'silber',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      },
      {
        id: '7f89dd97-df8e-473b-9551-8f6390133b15',
        code: 'material',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Material',
        value: 'Polyester',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      },
      {
        id: 'c9b06ca1-66b6-4ef8-b311-2ff1545b5225',
        code: 'product_package_dimensions_aggregate',
        type: 3,
        aggregated: true,
        translatable: true,
        label:
          'CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PACKAGE_DIMENSIONS.LABEL',
        value: '%height% x %width% x %length%',
        frontend: '10',
        displayUnit: null,
        substitutes: {
          width: {
            id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
            code: 'width',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Breite inkl. Verpackung',
            value: '10',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          },
          height: {
            id: '738a2b96-e334-4938-a12f-372b8212ccb3',
            code: 'height',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Höhe inkl. Verpackung',
            value: '30',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          },
          length: {
            id: '816b9d57-58b4-41ca-a917-98f99bf94198',
            code: 'length',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Länge/Tiefe inkl. Verpackung',
            value: '10',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          }
        }
      },
      {
        id: '28cc8aa7-7688-40f3-9988-2352a893883e',
        code: 'weight_gross',
        type: 6,
        aggregated: false,
        translatable: false,
        label: 'Gewicht inkl. Verpackung',
        value: '2.4',
        frontend: '1',
        displayUnit: 'kg',
        substitutes: null
      },
      {
        id: '466c3e55-7193-4a96-9c7f-f579e9787640',
        code: 'country_of_manufacture',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Herstellungsland',
        value: 'Deutschland',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      }
    ],
    images: [
      {
        id: '82960c82-2c96-461d-92a7-bf2795532beb',
        url: 'https://pp-de-metro-markets.imgix.net/item_image/82960c82-2c96-461d-92a7-bf2795532beb',
        main: true,
        alt: null,
        sizes: [
          {
            originalImageId: 'item_image/0acee3c5-2772-48c3-b408-57c0f80941e9',
            id: 'item_image/0acee3c5-2772-48c3-b408-57c0f80941e9id',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/82960c82-2c96-461d-92a7-bf2795532beb?h=80&ixlib=php-2.3.0&q=100&w=80',
            width: 80,
            height: 80
          },
          {
            originalImageId: 'item_image/ae590ba3-4978-466c-9c05-a1e51fbbffff',
            id: 'item_image/ae590ba3-4978-466c-9c05-a1e51fbbffffid',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/82960c82-2c96-461d-92a7-bf2795532beb?h=148&ixlib=php-2.3.0&q=100&w=148',
            width: 148,
            height: 148
          },
          {
            originalImageId: 'item_image/fa2b59e2-78b8-4234-8840-fb32cadae646',
            id: 'item_image/fa2b59e2-78b8-4234-8840-fb32cadae646id',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/82960c82-2c96-461d-92a7-bf2795532beb?h=370&ixlib=php-2.3.0&q=100&w=370',
            width: 370,
            height: 370
          }
        ]
      }
    ],
    idCategory: '9cb1e31e-80db-4203-80cb-d71874f252c9',
    commercialUse: false,
    createdAt: '2022-07-07T09:57:57+00:00',
    updatedAt: '2023-05-24T22:09:03+00:00',
    originUpdatedAt: null,
    originItemUpdatedAt: '2023-02-01T14:15:50+00:00'
  }
}

export const ProductWithVolumePrice: ProductIndexResponse = {
  idMarket: 'de',
  tags: ['egwrg', 'wefwerfgq3rfd', 'weweffe'],
  status: 1,
  offers: [
    {
      id: '9720d2b2-78e6-4aff-b152-f0cca23bb2f6',
      quantity: 480,
      freightForwarding: false,
      organization: {
        id: 'b4b309e0-9f53-4c9b-b639-3913b7131996',
        name: 'SMS PreProd Metro',
        shopName: 'SMS test'
      },
      promotion: null,
      businessModel: {
        value: 1
      },
      offerServices: [
        {
          id: 'df9095b8-63af-4e5c-8e52-c6e9bd1c8c78',
          name: 'Premise',
          active: true
        }
      ],
      originRegionInfo: {
        region: {
          value: 'DE_MAIN'
        },
        vatRate: '19',
        price: {
          currency: 'EUR',
          vatRate: '19',
          net: '33.3',
          gross: '39.63'
        },
        msrpPrice: null,
        referencePrice: null,
        shipping: {
          time: {
            from: '1',
            to: '3'
          }
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: [
          {
            quantity: 6,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '31.79',
              gross: '37.83'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 5,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '1.51',
                gross: '1.8'
              }
            }
          },
          {
            quantity: 10,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '30.35',
              gross: '36.12'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 9,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '2.95',
                gross: '3.51'
              }
            }
          }
        ]
      },
      destinationRegionInfo: {
        region: {
          value: 'DE_MAIN'
        },
        vatRate: '19',
        price: {
          currency: 'EUR',
          vatRate: '19',
          net: '33.3',
          gross: '39.63'
        },
        msrpPrice: null,
        referencePrice: null,
        shipping: {
          time: {
            from: '1',
            to: '3'
          }
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: [
          {
            quantity: 6,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '31.79',
              gross: '37.83'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 5,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '1.51',
                gross: '1.8'
              }
            }
          },
          {
            quantity: 10,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '30.35',
              gross: '36.12'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 9,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '2.95',
                gross: '3.51'
              }
            }
          }
        ]
      },
      labels: null,
      shippingGroup: null
    }
  ],
  bestOffer: {
    smart: {
      b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
      b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
    },
    cheapest: {
      b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
      b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
    }
  },
  bestOfferV2: {
    cheapest: {
      DE_MAIN: {
        b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
        b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
      }
    }
  },
  promotion: null,
  category: {
    id: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    name: 'Outdoor-Stühle',
    slug: 'outdoor-stuhle',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: null
  },
  breadcrumb: {
    id: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    name: 'Outdoor-Stühle',
    slug: 'outdoor-stuhle',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: [
      {
        id: '5e66271a-89b0-48c6-8472-4d30a4dfe1f6',
        name: 'Outdoor-Möbel',
        slug: 'outdoor-mobel',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: null
      },
      {
        id: 'f2ffd745-325f-49b0-be1c-605873728ab1',
        name: 'Hotel & Gastraum',
        slug: 'hotel-gastraum',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: null
      }
    ]
  },
  firstSaleOfferedAt: '2021-11-23T07:36:08+00:00',
  createdAt: '2022-03-01T07:08:53+00:00',
  updatedAt: '2023-06-12T15:47:32+00:00',
  originOfferUpdatedAt: '2023-04-25T01:03:33+00:00',
  originOfferInventoryUpdatedAt: '2023-05-15T11:37:18+00:00',
  item: {
    idItem: 'ef14d5d9-a111-4b37-bcc7-1f8d38f8ce7a',
    hasVariants: true,
    mid: 'AAA0000016347',
    gtin: '4337147824173',
    mpn: '892615',
    name: 'METRO Professional Stapelstuhl, Aluminium',
    description: '',
    descriptionLong:
      'Der METRO Professional Stapelstuhl hat die Maße von 67 x 54 x 74 cm (L x B x H). Die Armlehne hat einen Durchmesser von 25 mm und eine Dicke von 1,1 mm. Das Gestell des Stuhls ist aus robustem, pulverbeschichtetem Aluminium gefertigt und die Sitzfläche besteht aus Allwetter-Polyethylen-Rattan. Das Produkt ist leicht stapelbar und erfüllt die allgemeinen Sicherheitsanforderungen für den gewerblichen Außeneinsatz.',
    brand: {
      id: 'f83d5d3b-42be-4840-a058-df9aa73ba8e6',
      name: 'METRO Professional',
      slug: 'metro-professional'
    },
    brandV2: {
      id: 'f83d5d3b-42be-4840-a058-df9aa73ba8e6',
      name: 'METRO Professional',
      slug: 'metro-professional'
    },
    manufacturer: {
      id: 'e7653c9d-c8af-404f-8a61-6620db38937c',
      name: 'Dongyang Elephant Hardware Products Co.,'
    },
    keyFeatures: null,
    attributes: [
      {
        id: '36d3dac9-47a8-4eeb-b8b1-1b7096a97001',
        code: 'foot_rest',
        type: {
          value: 2
        },
        label: 'Fußstütze',
        value: '',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
        code: 'width',
        type: {
          value: 6
        },
        label: 'Breite inkl. Verpackung',
        value: '0.54',
        displayValue: '54',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '4b2e34de-ace6-45d8-a698-d4cdaf2d4311',
        code: 'foldable',
        type: {
          value: 2
        },
        label: 'Faltbar',
        value: '',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        type: {
          value: 1
        },
        label: 'Farbe',
        value: 'grau',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '738a2b96-e334-4938-a12f-372b8212ccb3',
        code: 'height',
        type: {
          value: 6
        },
        label: 'Höhe inkl. Verpackung',
        value: '0.74',
        displayValue: '74',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '816b9d57-58b4-41ca-a917-98f99bf94198',
        code: 'length',
        type: {
          value: 6
        },
        label: 'Länge/Tiefe inkl. Verpackung',
        value: '0.67',
        displayValue: '67',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '9ca23ffa-6efc-42a1-be67-28e2b025f5cc',
        code: 'waterproof',
        type: {
          value: 1
        },
        label: 'Wasserdichtigkeit',
        value: 'wasserdurchlässig',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: 'a019fa71-9038-4942-832a-42e24919f8ea',
        code: 'height_product',
        type: {
          value: 6
        },
        label: 'Produkthöhe',
        value: '0.74',
        displayValue: '74',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: 'b4547557-2f3d-448d-92a8-f8395a8f4e6a',
        code: 'length_depth_product',
        type: {
          value: 6
        },
        label: 'Produktlänge/-tiefe',
        value: '0.67',
        displayValue: '67',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: 'd460ced6-95cc-4f24-b4d3-a04effbc9d1a',
        code: 'weight_net',
        type: {
          value: 6
        },
        label: 'Produktgewicht',
        value: '34',
        displayValue: '34',
        frontend: '1',
        measure: {
          displayUnit: 'kg',
          baseUnit: 'kg'
        }
      },
      {
        id: 'e625f6d2-4482-4406-a2f7-854dfe6b836d',
        code: 'width_product',
        type: {
          value: 6
        },
        label: 'Produktbreite',
        value: '0.54',
        displayValue: '54',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b74',
        code: 'custom_item_attributes_in_total',
        type: {
          value: 4
        },
        label: 'custom_item_attributes_in_total',
        value: '27',
        displayValue: null,
        frontend: '0',
        measure: null
      }
    ],
    images: [
      {
        id: '0beca270-765b-407d-af44-fa570635b84a',
        url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a',
        alt: null,
        main: true,
        sizes: [
          {
            id: 'item_image/79bd44d0-e60b-458c-9461-a9d9a3812965',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=80&ixlib=php-2.3.0&q=&w=80',
            width: 80,
            height: 80,
            originalImageId: ''
          },
          {
            id: 'item_image/2b15f9bb-f3c5-4410-ab81-743763196f86',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=148&ixlib=php-2.3.0&q=&w=148',
            width: 148,
            height: 148,
            originalImageId: ''
          },
          {
            id: 'item_image/8d189c68-5510-4448-97f1-157d125f0df2',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=370&ixlib=php-2.3.0&q=&w=370',
            width: 370,
            height: 370,
            originalImageId: ''
          }
        ]
      }
    ],
    idCategory: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    commercialUse: false,
    createdAt: '2019-09-02T06:27:59+00:00',
    updatedAt: '2023-06-25T01:35:00+00:00',
    originUpdatedAt: null,
    originItemUpdatedAt: '2019-09-04T06:00:10+00:00'
  }
}

export const BlackDealsProduct: ProductIndexResponse = {
  idMarket: 'de',
  tags: ['egwrg', 'wefwerfgq3rfd', BLACK_DEAL_TAGS[0]],
  status: 1,
  offers: [
    {
      id: '9720d2b2-78e6-4aff-b152-f0cca23bb2f6',
      quantity: 480,
      freightForwarding: false,
      organization: {
        id: 'b4b309e0-9f53-4c9b-b639-3913b7131996',
        name: 'SMS PreProd Metro',
        shopName: 'SMS test'
      },
      promotion: null,
      businessModel: {
        value: 1
      },
      offerServices: [
        {
          id: 'df9095b8-63af-4e5c-8e52-c6e9bd1c8c78',
          name: 'Premise',
          active: true
        }
      ],
      originRegionInfo: {
        region: {
          value: 'DE_MAIN'
        },
        vatRate: '19',
        price: {
          currency: 'EUR',
          vatRate: '19',
          net: '33.3',
          gross: '39.63'
        },
        msrpPrice: null,
        referencePrice: null,
        shipping: {
          time: {
            from: '1',
            to: '3'
          }
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: [
          {
            quantity: 6,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '31.79',
              gross: '37.83'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 5,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '1.51',
                gross: '1.8'
              }
            }
          },
          {
            quantity: 10,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '30.35',
              gross: '36.12'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 9,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '2.95',
                gross: '3.51'
              }
            }
          }
        ]
      },
      destinationRegionInfo: {
        region: {
          value: 'DE_MAIN'
        },
        vatRate: '19',
        price: {
          currency: 'EUR',
          vatRate: '19',
          net: '33.3',
          gross: '39.63'
        },
        msrpPrice: null,
        referencePrice: null,
        shipping: {
          time: {
            from: '1',
            to: '3'
          }
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: [
          {
            quantity: 6,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '31.79',
              gross: '37.83'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 5,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '1.51',
                gross: '1.8'
              }
            }
          },
          {
            quantity: 10,
            price: {
              currency: 'EUR',
              vatRate: '19',
              net: '30.35',
              gross: '36.12'
            },
            unitPrice: null,
            basePrice: null,
            savePrice: {
              percentage: 9,
              price: {
                currency: 'EUR',
                vatRate: '19',
                net: '2.95',
                gross: '3.51'
              }
            }
          }
        ]
      },
      labels: null,
      shippingGroup: null
    }
  ],
  bestOffer: {
    smart: {
      b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
      b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
    },
    cheapest: {
      b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
      b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
    }
  },
  bestOfferV2: {
    cheapest: {
      DE_MAIN: {
        b2b: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6'],
        b2c: ['9720d2b2-78e6-4aff-b152-f0cca23bb2f6']
      }
    }
  },
  promotion: null,
  category: {
    id: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    name: 'Outdoor-Stühle',
    slug: 'outdoor-stuhle',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: null
  },
  breadcrumb: {
    id: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    name: 'Outdoor-Stühle',
    slug: 'outdoor-stuhle',
    description: null,
    energyEfficiencyRange: null,
    parent: null,
    parents: [
      {
        id: '5e66271a-89b0-48c6-8472-4d30a4dfe1f6',
        name: 'Outdoor-Möbel',
        slug: 'outdoor-mobel',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: null
      },
      {
        id: 'f2ffd745-325f-49b0-be1c-605873728ab1',
        name: 'Hotel & Gastraum',
        slug: 'hotel-gastraum',
        description: null,
        energyEfficiencyRange: null,
        parent: null,
        parents: null
      }
    ]
  },
  firstSaleOfferedAt: '2021-11-23T07:36:08+00:00',
  createdAt: '2022-03-01T07:08:53+00:00',
  updatedAt: '2023-06-12T15:47:32+00:00',
  originOfferUpdatedAt: '2023-04-25T01:03:33+00:00',
  originOfferInventoryUpdatedAt: '2023-05-15T11:37:18+00:00',
  item: {
    idItem: 'ef14d5d9-a111-4b37-bcc7-1f8d38f8ce7a',
    hasVariants: true,
    mid: 'AAA0000016347',
    gtin: '4337147824173',
    mpn: '892615',
    name: 'METRO Professional Stapelstuhl, Aluminium',
    description: '',
    descriptionLong:
      'Der METRO Professional Stapelstuhl hat die Maße von 67 x 54 x 74 cm (L x B x H). Die Armlehne hat einen Durchmesser von 25 mm und eine Dicke von 1,1 mm. Das Gestell des Stuhls ist aus robustem, pulverbeschichtetem Aluminium gefertigt und die Sitzfläche besteht aus Allwetter-Polyethylen-Rattan. Das Produkt ist leicht stapelbar und erfüllt die allgemeinen Sicherheitsanforderungen für den gewerblichen Außeneinsatz.',
    brand: {
      id: 'f83d5d3b-42be-4840-a058-df9aa73ba8e6',
      name: 'METRO Professional',
      slug: 'metro-professional'
    },
    brandV2: {
      id: 'f83d5d3b-42be-4840-a058-df9aa73ba8e6',
      name: 'METRO Professional',
      slug: 'metro-professional'
    },
    manufacturer: {
      id: 'e7653c9d-c8af-404f-8a61-6620db38937c',
      name: 'Dongyang Elephant Hardware Products Co.,'
    },
    keyFeatures: null,
    attributes: [
      {
        id: '36d3dac9-47a8-4eeb-b8b1-1b7096a97001',
        code: 'foot_rest',
        type: {
          value: 2
        },
        label: 'Fußstütze',
        value: '',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
        code: 'width',
        type: {
          value: 6
        },
        label: 'Breite inkl. Verpackung',
        value: '0.54',
        displayValue: '54',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '4b2e34de-ace6-45d8-a698-d4cdaf2d4311',
        code: 'foldable',
        type: {
          value: 2
        },
        label: 'Faltbar',
        value: '',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        type: {
          value: 1
        },
        label: 'Farbe',
        value: 'grau',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: '738a2b96-e334-4938-a12f-372b8212ccb3',
        code: 'height',
        type: {
          value: 6
        },
        label: 'Höhe inkl. Verpackung',
        value: '0.74',
        displayValue: '74',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '816b9d57-58b4-41ca-a917-98f99bf94198',
        code: 'length',
        type: {
          value: 6
        },
        label: 'Länge/Tiefe inkl. Verpackung',
        value: '0.67',
        displayValue: '67',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '9ca23ffa-6efc-42a1-be67-28e2b025f5cc',
        code: 'waterproof',
        type: {
          value: 1
        },
        label: 'Wasserdichtigkeit',
        value: 'wasserdurchlässig',
        displayValue: null,
        frontend: '1',
        measure: null
      },
      {
        id: 'a019fa71-9038-4942-832a-42e24919f8ea',
        code: 'height_product',
        type: {
          value: 6
        },
        label: 'Produkthöhe',
        value: '0.74',
        displayValue: '74',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: 'b4547557-2f3d-448d-92a8-f8395a8f4e6a',
        code: 'length_depth_product',
        type: {
          value: 6
        },
        label: 'Produktlänge/-tiefe',
        value: '0.67',
        displayValue: '67',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: 'd460ced6-95cc-4f24-b4d3-a04effbc9d1a',
        code: 'weight_net',
        type: {
          value: 6
        },
        label: 'Produktgewicht',
        value: '34',
        displayValue: '34',
        frontend: '1',
        measure: {
          displayUnit: 'kg',
          baseUnit: 'kg'
        }
      },
      {
        id: 'e625f6d2-4482-4406-a2f7-854dfe6b836d',
        code: 'width_product',
        type: {
          value: 6
        },
        label: 'Produktbreite',
        value: '0.54',
        displayValue: '54',
        frontend: '1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      {
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b74',
        code: 'custom_item_attributes_in_total',
        type: {
          value: 4
        },
        label: 'custom_item_attributes_in_total',
        value: '27',
        displayValue: null,
        frontend: '0',
        measure: null
      }
    ],
    images: [
      {
        id: '0beca270-765b-407d-af44-fa570635b84a',
        url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a',
        alt: null,
        main: true,
        sizes: [
          {
            id: 'item_image/79bd44d0-e60b-458c-9461-a9d9a3812965',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=80&ixlib=php-2.3.0&q=&w=80',
            width: 80,
            height: 80,
            originalImageId: ''
          },
          {
            id: 'item_image/2b15f9bb-f3c5-4410-ab81-743763196f86',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=148&ixlib=php-2.3.0&q=&w=148',
            width: 148,
            height: 148,
            originalImageId: ''
          },
          {
            id: 'item_image/8d189c68-5510-4448-97f1-157d125f0df2',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/0beca270-765b-407d-af44-fa570635b84a?h=370&ixlib=php-2.3.0&q=&w=370',
            width: 370,
            height: 370,
            originalImageId: ''
          }
        ]
      }
    ],
    idCategory: 'de0b1d02-d769-4756-95a9-5c61b9ff8fbf',
    commercialUse: false,
    createdAt: '2019-09-02T06:27:59+00:00',
    updatedAt: '2023-06-25T01:35:00+00:00',
    originUpdatedAt: null,
    originItemUpdatedAt: '2019-09-04T06:00:10+00:00'
  }
}

export const ProductIndexMockV2: ItemMarket = {
  status: 1,
  idMarket: 'de',
  tags: [],
  offers: {
    '51c2dee2-7632-428c-a7e1-e476e1e991c7': {
      businessModel: 1,
      id: '51c2dee2-7632-428c-a7e1-e476e1e991c7',
      quantity: 95,
      freightForwarding: false,
      originRegionInfo: {
        region: 'DE_MAIN',
        price: { currency: 'EUR', vatRate: '19', net: '659', gross: '784.21' },
        referencePrice: null,
        basePrice: null,
        unitPrice: null,
        volumePrices: []
      },
      destinationRegionInfo: {
        region: 'DE_MAIN',
        price: {
          net: '96',
          gross: '114.24',
          vatRate: '19',
          currency: 'EUR'
        },
        referencePrice: null,
        basePrice: null,
        unitPrice: {
          unitType: 1,
          price: {
            net: '16',
            gross: '19.04',
            vatRate: '19',
            currency: 'EUR'
          },
          unitCount: 6,
          referencePrice: null
        },
        volumePrices: [
          {
            quantity: 2,
            price: {
              net: '91.2',
              gross: '108.53',
              vatRate: '19',
              currency: 'EUR'
            },
            savePrice: {
              price: {
                net: '4.8',
                gross: '5.71',
                vatRate: '19',
                currency: 'EUR'
              },
              percentage: '5'
            },
            basePrice: null,
            unitPrice: {
              unitType: 1,
              price: {
                net: '15.2',
                gross: '18.09',
                vatRate: '19',
                currency: 'EUR'
              },
              unitCount: 6,
              referencePrice: null
            }
          },
          {
            quantity: 3,
            price: {
              net: '86.4',
              gross: '102.82',
              vatRate: '19',
              currency: 'EUR'
            },
            savePrice: {
              price: {
                net: '9.6',
                gross: '11.42',
                vatRate: '19',
                currency: 'EUR'
              },
              percentage: '10'
            },
            basePrice: null,
            unitPrice: {
              unitType: 1,
              price: {
                net: '14.4',
                gross: '17.14',
                vatRate: '19',
                currency: 'EUR'
              },
              unitCount: 6,
              referencePrice: null
            }
          },
          {
            quantity: 4,
            price: {
              net: '81.6',
              gross: '97.10',
              vatRate: '19',
              currency: 'EUR'
            },
            savePrice: {
              price: {
                net: '14.4',
                gross: '17.14',
                vatRate: '19',
                currency: 'EUR'
              },
              percentage: '15'
            },
            basePrice: null,
            unitPrice: {
              unitType: 1,
              price: {
                net: '13.6',
                gross: '16.18',
                vatRate: '19',
                currency: 'EUR'
              },
              unitCount: 6,
              referencePrice: null
            }
          }
        ]
      },
      organization: {
        id: 'e13514f4-18fa-479e-88d7-e79eb3e02ef2',
        name: 'Nader Ikladious',
        shopName: 'Nader Ikladious Shop'
      },
      shippingTime: {
        from: '2',
        to: '4'
      },
      shippingGroup: {
        shippingCosts: {
          DE_MAIN: [
            {
              unitCost: {
                currency: 'EUR',
                vatRate: '19',
                net: '4.16',
                gross: '4.95'
              },
              extraUnitCost: {
                currency: 'EUR',
                vatRate: '19',
                net: '0',
                gross: '0'
              },
              thresholdEnabled: true,
              thresholdAmount: {
                currency: 'EUR',
                vatRate: '19',
                net: '49',
                gross: '58.31'
              }
            }
          ]
        }
      },
      labels: {
        DE_MAIN: ['volume_prices']
      },
      offerServices: [
        {
          id: 'df9095b8-63af-4e5c-8e52-c6e9bd1c8c78',
          name: 'Premise',
          active: true
        }
      ],
      valueAddedServices: []
    }
  },
  bestOffer: {
    cheapest: {
      DE_MAIN: {
        b2b: ['51c2dee2-7632-428c-a7e1-e476e1e991c7'],
        b2c: ['51c2dee2-7632-428c-a7e1-e476e1e991c7']
      }
    }
  },
  breadcrumb: {
    id: '1347a488-9977-4715-94ea-8c00a517a557',
    name: 'Suppenteller',
    slug: 'suppenteller-professionell',
    energyEfficiencyRange: null,
    parents: [
      {
        id: 'a036707a-3150-4d9b-aba5-3c49010ead71',
        name: 'Teller',
        slug: 'teller',
        energyEfficiencyRange: null
      },
      {
        id: 'd6dc9cd0-5271-4995-b205-e7b0ceb6fa47',
        name: 'Geschirr',
        slug: 'geschirr-professionell',
        energyEfficiencyRange: null
      },
      {
        id: '9721e9e7-78a6-47b6-9168-85538866fd52',
        name: 'Gedeckter Tisch',
        slug: 'gedeckter-tisch-professionell',
        energyEfficiencyRange: null
      },
      {
        id: 'f2ffd745-325f-49b0-be1c-605873728ab1',
        name: 'Hotel & Gastraum',
        slug: 'hotel-gastraum',
        energyEfficiencyRange: null
      }
    ]
  },
  item: {
    idItem: '1fba0569-f654-4a19-a514-e1622f74418a',
    mid: 'AAA0000496661',
    idContentProvider: 'bbad3871-609f-4675-b909-116a0b6a8472',
    idCategory: '1347a488-9977-4715-94ea-8c00a517a557',
    attributes: {
      color: {
        type: 1,
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        frontend: '1',
        label: 'Farbe',
        value: 'grau',
        displayValue: null,
        measure: null
      },
      shape: {
        type: 1,
        id: 'cfa1aa7b-b4f8-4115-9ffa-a6979aecc69a',
        code: 'shape',
        frontend: '1',
        label: 'Form',
        value: 'rund',
        displayValue: null,
        measure: null
      },
      width: {
        type: 6,
        id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
        code: 'width',
        frontend: '1',
        label: 'Breite inkl. Verpackung',
        value: '0.21',
        displayValue: '21',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      height: {
        type: 6,
        id: '738a2b96-e334-4938-a12f-372b8212ccb3',
        code: 'height',
        frontend: '1',
        label: 'Höhe inkl. Verpackung',
        value: '0.105',
        displayValue: '10.5',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      length: {
        type: 6,
        id: '816b9d57-58b4-41ca-a917-98f99bf94198',
        code: 'length',
        frontend: '1',
        label: 'Länge/Tiefe inkl. Verpackung',
        value: '0.21',
        displayValue: '21',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      material: {
        type: 1,
        id: '7f89dd97-df8e-473b-9551-8f6390133b15',
        code: 'material',
        frontend: '1',
        label: 'Material',
        value: 'Steinzeug',
        displayValue: null,
        measure: null
      },
      quantity: {
        type: 4,
        id: '61b068ad-323a-41f7-998d-28f54785e0cd',
        code: 'quantity',
        frontend: '1',
        label: 'Gesamtstückzahl',
        value: '6',
        displayValue: null,
        measure: null
      },
      variant_id: {
        type: 3,
        id: '8e35a552-1b41-4afa-93a3-cba6e0004be0',
        code: 'variant_id',
        frontend: '0',
        label: 'Varianten-ID',
        value: 'fb0018a9-48ad-47a9-b55a-59765b72904aFF',
        displayValue: null,
        measure: null
      },
      weight_net: {
        type: 6,
        id: 'd460ced6-95cc-4f24-b4d3-a04effbc9d1a',
        code: 'weight_net',
        frontend: '1',
        label: 'Produktgewicht',
        value: '0.624',
        displayValue: '0.624',
        measure: {
          displayUnit: 'kg',
          baseUnit: 'kg'
        }
      },
      brand_series: {
        type: 1,
        id: 'a4b68ac2-59c3-423d-b420-bcdfabca5fd7',
        code: 'brand_series',
        frontend: '1',
        label: 'Serie',
        value: 'Madleen',
        displayValue: null,
        measure: null
      },
      variant_type: {
        type: 3,
        id: '4f5dcc99-c404-41c6-be8a-0767802d2fc4',
        code: 'variant_type',
        frontend: '0',
        label: 'Variant type',
        value: 'color',
        displayValue: null,
        measure: null
      },
      weight_gross: {
        type: 6,
        id: '28cc8aa7-7688-40f3-9988-2352a893883e',
        code: 'weight_gross',
        frontend: '1',
        label: 'Gewicht inkl. Verpackung',
        value: '3.08',
        displayValue: '3.08',
        measure: {
          displayUnit: 'kg',
          baseUnit: 'kg'
        }
      },
      height_product: {
        type: 6,
        id: 'a019fa71-9038-4942-832a-42e24919f8ea',
        code: 'height_product',
        frontend: '1',
        label: 'Höhe',
        value: '0.051',
        displayValue: '5.1',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      dishwasher_safe: {
        type: 2,
        id: 'c1de0a81-4576-4885-8aec-6c1d87688418',
        code: 'dishwasher_safe',
        frontend: '1',
        label: 'Spülmaschinenfest',
        value: '1',
        displayValue: null,
        measure: null
      },
      product_diameter: {
        type: 6,
        id: '9b6bfecc-0d9d-42c1-adfd-c8d6232b0687',
        code: 'product_diameter',
        frontend: '1',
        label: 'Durchmesser',
        value: '0.21',
        displayValue: '21',
        measure: {
          displayUnit: 'cm',
          baseUnit: 'm'
        }
      },
      count_of_packages: {
        type: 4,
        id: '9cfb558e-0e0b-40c0-87e8-8bbb295be66d',
        code: 'count_of_packages',
        frontend: '0',
        label: 'Anzahl der Verpackungen',
        value: '1',
        displayValue: null,
        measure: null
      },
      internal_metro_identifier: {
        type: 3,
        id: '6c04bc4a-8696-4bed-8a9b-0185bfefffe6',
        code: 'internal_metro_identifier',
        frontend: '0',
        label: 'Internal Metro Identifier',
        value: '483661',
        displayValue: null,
        measure: null
      },
      count_of_pieces_per_package: {
        type: 4,
        id: '95a5e9ac-5e62-4fdb-8c13-73cf7441b7ec',
        code: 'count_of_pieces_per_package',
        frontend: '0',
        label: 'Stückzahl per Verpackung',
        value: '6',
        displayValue: null,
        measure: null
      },
      custom_item_attributes_in_total: {
        type: 4,
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b74',
        code: 'custom_item_attributes_in_total',
        frontend: '0',
        label: 'custom_item_attributes_in_total',
        value: '30',
        displayValue: null,
        measure: null
      },
      int_rbg: {
        id: '7a14b004-c0e5-424b-b62b-ed536bb3739a',
        code: 'int_rbg',
        label: 'INT_rbg',
        value: '1',
        displayValue: null,
        frontend: '0',
        measure: null
      }
    },
    images: [
      {
        id: '51204af0-f338-402e-9354-3e5b4994b510',
        url: 'https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510',
        sequenceNumber: 0,
        main: true,
        sizes: [
          {
            id: 'item_image/34ef44b4-74cf-4e26-8359-cd96a2c89a8a',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510?h=80&ixlib=php-2.3.0&q=100&w=80',
            width: 80,
            height: 80
          },
          {
            id: 'item_image/8de7e856-e214-452b-b876-886f3661b009',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510?h=148&ixlib=php-2.3.0&q=100&w=148',
            width: 148,
            height: 148
          },
          {
            id: 'item_image/ecc5b608-9baa-4107-baa8-00e748dc8be2',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510?h=370&ixlib=php-2.3.0&q=100&w=370',
            width: 370,
            height: 370
          }
        ],
        visibility: null,
        mimeType: null
      },
      {
        id: '13c4f377-f0c2-46a8-977f-dd8a1adb05f7',
        url: 'https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7',
        sequenceNumber: 1,
        main: false,
        sizes: [
          {
            id: 'item_image/1e9ab118-02d5-4b5d-bc37-d068240950da',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7?h=80&ixlib=php-2.3.0&q=100&w=80',
            width: 80,
            height: 80
          },
          {
            id: 'item_image/35eb2a87-28ea-4ea1-87e0-8a9af81d9862',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7?h=148&ixlib=php-2.3.0&q=100&w=148',
            width: 148,
            height: 148
          },
          {
            id: 'item_image/afea03c9-199b-4b62-b6ed-76a54fb7d946',
            url: 'https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7?h=370&ixlib=php-2.3.0&q=100&w=370',
            width: 370,
            height: 370
          }
        ],
        visibility: null,
        mimeType: null
      }
    ],
    commercialUse: false,
    gtin: '4337182187783',
    mpn: '483661',
    name: 'METRO Professional Teller tief Madleen, Steinzeug, Ø 21 cm, grau, 6 Stück',
    description: null,
    descriptionLong:
      'Entdecken Sie unseren METRO Professional Madleen tiefen Teller aus Steinzeug, eine erstklassige Wahl für Ihre gastronomischen Bedürfnisse. Das Produkt vereint anspruchsvolles Design mit funktionalen Merkmalen und ist speziell für den professionellen Einsatz konzipiert. Mit seinem grauen Farbton bringt er eine attraktive Note auf Ihren Tisch. Der dunkle Rand verleiht diesem Teller eine rustikale Eleganz. Dank seiner Stapelbarkeit lässt er sich platzsparend aufbewahren, und seinen Durchmesser von 21 cm bietet er ausreichend Platz für Ihre köstlichen Mahlzeiten. Die Spülmaschinenfestigkeit sorgt für eine unkomplizierte Reinigung. Jede Verpackungseinheit besteht aus 6 Stück.',
    keyFeatures: [
      'Hergestellt aus hochwertigem Steinzeug',
      'Mikrowellen- und Spülmaschinengeeignet',
      'Stapelbar',
      'Sehr buntes und trendiges sortiment'
    ],
    artificialHighlights: [
      {
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Farbe',
        value: 'silber',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      },
      {
        id: '7f89dd97-df8e-473b-9551-8f6390133b15',
        code: 'material',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Material',
        value: 'Polyester',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      },
      {
        id: 'c9b06ca1-66b6-4ef8-b311-2ff1545b5225',
        code: 'product_package_dimensions_aggregate',
        type: 3,
        aggregated: true,
        translatable: true,
        label:
          'CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PACKAGE_DIMENSIONS.LABEL',
        value: '%height% x %width% x %length%',
        frontend: '10',
        displayUnit: null,
        substitutes: {
          width: {
            id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
            code: 'width',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Breite inkl. Verpackung',
            value: '10',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          },
          height: {
            id: '738a2b96-e334-4938-a12f-372b8212ccb3',
            code: 'height',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Höhe inkl. Verpackung',
            value: '30',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          },
          length: {
            id: '816b9d57-58b4-41ca-a917-98f99bf94198',
            code: 'length',
            type: 6,
            aggregated: false,
            translatable: false,
            label: 'Länge/Tiefe inkl. Verpackung',
            value: '10',
            frontend: '1',
            displayUnit: 'cm',
            substitutes: null
          }
        }
      },
      {
        id: '28cc8aa7-7688-40f3-9988-2352a893883e',
        code: 'weight_gross',
        type: 6,
        aggregated: false,
        translatable: false,
        label: 'Gewicht inkl. Verpackung',
        value: '2.4',
        frontend: '1',
        displayUnit: 'kg',
        substitutes: null
      },
      {
        id: '466c3e55-7193-4a96-9c7f-f579e9787640',
        code: 'country_of_manufacture',
        type: 1,
        aggregated: false,
        translatable: false,
        label: 'Herstellungsland',
        value: 'Deutschland',
        frontend: '1',
        displayUnit: null,
        substitutes: null
      }
    ],
    brand: {
      id: 'f83d5d3b-42be-4840-a058-df9aa73ba8e6',
      name: 'METRO Professional',
      slug: 'metro-professional'
    },
    manufacturer: {
      id: '8dbdd627-ae63-4dc8-8460-7c5420325b56',
      name: 'MCC Trading International GmbH'
    },
    hasVariants: true
  }
}

export const FridgeProductMockV2: ItemMarket = {
  status: 1,
  pdpAvailable: true,
  tags: [
    'jubi-deals-elektrogerate',
    'produktwelt-von-pkm',
    'kuhlgerate',
    'reference-price-promo',
    'mm'
  ],
  offers: {
    'b7971ad9-9251-4444-86bd-ebedd6d28a1c': {
      businessModel: 1,
      id: 'b7971ad9-9251-4444-86bd-ebedd6d28a1c',
      quantity: 52,
      freightForwarding: true,
      originRegionInfo: null,
      destinationRegionInfo: {
        region: 'DE_MAIN',
        price: {
          net: '419.99',
          gross: '499.79',
          vatRate: '19.00',
          currency: 'EUR'
        },
        referencePrice: {
          net: '1175.6',
          gross: '1398.96',
          vatRate: '19.00',
          currency: 'EUR',
          discountPercentage: '64'
        },
        basePrice: null,
        unitPrice: null,
        volumePrices: []
      },
      organization: {
        id: '118ede85-fd10-42aa-8ee5-6fbc2553de02',
        shopName: 'METRO'
      },
      shippingTime: {
        from: '4',
        to: '6'
      },
      shippingGroup: {
        shippingCosts: {
          DE_MAIN: [
            {
              shippingType: 2,
              id: '905f3ed4-70e2-48cd-9816-40d078cd9b06',
              destinationRegion: 'DE_MAIN',
              unitCost: {
                net: '42.01',
                gross: '49.99',
                vatRate: '19.00',
                currency: 'EUR'
              },
              extraUnitCost: {
                net: '4.20',
                gross: '5.00',
                vatRate: '19.00',
                currency: 'EUR'
              },
              thresholdEnabled: false,
              thresholdAmount: null
            }
          ]
        },
        id: '042a3ac8-3b7f-42c5-8384-6fa205c2630b',
        name: ''
      },
      labels: {
        DE_MAIN: ['promotion', 'msrp_price']
      },
      offerServices: [],
      valueAddedServices: []
    }
  },
  bestOffer: {
    cheapest: {
      DE_MAIN: {
        b2b: ['b7971ad9-9251-4444-86bd-ebedd6d28a1c'],
        b2c: ['b7971ad9-9251-4444-86bd-ebedd6d28a1c']
      }
    }
  },
  breadcrumb: {
    id: '0d201a2e-2dbf-425f-9468-28566db6609a',
    name: 'Kühl-Gefrierkombinationen Haushalt',
    slug: 'kuhl-gefrierkombinationen',
    energyEfficiencyRange: null,
    parents: [
      {
        id: 'eb1de62d-9585-4412-98ed-a960aaee25ee',
        name: 'Haushaltskühlgeräte',
        slug: 'kuhlgerate',
        energyEfficiencyRange: null
      },
      {
        id: 'e45413de-3229-40b6-be52-2f77f97bcdc2',
        name: 'Kühltechnik',
        slug: 'kuhltechnik',
        energyEfficiencyRange: null
      }
    ]
  },
  item: {
    idItem: '57fbc8a3-bf54-4b16-97f3-4927ec46032e',
    mid: 'AAA0046021500',
    idCategory: '0d201a2e-2dbf-425f-9468-28566db6609a',
    attributes: {
      rrp: {
        type: 6,
        id: '9949e380-4722-463a-85ea-a448c4b95f57',
        code: 'rrp',
        frontend: '0',
        label: 'Unverbindlicher Verkauspreis',
        value: '1175.6'
      },
      color: {
        type: 1,
        id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
        code: 'color',
        frontend: '1',
        label: 'Farbe',
        value: 'schwarz'
      },
      light: {
        type: 2,
        id: '18e72099-5f6b-4658-be3e-0a707eab826a',
        code: 'light',
        frontend: '1',
        label: 'Licht',
        value: '1'
      },
      width: {
        type: 6,
        id: '4ababbb0-a8f3-4a50-b862-dbd2b53b7d85',
        code: 'width',
        frontend: '1',
        label: 'Breite inkl. Verpackung',
        value: '0.91'
      },
      height: {
        type: 6,
        id: '738a2b96-e334-4938-a12f-372b8212ccb3',
        code: 'height',
        frontend: '1',
        label: 'Höhe inkl. Verpackung',
        value: '1.87'
      },
      length: {
        type: 6,
        id: '816b9d57-58b4-41ca-a917-98f99bf94198',
        code: 'length',
        frontend: '1',
        label: 'Länge/Tiefe inkl. Verpackung',
        value: '0.69'
      },
      control: {
        type: 1,
        id: '8d249c21-5b22-476a-a1ac-9f7c2df86d78',
        code: 'control',
        frontend: '1',
        label: 'Steuerung',
        value: 'manuell'
      },
      intweee: {
        type: 2,
        id: '0f964bb7-0e11-4d92-b5bf-1cf6586683ce',
        code: 'intweee',
        frontend: '0',
        label: 'INTweee',
        value: '1'
      },
      quantity: {
        type: 4,
        id: '61b068ad-323a-41f7-998d-28f54785e0cd',
        code: 'quantity',
        frontend: '1',
        label: 'Gesamtstückzahl',
        value: '1'
      },
      bulb_type: {
        type: 1,
        id: 'bdee4033-5234-4662-8126-6108e0396538',
        code: 'bulb_type',
        frontend: '1',
        label: 'Glühbirnentyp',
        value: 'LED'
      },
      door_hinge: {
        type: 1,
        id: '97959e6c-ba5d-4039-81cb-7ec2690573ce',
        code: 'door_hinge',
        frontend: '1',
        label: 'Türanschlag',
        value: 'rechts wechselbar'
      },
      weight_net: {
        type: 6,
        id: 'd460ced6-95cc-4f24-b4d3-a04effbc9d1a',
        code: 'weight_net',
        frontend: '1',
        label: 'Produktgewicht',
        value: '71'
      },
      ambient_temp: {
        type: 6,
        id: '9862efc1-dfac-41e2-8db2-277c99d288b4',
        code: 'ambient_temp',
        frontend: '1',
        label: 'Umgebungstemperatur',
        value: '38'
      },
      cooling_type: {
        type: 1,
        id: 'a2081a63-b5b3-443f-beb2-d721bee8c591',
        code: 'cooling_type',
        frontend: '1',
        label: 'Kühlart',
        value: 'Kompressorkühlung'
      },
      eprel_source: {
        type: 3,
        id: '720afd68-b11c-4ec6-bb5c-e57e08126841',
        code: 'eprel_source',
        frontend: '0',
        label: 'INTepso',
        value: 'multi-extractor'
      },
      weight_gross: {
        type: 6,
        id: '28cc8aa7-7688-40f3-9988-2352a893883e',
        code: 'weight_gross',
        frontend: '1',
        label: 'Gewicht inkl. Verpackung',
        value: '79'
      },
      aggregated_wl: {
        type: 3,
        id: '8a38b0ad-f78d-492d-a4fc-4a0597a655dd',
        code: 'aggregated_wl',
        frontend: '0',
        label: 'Abmessungen (B x T)',
        value: '83 x 61 cm'
      },
      voltage_range: {
        type: 3,
        id: '2bfd2bff-76bf-4553-bd2a-cf5209100672',
        code: 'voltage_range',
        frontend: '1',
        label: 'Spannung (Bereich)',
        value: '220 - 240 V'
      },
      width_product: {
        type: 6,
        id: 'e625f6d2-4482-4406-a2f7-854dfe6b836d',
        code: 'width_product',
        frontend: '1',
        label: 'Breite',
        value: '0.83'
      },
      aggregated_hwl: {
        type: 3,
        id: '98127c75-b81d-4879-8daa-a594c5b36d77',
        code: 'aggregated_hwl',
        frontend: '0',
        label: 'Abmessungen (H x B x T)',
        value: '178.5 x 83 x 61 cm'
      },
      climatic_class: {
        type: 1,
        id: 'e87ec51d-187a-4559-92fe-30514d9ef0f0',
        code: 'climatic_class',
        frontend: '1',
        label: 'Klimaklasse',
        value: 'SN-ST'
      },
      height_product: {
        type: 6,
        id: 'a019fa71-9038-4942-832a-42e24919f8ea',
        code: 'height_product',
        frontend: '1',
        label: 'Höhe',
        value: '1.785'
      },
      number_drawers: {
        type: 4,
        id: '7e2cb66e-26ba-42d4-a6c1-824ec5313056',
        code: 'number_drawers',
        frontend: '1',
        label: 'Anzahl der Schubladen',
        value: '12'
      },
      door_open_alarm: {
        type: 2,
        id: 'bee8e0f0-9b4a-42b3-a702-1ba9462aa19e',
        code: 'door_open_alarm',
        frontend: '1',
        label: 'Alarm bei offener Tür',
        value: '1'
      },
      eu_energy_label: {
        type: 5,
        id: '7075b05b-845d-4042-bd34-b54229338e81',
        code: 'eu_energy_label',
        frontend: '3',
        label: 'EU Energielabel',
        value:
          'https://storage.googleapis.com/service-file-documents-public-bucket-prod/attribute_file/0574ee65-bc43-450a-a7ca-a9aa2380e9e8'
      },
      number_of_doors: {
        type: 4,
        id: 'b400dc10-9f9c-4deb-8217-6c579999b5e8',
        code: 'number_of_doors',
        frontend: '1',
        label: 'Anzahl Türen',
        value: '2'
      },
      freezer_position: {
        type: 1,
        id: 'beaa05ab-8a43-4bea-90f4-a48e2b6fd0b6',
        code: 'freezer_position',
        frontend: '1',
        label: 'Position Gefrierfach',
        value: 'unten'
      },
      led_illumination: {
        type: 2,
        id: 'e00a3172-0f17-4ab5-a7da-822df996a9b2',
        code: 'led_illumination',
        frontend: '1',
        label: 'LED Beleuchtung',
        value: '1'
      },
      noise_level_class: {
        type: 1,
        id: '93155d8f-235d-4474-8cef-5124895f78fd',
        code: 'noise_level_class',
        frontend: '1',
        label: 'Geräuschemissionsklasse',
        value: 'C'
      },
      energy_consumption: {
        type: 6,
        id: '600a121f-201a-47bf-a5fb-e5beb0a08cbc',
        code: 'energy_consumption',
        frontend: '1',
        label: 'Energieverbrauch',
        value: '0.79'
      },
      freezer_star_rating: {
        type: 1,
        id: '5c62fd7d-7e8b-4bda-9124-8cd11756dea6',
        code: 'freezer_star_rating',
        frontend: '1',
        label: 'Gefrierfach (Sterne)',
        value: '****'
      },
      temperature_display: {
        type: 2,
        id: '262b5abe-9bde-4a71-813b-13d05ab0c21a',
        code: 'temperature_display',
        frontend: '1',
        label: 'Temperaturanzeige',
        value: '1'
      },
      length_depth_product: {
        type: 6,
        id: 'b4547557-2f3d-448d-92a8-f8395a8f4e6a',
        code: 'length_depth_product',
        frontend: '1',
        label: 'Länge/Tiefe',
        value: '0.61'
      },
      eu_product_data_sheet: {
        type: 5,
        id: 'caf45c82-0863-42b2-88ba-f9a5bb26ad5f',
        code: 'eu_product_data_sheet',
        frontend: '2',
        label: 'EU Produktdatenblatt',
        value:
          'https://storage.googleapis.com/service-file-documents-public-bucket-prod/attribute_file/db6b9cef-500d-45d9-a73a-381f1d735d35'
      },
      fridge_interior_light: {
        type: 2,
        id: '3d716410-c7ac-4b7a-a36b-be96ba1e2b09',
        code: 'fridge_interior_light',
        frontend: '1',
        label: 'Innenbeleuchtung Kühlschrank',
        value: '1'
      },
      inttakebackobligation: {
        type: 2,
        id: '0325fd83-e2bc-4d24-88b4-4a1acffba172',
        code: 'inttakebackobligation',
        frontend: '0',
        label: 'INTtakebackobligation',
        value: '0'
      },
      noise_level_elec_appl: {
        type: 4,
        id: '34549397-94c7-4dbb-86aa-f0353064b836',
        code: 'noise_level_elec_appl',
        frontend: '1',
        label: 'Betriebsgeräusch',
        value: '40'
      },
      country_of_manufacture: {
        type: 1,
        id: '466c3e55-7193-4a96-9c7f-f579e9787640',
        code: 'country_of_manufacture',
        frontend: '1',
        label: 'Herstellungsland',
        value: 'China'
      },
      energy_efficiency_class: {
        type: 1,
        id: '3ae68be1-fafa-4e94-96ce-62832fd6ffc7',
        code: 'energy_efficiency_class',
        frontend: '1',
        label: 'Energieeffizienzklasse',
        value: 'E'
      },
      energy_efficiency_color: {
        type: 3,
        id: '73e1a042-dbd0-4211-ae1b-41a8dccf7b02',
        code: 'energy_efficiency_color',
        frontend: '0',
        label: 'INTegefco',
        value: 'FCB919'
      },
      spare_part_availability: {
        type: 3,
        id: '0d7bd895-0164-4f27-943c-d62ce6eb3aa7',
        code: 'spare_part_availability',
        frontend: '1',
        label: 'Verfügbarkeit von Ersatzteilen',
        value: ''
      },
      eprel_registration_number: {
        type: 4,
        id: '60adaf00-5d9d-4d22-a202-09fbbb1a8e10',
        code: 'eprel_registration_number',
        frontend: '0',
        label: 'EPREL-Registrierungsnummer',
        value: '1868336'
      },
      energy_efficiency_color_map: {
        type: 3,
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b72',
        code: 'energy_efficiency_color_map',
        frontend: '0',
        label: 'energy_efficiency_color_map_label',
        value:
          '{"A":"09A64F","B":"52B847","C":"BED732","D":"FCEE23","E":"FCB919","F":"F37123","G":"ED1B24","max":"A","min":"G","version":2}'
      },
      energy_efficiency_label_config: {
        type: 3,
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b75',
        code: 'energy_efficiency_label_config',
        frontend: '0',
        label: 'energy_efficiency_label_config_label',
        value:
          '{"range":{"max":"A","min":"G"},"values":["A","B","C","D","E","F","G"],"version":2,"attributes":["energy_efficiency_class"],"defaultAttribute":"energy_efficiency_class"}'
      },
      custom_item_attributes_in_total: {
        type: 4,
        id: '03019001-e2f3-463e-be13-4b2bb8ca4b74',
        code: 'custom_item_attributes_in_total',
        frontend: '0',
        label: 'custom_item_attributes_in_total',
        value: '129'
      }
    },
    images: [
      {
        id: '803f7059-019a-4f8a-b6ef-ac07345b0537',
        url: 'https://prod-metro-markets.imgix.net/item_image/803f7059-019a-4f8a-b6ef-ac07345b0537?auto=format,compress',
        sizes: [
          {
            id: 'item_image/53213b37-04a3-48f6-90d4-b2d262dd7004',
            url: 'https://prod-metro-markets.imgix.net/item_image/803f7059-019a-4f8a-b6ef-ac07345b0537?h=80&ixlib=php-2.3.0&q=100&w=80&auto=format,compress',
            width: 80,
            height: 80
          },
          {
            id: 'item_image/8c2ddae3-a710-476d-827e-50f4f4590e24',
            url: 'https://prod-metro-markets.imgix.net/item_image/803f7059-019a-4f8a-b6ef-ac07345b0537?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress',
            width: 148,
            height: 148
          },
          {
            id: 'item_image/0ee2802a-6212-4526-a4b9-217c5a3e0f2d',
            url: 'https://prod-metro-markets.imgix.net/item_image/803f7059-019a-4f8a-b6ef-ac07345b0537?h=370&ixlib=php-2.3.0&q=100&w=370&auto=format,compress',
            width: 370,
            height: 370
          }
        ]
      },
      {
        id: 'b91d887f-9afe-4aa0-875c-1a6fe2d7692e',
        url: 'https://prod-metro-markets.imgix.net/item_image/b91d887f-9afe-4aa0-875c-1a6fe2d7692e?auto=format,compress',
        sizes: [
          {
            id: 'item_image/f21a0ca6-936f-4f95-94d7-02f3a40ce1c8',
            url: 'https://prod-metro-markets.imgix.net/item_image/b91d887f-9afe-4aa0-875c-1a6fe2d7692e?h=80&ixlib=php-2.3.0&q=100&w=80&auto=format,compress',
            width: 80,
            height: 80
          },
          {
            id: 'item_image/c4278050-d961-4f72-9839-017452f244bc',
            url: 'https://prod-metro-markets.imgix.net/item_image/b91d887f-9afe-4aa0-875c-1a6fe2d7692e?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress',
            width: 148,
            height: 148
          },
          {
            id: 'item_image/7322c9e7-7acb-4779-9e94-84a4b743a177',
            url: 'https://prod-metro-markets.imgix.net/item_image/b91d887f-9afe-4aa0-875c-1a6fe2d7692e?h=370&ixlib=php-2.3.0&q=100&w=370&auto=format,compress',
            width: 370,
            height: 370
          }
        ]
      }
    ],
    gtin: '4030608250091',
    mpn: '25009',
    name: 'PKM Side by Side Kühl-Gefrierkombination SBS480NFWDBJ',
    descriptionLong:
      'Entdecken Sie die PKM Side by Side Kühl-Gefrierkombination SBS480NFWDBJ und sagen Sie adieu zum manuellen Abtauen dank der innovativen NoFrost-Technologie. Mit der Energieeffizienzklasse E und der effizienten LED-Beleuchtung sparen Sie nicht nur Energie, sondern haben auch stets den perfekten Überblick über Ihre Lebensmittel. Der Schnellgefrieren-Modus sorgt dafür, dass frische Lebensmittel schnell eingefroren werden, während der Wasserspender mit einem großzügigen 2,5 Liter Tank für erfrischende Getränke jederzeit zur Verfügung steht. Der Schnellkühlen-Modus ermöglicht es Ihnen, frische Lebensmittel besonders schnell auf die gewünschte Temperatur zu bringen. Mit dem Display & Touch Control haben Sie die volle Kontrolle über die Einstellungen Ihrer Kühl-Gefrierkombination. Der Gesamtnutzinhalt von 444 Litern bietet ausreichend Platz für all Ihre Lebensmittel, davon 276 Liter im Kühlfach und 168 Liter im Gefrierfach. Erleben Sie Komfort und Effizienz in Ihrer Küche mit der PKM Side by Side Kühl-Gefrierkombination SBS480NFWDBJ - für ein modernes und praktisches Kühl-Erlebnis.',
    keyFeatures: [
      'Energieeffizienzklasse E',
      'LED-Beleuchtung',
      'Schnellgefrieren-Modus',
      'Smart Modus',
      'Wasserspender'
    ],
    brand: {
      id: 'f49c80d3-af7b-43df-b120-e397190a81bc',
      name: 'PKM',
      slug: 'pkm',
      logo: null,
      contact: {
        name: 'PKM GmbH & Co.KG',
        email: '<EMAIL>',
        phone: '0049 (0) 2841-178738',
        address: 'Neuer Wall 2-6, 47441, Moers, DE'
      }
    },
    hasVariants: false,
    artificialHighlights: null,
    videos: null
  },
  promotion: true
}
