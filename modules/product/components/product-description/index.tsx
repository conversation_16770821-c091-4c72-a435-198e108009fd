import { RefObject } from 'react'


import { ProductGeneralInfo } from '@modules/product/components/product-description/product-general-info'
import { FOOD_CATEGORY } from '@modules/product/constants'
import { ItemMarket } from '@modules/product/types/spi'
import { checkForAlcoholContent } from '@modules/product/utils'
import { clsx } from '@modules/shared/utils'

import { ProductAlcoholWarning } from '../product-alcohol-warning'
import { ProductDetailsTable } from './product-details-table'

interface Props {
  product: ItemMarket
  showHighlightsInDescription: boolean
  descriptionRef: RefObject<HTMLInputElement>
}

export const ProductDescriptionComponent = ({
  product,
  showHighlightsInDescription,
  descriptionRef
}: Props) => {
  const isFoodCategory =
    product.breadcrumb?.parents?.slice(-1)[0].id === FOOD_CATEGORY
  const hasAlcoholContent = checkForAlcoholContent(product?.item?.attributes)
  
  return (
    <div
      test-target="product-additional-information"
      className="flex flex-col xs-md:m-0"
    >
      {hasAlcoholContent && (
        <div className="bg-white-main p-6 mt-6 block xl:hidden 2xl:hidden">
          <ProductAlcoholWarning />
        </div>
      )}

      <div
        className={clsx(
          'flex',
          isFoodCategory ? 'flex-col-reverse' : 'flex-col'
        )}
      >
        <ProductDetailsTable product={product} />
        <ProductGeneralInfo
          descriptionRef={descriptionRef}
          productData={product}
          showHighlightsInDescription={showHighlightsInDescription}
        />
      </div>
    </div>
  )
}
