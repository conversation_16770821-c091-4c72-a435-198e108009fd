import axios from 'axios'
import clsx from 'clsx'
import parse from 'html-react-parser'
import { useTranslation } from 'next-i18next'
import { useDispatch, useSelector } from 'react-redux'
import sanitizeHtml from 'sanitize-html'

import { NUMBER_5 } from '@core/constants/numbers'
import { useGtag, useMarket } from '@core/hooks'
import { PopUPModalProvider, usePopUpModal } from '@core/hooks/usePopUpModal'
import { toggleGeneralInfo } from '@core/redux/features/product/productSlice'
import { RootState } from '@core/redux/store'

import { PopUpModal } from '@shared/components'

import { ProductHighlights } from '@modules/product/components/product-highlights'
import { PDP } from '@modules/product/constants'
import { getHazardLabelImage } from '@modules/product/constants/api.constants'
import { Attribute, Item, ItemMarket } from '@modules/product/types/spi'
import { getDescriptionInteractionPayload } from '@modules/product/utils'
import { Accordion } from '@modules/shared/components/Accordion'
import { SidebarV2 } from '@modules/shared/components/SidebarV2'
import { SIDEBAR } from '@modules/shared/components/SidebarV2/Sidebar.constant'

import { LoadbeeLink } from '../../product-rich-media-contents/loadbee/loadbee-link'
import { ProductDescriptionSidebar } from './ProductDescriptionSidebar'

type Props = {
  description: string
  haveFileTypes: Attribute[]
  hazardLabels: Attribute[]
  item: Item
  product: ItemMarket
  showHighlightsInDescription: boolean
  isProductHasLoadbee: boolean
  productSafetyDocument?: Attribute
  productSafetyInstructions?: Attribute
}

export const ProductGeneralInfoUI = ({
  description,
  haveFileTypes,
  hazardLabels,
  item,
  product,
  showHighlightsInDescription,
  isProductHasLoadbee,
  productSafetyDocument,
  productSafetyInstructions
}: Props): JSX.Element => {
  const { t } = useTranslation([PDP])
  const { gtagEvent } = useGtag()
  const market = useMarket()
  const isBrandContactPresent = item?.brand?.contact

  const sanitizeNbsp = (description: string) => {
    const regexp = /&nbsp;/g
    const occurrences = description?.match(regexp)

    if (occurrences?.length > NUMBER_5) {
      return description.replace(regexp, ' ')
    }

    return description
  }

  const dispatch = useDispatch()
  const generalInfoAccordion = useSelector(
    (state: RootState) => state.product.generalInfoAccordion
  )

  const onToggle = () => {
    dispatch(toggleGeneralInfo())
    gtagEvent(getDescriptionInteractionPayload())
  }

  return (
    <div data-testid="productDescription">
      <Accordion
        title={t(
          'CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DESCRIPTION.TITLE'
        )}
        containerClassName={clsx(
          'mt-6 mb-0 px-5 xs-md:px-4',
          generalInfoAccordion && 'pb-6'
        )}
        titleClassName="font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 text-blue-shade-60 prose antialiased"
        isOpen={generalInfoAccordion}
        onToggle={onToggle}
      >
        <div
          test-target="product-description-container"
          data-testid="product-description-layout"
          className="pt-5 break-words"
        >
          {isProductHasLoadbee && (
            <PopUPModalProvider>
              <LoadbeeLink brandName={item.brand?.name} gtin={item.gtin} />
            </PopUPModalProvider>
          )}
          <div>
            {showHighlightsInDescription  && (
              <div className="hidden md:block">
                <ProductHighlights
                  product={product}
                  market={market}
                  displayTitle={false}
                  displayBrandInfo={false}
                  displayShowMoreInfo={false}
                />
              </div>
            )}
            <span data-testid="productDetails" className="text-blue-shade-60">
              {parse(sanitizeHtml(sanitizeNbsp(description)))}
            </span>
          </div>
          {/*Documents*/}
          <div>
            {(haveFileTypes.length > 0 ||
              isBrandContactPresent ||
              productSafetyDocument ||
              productSafetyInstructions) && (
              <div data-testid="file-types-section" className="group">
                <h4 className="text-primary-main font-extrabold">
                  {t('CATALOG.DETAILS_PAGE.DOCUMENTS.TITLE')}
                </h4>
                <ul className="items-center text-secondary-main pl-26px">
                  {/** Contact person */}
                  <SidebarV2
                    id={SIDEBAR.SAFTEY_BRAND_INFORMATION}
                    trigger={
                      <div>
                        {(isBrandContactPresent ||
                          productSafetyDocument ||
                          productSafetyInstructions) && (
                          <li className="cursor-pointer marker:text-black m-0">
                            <span className="text-base text-secondary-main hover:underline">
                              {t(
                                'CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.PRODUCT_SAFETY_TITLE'
                              )}
                            </span>
                          </li>
                        )}
                      </div>
                    }
                  >
                    <ProductDescriptionSidebar
                      productSafetyDocument={productSafetyDocument}
                      productSafetyInstructions={productSafetyInstructions}
                      brandContact={item?.brand?.contact}
                    />
                  </SidebarV2>
                  {/** Files */}
                  {haveFileTypes?.map((data) => (
                    <div data-testid="file-types-links" key={data?.label}>
                      <PopUPModalProvider>
                        <DocumentLink item={data} />
                        <PopUpModal />
                      </PopUPModalProvider>
                    </div>
                  ))}
                </ul>
              </div>
            )}
          </div>
          {/*Hazard labels*/}
          <div>
            {hazardLabels.length > 0 && (
              <div data-testid="hazard-types-section">
                <h4 className="text-primary-main font-extrabold">
                  {t(
                    'CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.PRODUCT_SAFETY.TITLE'
                  )}
                </h4>
                <div className="flex flex-row flex-wrap gap-6">
                  {hazardLabels.map((data, index) => (
                    <div
                      data-testid="hazard-types-images"
                      key={`hazard-image-${index}`}
                    >
                      <span className="flex justify-center">
                        <img
                          className="w-60px m-0"
                          src={getHazardLabelImage(data?.value)}
                          alt="hazard-labels"
                          width={60}
                          height={60}
                          draggable={false}
                        />
                      </span>
                      <p className="flex my-0 font-extrabold text-sm justify-center truncate hover:text-clip">
                        {t(`HAZARD_LABELS.${data.value}`)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </Accordion>
    </div>
  )
}

export const DocumentLink = (data): JSX.Element => {
  const WINDOW_OPEN_CONFIG = 'noopener,noreferrer'
  const contentType = 'application/pdf'
  const { setImageURL, setShowModal } = usePopUpModal()
  const handleDocument = async (url) => {
    const response = await axios.get(url)
    const isPDF = response?.headers['content-type'] === contentType
    if (isPDF) {
      window.open(url, '_blank', WINDOW_OPEN_CONFIG)
    } else {
      setImageURL(data.item.value)
      setShowModal(true)
    }
  }

  return (
    <li
      data-testid="product-document-link"
      key={data?.item?.id}
      onClick={(e) => handleDocument(data?.item?.value)}
      className="cursor-pointer group-has-[ul]:marker:text-black group-has-[ul]:list-disc m-0 list-none"
    >
      <span className="text-base text-secondary-main hover:underline">
        {' '}
        {data.children ?? data?.item?.label}
      </span>
    </li>
  )
}
