import { screen } from '@testing-library/react'

import '@core/hooks/__mocks__/intersectionObserver'
import { APIResponse } from '@core/services/http/types'
import { useFeatureFlag } from '@core/ssr/featureFlag/featureFlag.context'
import { renderWithProviders } from '@core/utils/testing'

import { ProductIndexMockV2 } from '@modules/product/mocks/product-index/product-index-mock'
import { ProductAttributeDetails } from '@modules/product/types'

import { ProductDescriptionComponent } from '../..'

jest.mock('@core/ssr/featureFlag/featureFlag.context', () => ({
  useFeatureFlag: jest.fn()
}))
const resizeObserverMock = () => ({
  observe: () => null,
  unobserve: () => null
})
window.ResizeObserver = jest.fn().mockImplementation(resizeObserverMock)

const mockedUseFeatureFlag = useFeatureFlag as jest.MockedFunction<
  typeof useFeatureFlag
>
describe('Product description', () => {
  const descriptionRef = { current: null }
  it('should return black deals banner', async () => {
    mockedUseFeatureFlag.mockReturnValue(true)
    const productData = ProductIndexMockV2
    renderWithProviders(
      <ProductDescriptionComponent
        product={productData}
        showHighlightsInDescription={true}
        descriptionRef={descriptionRef}
      />
    )
    expect(screen.getAllByTestId('productDescription')).toMatchSnapshot()
  })
})
