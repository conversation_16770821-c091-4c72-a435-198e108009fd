import { RefObject, useEffect, useState } from 'react'

import { NUMBER_200 } from '@core/constants/numbers'
import { useMarket } from '@core/redux/features/market'

import { ProductGeneralInfoUI } from '@modules/product/components/product-description/product-general-info/ProductGeneralInfoUI'
import {
  HazardCodes,
  LOADBEE_OK,
  attributeTypes
} from '@modules/product/constants'
import { ProductAttribute } from '@modules/product/types'
import { ItemMarket } from '@modules/product/types/spi'
import { checkLoadbeeAvailability } from '@modules/product/utils/loadbee.utils'

export interface ProductProps {
  productData: ItemMarket
  showHighlightsInDescription: boolean
  descriptionRef: RefObject<HTMLInputElement>
}

export const ProductGeneralInfo = ({
  productData,
  showHighlightsInDescription,
  descriptionRef
}: ProductProps): JSX.Element => {
  const [isProductHasLoadbee, setIsProductHasLoadbee] = useState(false)
  const market = useMarket()

  const { item } = productData

  useEffect(() => {
    if (item?.gtin) {
      checkLoadbeeAvailability(item?.gtin, market).then((response) => {
        if (
          (response as any)?.result?.code === NUMBER_200 ||
          (response as any)?.result?.status === LOADBEE_OK
        ) {
          setIsProductHasLoadbee(true)
        }
      })
    }
  }, [item?.gtin, market])

  const attributes = productData.item?.attributes || {}

  const productSafetyDocument = attributes?.['product_safety_document']
  const productSafetyInstructions = attributes?.['product_safety_instructions']

  const haveFileTypes = Object.values(attributes).filter((attribute) => {
    return (
      (attribute?.frontend === attributeTypes.DataSheet ||
        attribute?.type === attributeTypes.GuaranteeInformation) &&
      attribute.id !== productSafetyDocument?.id
    )
  })

  const hazardLabels = Object.values(attributes).filter((attr) =>
    Object.keys(HazardCodes).some((code) => code === attr?.value)
  )

  if (!item) return <></>

  return (
    <div ref={descriptionRef}>
      <ProductGeneralInfoUI
        description={item?.descriptionLong}
        haveFileTypes={haveFileTypes}
        hazardLabels={hazardLabels}
        product={productData}
        item={item}
        showHighlightsInDescription={showHighlightsInDescription}
        isProductHasLoadbee={isProductHasLoadbee}
        productSafetyDocument={productSafetyDocument}
        productSafetyInstructions={productSafetyInstructions}
      />
    </div>
  )
}
