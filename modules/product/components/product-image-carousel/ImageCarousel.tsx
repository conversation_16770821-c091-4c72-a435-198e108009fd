import clsx from 'clsx'
import dynamic from 'next/dynamic'
import { ComponentType, useCallback, useEffect, useRef, useState } from 'react'
import Swiper, { Navigation, Pagination } from 'swiper'
import 'swiper/swiper-bundle.min.css'
import 'swiper/swiper.min.css'

import { NUMBER_0, NUMBER_2, NUMBER_3, NUMBER_4 } from '@core/constants/numbers'
import { useMediaQuery } from '@core/hooks/useMediaQuery'
import { PopUPModalProvider } from '@core/hooks/usePopUpModal'

import { Image } from '@modules/product/types/spi'
import { SVGIcon } from '@modules/shared/components'
import { SVG_NAMES } from '@modules/shared/icons/constants'

import {
  LG_BREAKPOINT,
  MD_BREAKPOINT,
  SM_BREAKPOINT,
  XL_BREAKPOINT
} from '@styles/mediaQueriesBreakpoints'

import { ModalImageCarouselProps } from '../product-image-modal'
import { ImageCarouselUI } from './ImageCarousel/ImageCarouselUI'
import { ImageCarouselMobileUI } from './ImageCarouselMobile/ImageCarouselUI'

const ModalImageCarouselUI: ComponentType<ModalImageCarouselProps> = dynamic(
  () =>
    import('../product-image-modal').then(
      (component) => component.ModalImageCarouselUI
    )
)
interface Props {
  images: Image[]
  productName: string
  videos?: any[]
}
// eslint-disable-next-line react-hooks/rules-of-hooks
Swiper.use([Pagination, Navigation])

// TODO: Missing double click in the modal on resolutions smaller than xl desktop
export const ImageCarousel = ({ images, productName, videos }: Props) => {
  const isImagesEven = images.length % 2 === 0
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [swiper, setSwiper] = useState(null)
  const [isMobile, setIsMobile] = useState(false)
  const matchesSm = useMediaQuery(`(${SM_BREAKPOINT})`)
  const matchesMd = useMediaQuery(`(${MD_BREAKPOINT})`)
  const matchesLg = useMediaQuery(`(${LG_BREAKPOINT})`)
  const matchesDesktop = useMediaQuery(`(${XL_BREAKPOINT})`)
  const [activeIndex, setActiveIndex] = useState(0)
  const [swiperPosition, setSwiperPosition] = useState<string>('Beginning')
  const [currentIndexSlide, setCurrentIndexSlide] = useState<number>(0)

  const [isVideoActive, setIsVideoActive] = useState(false)

  let swiperRefDesktop = useRef(null)
  let swiperRefMobile = useRef(null)

  const videoIndex = images?.length || NUMBER_0

  const handleSlideChange = useCallback((swiper) => {
    const { isBeginning, isEnd } = swiper
    setSwiperPosition(isBeginning ? 'Beginning' : isEnd ? 'End' : 'InBetween')
  }, [])

  const handleClick = useCallback(
    (index) => {
      if (swiper) {
        swiper.slideTo(index)
      }
    },
    [swiper]
  )

  const handleClickOnArrowAndChangeSlide = (
    index: number,
    totalImages: number
  ) => {
    const nextIndex = calcNextIndex(index, totalImages)
    setCurrentIndexSlide(nextIndex)
    swiper?.slideTo(nextIndex, 600, false)
  }

  const calcNextIndex = (nextIndex: number, totalImages: number): number =>
    nextIndex < NUMBER_0
      ? NUMBER_0
      : nextIndex > totalImages - 1
        ? totalImages - 1
        : nextIndex

  const getStep = (): number =>
    !matchesDesktop
      ? isImagesEven
        ? NUMBER_2
        : NUMBER_3
      : isImagesEven
        ? NUMBER_4
        : NUMBER_3

  useEffect(() => {
    setIsMobile(matchesSm)
    if (matchesSm && swiper) {
      swiper.slideTo(activeIndex)
    }
  }, [activeIndex, matchesSm, swiper])

  useEffect(() => {
    if (swiperRefMobile.current && isMobile) {
      const swiper_ = new Swiper(swiperRefMobile.current, {
        slidesPerView: 1,
        on: {
          slideChange: (swiper) => setActiveIndex(swiper.activeIndex)
        }
      })
      setSwiper(swiper_)
    }
    if (swiperRefDesktop.current && !isMobile) {
      const swiper_ = new Swiper(swiperRefDesktop.current, {
        slidesPerView: 'auto',
        direction: 'vertical',
        allowTouchMove: matchesMd ? true : false,
        navigation: {
          nextEl: '.swiper-button-n',
          prevEl: '.swiper-button-p'
        },
        on: {
          slideChange: (swiper) => handleSlideChange(swiper)
        }
      })
      setSwiper(swiper_)
    }
  }, [isMobile, handleSlideChange, matchesLg])

  const bulletsArray = (!!images?.length || !!videos?.length) && [
    ...(images?.map((_, index) => (
      <button
        key={index}
        className={`swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 ${
          activeIndex === index
            ? 'swiper-pagination-bullet-active !bg-blue-main'
            : ''
        }`}
        onClick={() => handleClick(index)}
        aria-label={`Slide ${index + 1}`}
      />
    )) || []),
    ...(!!videos?.length
      ? [
          <button
            data-testid="video-bullet"
            key={'video'}
            className={clsx(
              'swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 !bg-transparent',
              activeIndex === videoIndex
                ? 'swiper-pagination-bullet-active'
                : ''
            )}
            onClick={() => {
              handleClick(videoIndex)
              setActiveIndex(videoIndex)
            }}
            aria-label="Slide video"
          >
            <SVGIcon
              name={SVG_NAMES.PLAY_ICON_NO_CONTAINER}
              width="6px"
              height="6px"
              fill={activeIndex === videoIndex ? '#0064fe' : 'black'}
            />
          </button>
        ]
      : [])
  ]

  return (
    <PopUPModalProvider>
      <ImageCarouselMobileUI
        swiperRef={swiperRefMobile}
        images={images}
        videos={videos}
        bullets={bulletsArray}
        setIsVideoActive={setIsVideoActive}
      />
      <ImageCarouselUI
        handleClickOnArrowAndChangeSlide={handleClickOnArrowAndChangeSlide}
        swiperPosition={swiperPosition}
        images={images}
        videos={videos}
        activeIndex={activeIndex}
        matchesDesktop={matchesDesktop}
        swiperRef={swiperRefDesktop}
        setActiveIndex={setActiveIndex}
        setIsVideoActive={setIsVideoActive}
        currentIndexSlide={currentIndexSlide}
        getStep={getStep}
        isModalOpen={isModalOpen}
        productName={productName}
      />
      <ModalImageCarouselUI
        productName={productName}
        images={images}
        videos={videos}
        activeIndex={activeIndex}
        isMobile={isMobile}
        matchesDesktop={matchesDesktop}
        setActiveIndex={setActiveIndex}
        setIsModalOpen={setIsModalOpen}
        isVideoActive={isVideoActive}
        setIsVideoActive={setIsVideoActive}
      />
    </PopUPModalProvider>
  )
}
