import { RefObject, useEffect, useState } from 'react'

import { NUMBER_200 } from '@core/constants/numbers'
import { useMarket } from '@core/redux/features/market'

import {
  HazardCodes,
  LOADBEE_OK,
  attributeTypes
} from '@modules/product/constants'
import { ItemMarket } from '@modules/product/types/spi'
import { checkForAlcoholContent } from '@modules/product/utils'
import { checkLoadbeeAvailability } from '@modules/product/utils/loadbee.utils'

import { ProductAlcoholWarning } from '../product-alcohol-warning'
import { CharacteristicsUI } from './CharacteristicsUI'
import { DescriptionUI } from './DescriptionUI'
import { DocumentsUI } from './DocumentsUI'

interface Props {
  product: ItemMarket
  characteristicsContainerRef: RefObject<HTMLDivElement>
}

export const Information = ({
  product,
  characteristicsContainerRef
}: Props) => {
  const market = useMarket()
  const { item } = product
  const [hasLoadbee, setHasLoadbee] = useState(false)

  useEffect(() => {
    if (!item?.gtin) return

    checkLoadbeeAvailability(item.gtin, market).then((res) => {
      const result = (res as any)?.result
      if (result?.code === NUMBER_200 || result?.status === LOADBEE_OK) {
        setHasLoadbee(true)
      }
    })
  }, [item?.gtin, market])

  const attributes = item?.attributes || {}

  const productSafetyDocument = attributes['product_safety_document']
  const productSafetyInstructions = attributes?.['product_safety_instructions']
  const isBrandContactPresent = item?.brand?.contact
  const fileAttributes = Object.values(attributes).filter(
    (attr) =>
      (attr?.frontend === attributeTypes.DataSheet ||
        attr?.type === attributeTypes.GuaranteeInformation) &&
      attr.id !== productSafetyDocument?.id
  )

  const hazardLabels = Object.values(attributes).filter((attr) =>
    Object.keys(HazardCodes).includes(attr?.value)
  )

  const hasAlcoholContent = checkForAlcoholContent(attributes)

  return (
    <div className="flex flex-col xs-md:m-0">
      {hasAlcoholContent && (
        <div className="bg-white-main p-6 mt-6 block xl:hidden 2xl:hidden">
          <ProductAlcoholWarning />
        </div>
      )}

      {/* Description + Characteristics */}
      <div className="bg-white-main mt-5">
        <DescriptionUI
          description={item?.descriptionLong}
          product={product}
          isProductHasLoadbee={hasLoadbee}
        />
        <CharacteristicsUI
          item={item}
          characteristicsContainerRef={characteristicsContainerRef}
        />
        {(fileAttributes.length > 0 ||
          hazardLabels.length > 0 ||
          isBrandContactPresent ||
          productSafetyDocument ||
          productSafetyInstructions) && (
          <DocumentsUI
            haveFileTypes={fileAttributes}
            hazardLabels={hazardLabels}
            item={item}
            productSafetyDocument={productSafetyDocument}
            productSafetyInstructions={productSafetyInstructions}
            isBrandContactPresent={isBrandContactPresent}
          />
        )}
      </div>
    </div>
  )
}
