import '@testing-library/jest-dom'
import { fireEvent, screen, waitFor } from '@testing-library/react'

import { renderWithProviders } from '@core/utils/testing'

import { productMockV2 } from '@modules/product/services/__test__/fixtures'

import { DescriptionUI } from '../DescriptionUI'

let resizeCallback: ResizeObserverCallback
describe('Description UI component', () => {
  beforeEach(() => {
    window.ResizeObserver = jest.fn().mockImplementation((cb) => {
      resizeCallback = cb
      return {
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn()
      }
    })
  })
  it('renders Description component - with out load bee', async () => {
    renderWithProviders(
      <DescriptionUI
        description={productMockV2.item?.descriptionLong}
        product={productMockV2}
        isProductHasLoadbee={false}
      />
    )
    expect(screen.queryByTestId('loadbee-content')).not.toBeInTheDocument()
  })
  it('renders Description component - with load bee', async () => {
    renderWithProviders(
      <DescriptionUI
        description={productMockV2.item?.descriptionLong}
        product={productMockV2}
        isProductHasLoadbee={true}
      />
    )
    expect(screen.getByTestId('loadbee-content')).toBeInTheDocument()
  })

  it('shows "Show More" button when content overflows', async () => {
    renderWithProviders(
      <DescriptionUI
        description={'Very long description content...'}
        product={productMockV2}
        isProductHasLoadbee={false}
      />
    )

    // Simulate overflowing content
    const descEl = screen.getByTestId('product-description')
    if (descEl) {
      Object.defineProperty(descEl, 'scrollHeight', {
        configurable: true,
        value: 151 // overflow > 150
      })
      const fakeResizeEntry = {
        target: descEl,
        contentRect: descEl.getBoundingClientRect(),
        borderBoxSize: [],
        contentBoxSize: [],
        devicePixelContentBoxSize: []
      } as ResizeObserverEntry

      resizeCallback([fakeResizeEntry], {} as ResizeObserver)
    }

    // Force re-check
    fireEvent.resize(window)

    // Await UI update
    await waitFor(() => {
      expect(screen.getByTestId('content-toggle')).toBeInTheDocument()
    })
  })

  it('Do not show "Show More" button when content is not overflowing', async () => {
    renderWithProviders(
      <DescriptionUI
        description={'Very long description content...'}
        product={productMockV2}
        isProductHasLoadbee={false}
      />
    )
    // Simulate overflowing content
    const descEl = screen.getByTestId('product-description')
    if (descEl) {
      Object.defineProperty(descEl, 'scrollHeight', {
        configurable: true,
        value: 129 // overflow > 150
      })
      const fakeResizeEntry = {
        target: descEl,
        contentRect: descEl.getBoundingClientRect(),
        borderBoxSize: [],
        contentBoxSize: [],
        devicePixelContentBoxSize: []
      } as ResizeObserverEntry

      resizeCallback([fakeResizeEntry], {} as ResizeObserver)
    }

    // Force re-check
    fireEvent.resize(window)

    // Await UI update
    await waitFor(() => {
      expect(screen.queryByTestId('content-toggle')).not.toBeInTheDocument()
    })
  })

  it('toggles to "Show Less" on button click', async () => {
    renderWithProviders(
      <DescriptionUI
        description={'Very long description content...'}
        product={productMockV2}
        isProductHasLoadbee={false}
      />
    )

    // Simulate overflowing content
    const descEl = screen.getByTestId('product-description')
    if (descEl) {
      Object.defineProperty(descEl, 'scrollHeight', {
        configurable: true,
        value: 151 // overflow > 150
      })
      const fakeResizeEntry = {
        target: descEl,
        contentRect: descEl.getBoundingClientRect(),
        borderBoxSize: [],
        contentBoxSize: [],
        devicePixelContentBoxSize: []
      } as ResizeObserverEntry

      resizeCallback([fakeResizeEntry], {} as ResizeObserver)
    }

    // Force re-check
    fireEvent.resize(window)

    // Wait for "Show More" button to appear
    await waitFor(() => {
      const button = screen.queryByTestId('content-toggle')
      expect(button).toHaveTextContent('SHARED.SHOW_MORE.LABEL')
    })

    // Simulate button click to toggle
    const button = screen.getByTestId('content-toggle')
    fireEvent.click(button)

    // Await button text change to "Show Less"
    await waitFor(() => {
      expect(button).toHaveTextContent('SHARED.SHOW_LESS.LABEL')
    })
    // Simulate button click to toggle (2 time)
    fireEvent.click(button)

    await waitFor(() => {
      expect(button).toHaveTextContent('SHARED.SHOW_MORE.LABEL')
    })
  })
})
