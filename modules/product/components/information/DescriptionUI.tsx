import parse from 'html-react-parser'
import { useTranslation } from 'next-i18next'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import sanitizeHtml from 'sanitize-html'

import { NUMBER_5 } from '@core/constants/numbers'
import { useMarket } from '@core/hooks'
import { PopUPModalProvider } from '@core/hooks/usePopUpModal'
import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'

import { PDP } from '@modules/product/constants'
import { ItemMarket } from '@modules/product/types/spi'
import { clsx } from '@modules/shared/utils'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS
} from '@modules/shared/utils/optimizely.utils'

import { ProductHighlights } from '../product-highlights'
import { LoadbeeLink } from '../product-rich-media-contents/loadbee/loadbee-link'

type Props = {
  description: string
  product: ItemMarket
  isProductHasLoadbee: boolean
}

export const DescriptionUI = ({
  description,
  product,
  isProductHasLoadbee
}: Props): JSX.Element => {
  const { t } = useTranslation([PDP])
  const descriptionRef = useRef<HTMLDivElement>(null)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const [expanded, setExpanded] = useState(false)

  const { item } = product

  const market = useMarket()

  useEffect(() => {
    const descEl = descriptionRef.current
    const checkOverflow = () => {
      if (descEl) {
        setIsOverflowing(descEl.scrollHeight > 150)
      }
    }

    const observer = new ResizeObserver(checkOverflow)
    if (descEl) {
      observer.observe(descEl)
      checkOverflow()
    }

    return () => observer.disconnect()
  }, [description])

  const sanitizeNbsp = (html: string) =>
    html.match(/&nbsp;/g)?.length > NUMBER_5
      ? html.replace(/&nbsp;/g, ' ')
      : html

  const hasImage = Boolean(item?.images?.[1]?.url)
  const showImage = hasImage && descriptionRef.current?.scrollHeight > 96

  const isFridge =
    product?.breadcrumb?.parents[product?.breadcrumb?.parents?.length - 1]
      ?.id === 'e45413de-3229-40b6-be52-2f77f97bcdc2'
  const fridgeHighlightsVariation = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )

  const isFridgeHighlightsNewVariation =
    isFridge && fridgeHighlightsVariation === EXPERIMENT_VARIATIONS.VARIANT_TWO

  if (!description) return <></>

  return (
    <div className="xs-mdMinus:px-4 py-4 lgXl:p-4 px-3 border-b border-b-gray-200 last:border-none">
      <div className="text-[20px] leading-6 text-primary-main font-bold pt-2 pb-4">
        {t('CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DESCRIPTION.TITLE')}
      </div>

      <div>
        <div
          data-testid="product-description"
          ref={descriptionRef}
          className={clsx(
            'flex gap-6 overflow-hidden transition-[max-height] duration-300',
            (!showImage || !isOverflowing) && 'pb-3',
            expanded ? 'max-h-[1000px]' : 'max-h-[150px]'
          )}
        >
          {/* Text Block */}
          <div
            className={clsx(
              'relative flex flex-col gap-3',
              showImage && 'w-3/4 xs-mdMinus:w-full'
            )}
          >
            <div className="text-blue-shade-60 leading-5">
              {isProductHasLoadbee && (
                <div data-testid="loadbee-content">
                  <PopUPModalProvider>
                    <LoadbeeLink
                      brandName={item.brand?.name}
                      gtin={item.gtin}
                    />
                  </PopUPModalProvider>
                </div>
              )}
              {/* Highlights Section (Mobile Only) */}
              {!isFridgeHighlightsNewVariation && (
                <div className="bg-white-main md:hidden">
                  <ProductHighlights
                    product={product}
                    market={market}
                    displayTitle={false}
                    displayShowMoreInfo={false}
                    highlightsColor="text-metro-blue-tint-20"
                  />
                </div>
              )}
              <div className="!prose !text-primary-main !text-[14px] text-ellipsis">
                {parse(sanitizeHtml(sanitizeNbsp(description)))}
              </div>
            </div>
          </div>

          {/* Image Block */}
          {showImage && (
            <div
              className={clsx(
                '!prose w-1/4 overflow-hidden transition-[max-height] duration-100 ease-in-out xs-mdMinus:hidden max-h-[150px]'
              )}
            >
              <img
                className="h-full w-full object-contain"
                alt="product image"
                src={item.images[1].url}
                width={205}
                height={180}
              />
            </div>
          )}
        </div>

        {/* Show More / Less */}
        {isOverflowing && (
          <button
            data-testid="content-toggle"
            onClick={() => setExpanded((prev) => !prev)}
            className="text-secondary-main cursor-pointer hover:underline text-base bg-transparent border-none p-0 text-left"
          >
            {expanded
              ? t('SHARED.SHOW_LESS.LABEL')
              : t('SHARED.SHOW_MORE.LABEL')}
          </button>
        )}
      </div>
    </div>
  )
}
