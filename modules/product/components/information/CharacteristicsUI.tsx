import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'

import { useMarket } from '@core/redux/features/market/useMarket'

import { OK, PDP, attributeTypes, valueTypes } from '@modules/product/constants'
import { getProductDetailsById } from '@modules/product/services/get-product-details'
import { Item } from '@modules/product/types/spi'
import {
  showAggregatedValue,
  switchAttributeType
} from '@modules/product/utils/aggregatedValues'
import { clsx } from '@modules/shared/utils'

import { ProductDetailsTableSkeleton } from '../product-description/product-details-table/skeleton'
import { useProduct } from '@core/redux/features/product/useProduct'
import { useDispatch } from 'react-redux'
import { toggleDetailsSection } from '@core/redux/features/product/productSlice'

type Props = {
  item: Item
  characteristicsContainerRef: any
}

const LabelValueRow = ({
  label,
  value
}: {
  label: string
  value: string | React.ReactNode
}) => (
  <div className="flex items-end py-3">
    <span className="text-grey-tint-40 text-[14px] leading-[20px] max-w-[150px] lg-lgXl:max-w-none">
      {label}
    </span>
    <span className=" flex-grow border-b border-dotted border-gray-400 mx-2 mb-1" />
    <span className="md-lg:w-[350px] lg-lgXl:w-[350px] w-[160px] text-[14px] leading-[21px] text-left font-bold">
      {value}
    </span>
  </div>
)

export const CharacteristicsUI = ({ item, characteristicsContainerRef }: Props): JSX.Element => {
  const { t } = useTranslation([PDP])
  const characteristicRef = useRef<HTMLDivElement | null>(null)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const [loading, setLoading] = useState(null)
  const [productCharacteristics, setproductCharacteristics] = useState([])

  const market = useMarket()
  const dispatch = useDispatch()

  const { detailsSection } = useProduct()

  useEffect(() => {
    if (loading === null) {
      setLoading(true)
      getProductDetailsById({ productId: item?.idItem, market }).then(
        (data) => {
          if (data.type === OK) {
            setproductCharacteristics(data?.result)
          }
          setLoading(false)
        }
      )
    }
  }, [])

  useEffect(() => {
    const charEl = characteristicRef.current
    const checkOverflow = () => {
      if (charEl) {
        setIsOverflowing(charEl.scrollHeight > 150)
      }
    }

    const observer = new ResizeObserver(checkOverflow)
    if (charEl) {
      observer.observe(charEl)
      checkOverflow()
    }

    return () => observer.disconnect()
  }, [productCharacteristics])

  const getAttributeLabel = (attribute) =>
    attribute.translatable ? t(attribute.label, { ns: PDP }) : attribute.label

  const getAttributeValue = (attribute) => {
    if (attribute.aggregated) {
      if (attribute.frontend === attributeTypes.StringAggregate) {
        return showAggregatedValue(
          t(attribute.value as string),
          attribute.substitutes
        )
      }

      return attribute.substitutes
        ? Object.keys(attribute.substitutes).map((key) =>
            switchAttributeType(
              attribute.substitutes[key].id,
              attribute.substitutes[key].type,
              t(
                'CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.CONDITIONS_LINK.LABEL'
              ),
              attribute.substitutes[key].value,
              attribute.frontend
            )
          )
        : null
    }

    if (
      attribute.type !== valueTypes.Boolean &&
      attribute.type !== valueTypes.File
    ) {
      return `${attribute.value} ${attribute.displayUnit ?? ''}`
    }

    return switchAttributeType(
      attribute.id,
      attribute.type,
      t('CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.CONDITIONS_LINK.LABEL'),
      attribute.value
    )
  }

  const toggle = () => {
    dispatch(toggleDetailsSection(!detailsSection))
  }

  if (!productCharacteristics && !item?.brand) return <></>

  return (
    <div ref={characteristicsContainerRef} className="xs-mdMinus:px-4 py-4 lgXl:p-4 px-3 border-b-[1px] last:border-none pb-4">
      <div className="text-[20px] leading-6 text-primary-main font-bold pt-2 pb-4">
        {t('CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.DATA.TITLE')}
      </div>
      {!!loading && (
        <div data-testid="skeleton">
          <ProductDetailsTableSkeleton />
        </div>
      )}
      {!loading && (
        <div className="flex flex-col gap-3">
          <div
            data-testid="product-characteristics"
            ref={characteristicRef}
            className={clsx(
              'text-blue-shade-60 leading-5 overflow-hidden transition-[max-height] duration-300 ease-in-out flex flex-col gap-3',
              detailsSection ? 'max-h-[2000px]' : 'max-h-[160px]'
            )}
          >
            {productCharacteristics?.map((detail) => (
              <div key={detail?.caption} className="flex flex-col">
                <div className="text-[16px] font-bold leading-6 pb-2">
                  {t(detail?.caption)}
                </div>
                <div className="grid xs-md:grid-cols-1  md-lg:grid-cols-1 lg-lgXl:grid-cols-1 grid-cols-2 gap-x-8">
                  {detail?.attributes.map((attr) => (
                    <LabelValueRow
                      key={attr?.id}
                      label={getAttributeLabel(attr)}
                      value={getAttributeValue(attr)}
                    />
                  ))}
                </div>
              </div>
            ))}

            {/* Brand & Product ID */}
            <div>
              <div className="text-[16px] font-bold leading-6 pb-2">
                {t(
                  'CATALOG.DETAILS_PAGE.MANUFACTURER_INFORMATION.PRODUCT_BRAND.TITLE'
                )}
              </div>
              <div className="grid xs-md:grid-cols-1 md-lg:grid-cols-1 lg-lgXl:grid-cols-1 grid-cols-2 gap-x-8">
                {item?.brand?.name && (
                  <LabelValueRow
                    label={t('CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND')}
                    value={item.brand.name}
                  />
                )}
                {item?.mid && (
                  <LabelValueRow
                    label={t('CATALOG.DETAILS_PAGE.PRODUCT_ID.LABEL')}
                    value={item.mid}
                  />
                )}
              </div>
            </div>
          </div>

          {isOverflowing && (
            <button
              data-testid="content-toggle"
              onClick={toggle}
              className="text-secondary-main cursor-pointer hover:underline text-base bg-transparent border-none p-0 text-left"
            >
              {detailsSection
                ? t('SHARED.SHOW_LESS.LABEL')
                : t('SHARED.SHOW_MORE.LABEL')}
            </button>
          )}
        </div>
      )}
    </div>
  )
}
