import clsx from 'clsx'
import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'

import { getAppBuyerUrl } from '@core/config/url'
import { useGtag } from '@core/hooks'
import { useMediaQuery } from '@core/hooks/useMediaQuery'
import { useFeatureFlag } from '@core/redux/features/featureFlags/useFeatureFlag'
import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'
import { APIResponse } from '@core/services/http/types'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'
import { CountryCode } from '@core/types'

import { BLACK_DEAL_TAGS, PDP } from '@modules/product/constants'
import { FRIDGE_USPS } from '@modules/product/constants/usp.constants'
import { ProductVariant } from '@modules/product/types/product.type'
import { ItemMarket } from '@modules/product/types/spi'
import { checkForAlcoholContent } from '@modules/product/utils'
import {
  getBrandInteractionEventPayload,
  getFurtherInformationEventPayload
} from '@modules/product/utils/ga-payloads.utils'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS
} from '@modules/shared/utils/optimizely.utils'

import { SM_BREAKPOINT } from '@styles/mediaQueriesBreakpoints'

import { ProductAlcoholWarning } from '../product-alcohol-warning'
import { ProductHighlights } from '../product-highlights'
import { ProductIdCopier } from '../product-id-copier'
import { ProductVariants } from '../product-variants'
import { UniqueSellingPoints } from '../unique-selling-points/UniqueSellingPoints'

interface Props {
  product: ItemMarket
  scrollToInfoComponent: () => void
  showHighlights: (value: boolean) => void
  productVariants: APIResponse<ProductVariant[]>
  market: CountryCode
}

export const ProductInformation = ({
  product,
  scrollToInfoComponent,
  showHighlights,
  productVariants,
  market
}: Props) => {
  const name = product?.item?.name
  const brand = product?.item?.brand
  const attributes = product?.item?.attributes

  const { t } = useTranslation([PDP])
  const matchesSm = useMediaQuery(`(${SM_BREAKPOINT})`)
  const [isMobile, setIsMobile] = useState<boolean>(true)
  const { gtagEvent } = useGtag()

  const belongsToBlackDeals = product?.tags?.some((item) =>
    BLACK_DEAL_TAGS.includes(item)
  )
  const isBlackDealsEnabled = useFeatureFlag(
    FeatureFlag.FF_BLACK_FRIDAY_COUNTDOWN
  )

  useEffect(() => {
    setIsMobile(matchesSm)
  }, [matchesSm])

  const scrollContainerRef = useRef(null)

  useEffect(() => {
    if (scrollContainerRef.current.scrollHeight > 340) {
      showHighlights(scrollContainerRef.current.scrollHeight > 340)
    }
  }, [scrollContainerRef.current?.scrollHeight, showHighlights])

  const clickShowMore = () => {
    scrollToInfoComponent()
    gtagEvent(getFurtherInformationEventPayload(name))
  }

  const trackBrandInteraction = () => {
    gtagEvent(getBrandInteractionEventPayload(brand))
  }

  const hasAlcoholContent = checkForAlcoholContent(product?.item?.attributes)
  const isFridge =
    product?.breadcrumb?.parents[product?.breadcrumb?.parents?.length - 1]
      ?.id === 'e45413de-3229-40b6-be52-2f77f97bcdc2'

  const fridgeUSPSAttributes = Object.values(FRIDGE_USPS).reduce((obj, key) => {
    if (key in attributes) obj[key] = attributes[key]
    return obj
  }, {})

  const fridgeKeyHighlightsExperiment = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )

  return (
    <>
      <div className="prose md:overflow-y-hidden" ref={scrollContainerRef}>
        <h1
          test-target={
            isMobile ? 'PRODUCT_TITLE.MOBILE' : 'PRODUCT_TITLE.DESKTOP'
          }
          className="text-blue-shade-60 font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 mb-0 subpixel-antialiased [overflow-wrap:anywhere]"
        >
          {name}
        </h1>
        <ProductIdCopier mid={product?.item?.mid} />
        <ProductVariants productVariants={productVariants} market={market} />
        <div className="hidden md:block lg:grid lg:grid-cols-2 lg:gap-6 lgXl:block">
          <ProductHighlights
            product={product}
            market={market}
            scrollToInfoComponent={clickShowMore}
          />
          {isFridge &&
            fridgeKeyHighlightsExperiment ===
              EXPERIMENT_VARIATIONS.VARIANT_TWO && (
              <div
                className={clsx(
                  isFridge &&
                    fridgeKeyHighlightsExperiment ===
                      EXPERIMENT_VARIATIONS.VARIANT_TWO
                )}
              >
                <UniqueSellingPoints
                  id="product-infromation"
                  category={product?.item?.idCategory}
                  attributes={fridgeUSPSAttributes}
                  isFridge={isFridge}
                  inMainInfo={true}
                />
              </div>
            )}
        </div>
      </div>
      {hasAlcoholContent && (
        <div className="hidden xl:block 2xl:block">
          <ProductAlcoholWarning />
        </div>
      )}
    </>
  )
}
