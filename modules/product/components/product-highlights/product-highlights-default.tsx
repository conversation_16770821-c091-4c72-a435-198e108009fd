import parse from 'html-react-parser'
import { useTranslation } from 'next-i18next'
import { useEffect, useState } from 'react'
import sanitizeHtml from 'sanitize-html'

import { useFeatureFlag } from '@core/ssr/featureFlag/featureFlag.context'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS,
  optimizelyInstance
} from '@shared/utils/optimizely.utils'

import { ArtificialHighlights } from '@modules/product/components/product-artificial-highlights'
import { PDP, PRODUCT_SERIES_ATTRIBUTE } from '@modules/product/constants'
import { Attribute, AttributeDTO, Brand } from '@modules/product/types/spi'
import { getPDEdata } from '@modules/product/utils'
import { clsx } from '@modules/shared/utils'

import { ProductBrand } from '../product-brand'
import { ProductSeries } from '../series-highlight'

type Props = {
  keyFeatures: string[]
  brand?: Brand
  artificialHighlights: AttributeDTO[]
  attributesSPI: Record<string, Attribute>
  displayBrandInfo?: boolean
  displayTitle?: boolean
  highlightsColor?: string
  isFridge?: boolean
}
export const ProductHighlightsDefault = ({
  keyFeatures,
  brand,
  artificialHighlights,
  attributesSPI,
  displayBrandInfo = true,
  displayTitle = false,
  highlightsColor = 'text-metro-blue-tint-20',
  isFridge = false
}: Props) => {
  const { t } = useTranslation([PDP])
  const tableWare = useFeatureFlag(FeatureFlag.FF_PDP_TABLEWARE_FEATURES)
  const seriesAttribute = attributesSPI?.[PRODUCT_SERIES_ATTRIBUTE] ?? null
  const [keyFeaturesAIGenerated, setKeyFeaturesAIGenerated] = useState(null)
  const [isAIGeneratedKeyFeatureVariant, setIsAIGeneratedKeyFeatureVariant] =
    useState(false)
  const isNewIdFF = useFeatureFlag(FeatureFlag.FF_OPTIMIZELY_NEW_ID)
  useEffect(() => {
    setKeyFeaturesAIGenerated(getPDEdata(attributesSPI, 'key_features'))
  }, [attributesSPI])

  const isProductEligibleForAIGeneratedKeyFeatures = !!(
    !keyFeatures?.length && keyFeaturesAIGenerated?.length
  )

  useEffect(() => {
    const AIGeneratedKFExperiment: Promise<string> =
      optimizelyInstance.getExperimentVariant<EXPERIMENT_VARIATIONS>(
        EXPERIMENT_NAMES.AI_GENERATED_KEY_FEATURES,
        isNewIdFF,
        {
          AI_generated_eligible_product:
            isProductEligibleForAIGeneratedKeyFeatures
        }
      )

    AIGeneratedKFExperiment.then((variant) => {
      setIsAIGeneratedKeyFeatureVariant(
        variant && variant === EXPERIMENT_VARIATIONS.VARIANT_TWO
      )
    })
  }, [isProductEligibleForAIGeneratedKeyFeatures, keyFeaturesAIGenerated])
  return (
    <>
      {displayTitle && (
        <h2 className="font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 antialiased m-0 mb-6 text-blue-shade-60">
          {t('CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.KEY_FEATURES.TITLE')}
        </h2>
      )}

      <ul
        test-target="product-artificial-highlights"
        className={clsx(
          'ml-2 pl-3 mb-0 list-outside list-disc not-prose',
          `marker:${highlightsColor}`
        )}
      >
        {tableWare && seriesAttribute && (
          <li
            data-testid="highlights-series"
            className={clsx(
              'text-base my-0 ml-[5px] pl-0 leading-6',
              highlightsColor
            )}
          >
            <ProductSeries
              series={seriesAttribute}
              brand={brand}
            ></ProductSeries>
          </li>
        )}
        {displayBrandInfo && brand?.name && (
          <li
            data-testid="highlights-brand"
            className={clsx(
              'text-base my-0 ml-[5px] pl-0 leading-6',
              highlightsColor
            )}
          >
            <ProductBrand brand={brand}></ProductBrand>
          </li>
        )}

        {!!keyFeatures?.length &&
          keyFeatures.map((feature: string, index) => (
            <li
              key={feature + index}
              className={clsx(
                'text-base my-0 ml-[5px] pl-0 leading-6',
                highlightsColor
              )}
            >
              <div className="text-ellipsis text-blue-shade-60">
                {parse(sanitizeHtml(feature, { allowedTags: [] }))}
              </div>
            </li>
          ))}

        {!keyFeatures?.length &&
        artificialHighlights?.length &&
        !isAIGeneratedKeyFeatureVariant ? (
          <ArtificialHighlights
            attributes={artificialHighlights}
            highlightsColor={highlightsColor}
          />
        ) : null}

        {isProductEligibleForAIGeneratedKeyFeatures &&
          isAIGeneratedKeyFeatureVariant &&
          keyFeaturesAIGenerated.map((feature: string, index) => (
            <li
              key={feature + index}
              className={clsx(
                'text-base my-0 ml-[5px] pl-0 leading-6',
                highlightsColor
              )}
            >
              <div className="overflow-hidden text-ellipsis text-blue-shade-60">
                {parse(sanitizeHtml(feature, { allowedTags: [] }))}
              </div>
            </li>
          ))}
      </ul>
    </>
  )
}
