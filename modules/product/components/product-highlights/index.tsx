import clsx from 'clsx'

import { NUMBER_5 } from '@core/constants/numbers'
import { useGtag } from '@core/hooks'
import { usePDPTranslation } from '@core/hooks/useTranslation'
import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'
import { CountryCode } from '@core/types'

import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS
} from '@shared/utils/optimizely.utils'

import { getFridgeAttributes } from '@modules/product/constants/usp.constants'
import { ItemMarket } from '@modules/product/types/spi'
import { getFurtherInformationEventPayload } from '@modules/product/utils'

import { ProductHighlightsFridge } from '../product-highlights-fridge'
import { ProductHighlightsDefault } from './product-highlights-default'

type Props = {
  product: ItemMarket
  market: CountryCode
  scrollToInfoComponent?: () => void
  displayTitle?: boolean
  displayBrandInfo?: boolean
  displayShowMoreInfo?: boolean
  className?: string
  highlightsColor?: string
}
export const ProductHighlights = ({
  product,
  market,
  className = '',
  highlightsColor = '',
  displayTitle,
  displayBrandInfo,
  displayShowMoreInfo = true,
  scrollToInfoComponent
}: Props) => {
  const { t } = usePDPTranslation()
  const fridgeKeyHighlightsExperiment = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )
  const keyFeatures = product?.item?.keyFeatures
  const isFridge =
    product?.breadcrumb?.parents[product?.breadcrumb?.parents?.length - 1]
      ?.id === 'e45413de-3229-40b6-be52-2f77f97bcdc2'
  const fridgeKeyFeatures = getFridgeAttributes(product?.item?.attributes)

  const { gtagEvent } = useGtag()
  const clickShowMore = () => {
    const itemName = product?.item?.name
    scrollToInfoComponent()
    gtagEvent(getFurtherInformationEventPayload(itemName))
  }

  return (
    <div className={clsx(className)}>
      {isFridge &&
      fridgeKeyFeatures?.length >= NUMBER_5 &&
      fridgeKeyHighlightsExperiment === EXPERIMENT_VARIATIONS.VARIANT_TWO ? (
        <ProductHighlightsFridge
          item={product?.item}
          displayTitle={displayTitle}
          market={market}
        />
      ) : (
        <ProductHighlightsDefault
          keyFeatures={keyFeatures}
          brand={product?.item?.brand}
          artificialHighlights={product?.item?.artificialHighlights}
          displayTitle={displayTitle}
          attributesSPI={product?.item?.attributes}
          highlightsColor={highlightsColor}
          isFridge={isFridge}
          displayBrandInfo={displayBrandInfo}
        />
      )}

      {displayShowMoreInfo && (
        <>
          {isFridge &&
          fridgeKeyHighlightsExperiment ===
            EXPERIMENT_VARIATIONS.VARIANT_TWO ? (
            <div className="md:sticky bottom-0 pt-[10px] pb-4">
              <a
                data-testid={'read-more-link'}
                test-target="product-highlights-read-more"
                className="block text-secondary-main cursor-pointer hover:underline text-base text-left"
                onClick={clickShowMore}
              >
                {t('CATALOG.DETAILS_PAGE.SEE_ALL_CHARACTERISTICS')}
              </a>
            </div>
          ) : (
            <div className="sticky bottom-0 pt-[10px]">
              <a
                test-target="product-highlights-read-more"
                className="block text-secondary-main cursor-pointer hover:underline text-base text-left"
                onClick={scrollToInfoComponent}
              >
                {t('CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL')}
              </a>
            </div>
          )}
        </>
      )}
    </div>
  )
}
