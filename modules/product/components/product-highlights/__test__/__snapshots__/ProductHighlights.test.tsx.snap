// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Product highlights should match snapshot and should not have title 1`] = `
<div>
  <div
    class=""
  >
    <ul
      class="ml-2 pl-3 mb-0 list-outside list-disc not-prose marker:"
      test-target="product-artificial-highlights"
    >
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
        data-testid="highlights-brand"
      >
        <span
          class="text-blue-shade-60"
        >
          CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND
        </span>
        : 
        <a
          class="text-secondary-main hover:underline"
          href="https://marketplace-test.metro.de/marktplatz/b/dyson"
          rel="noreferrer"
          target="_blank"
        >
          Dyson
        </a>
      </li>
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
      >
        <div
          class="text-ellipsis text-blue-shade-60"
        >
          Vollständige Versiegelung nach HEPA-13 Standard
        </div>
      </li>
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
      >
        <div
          class="text-ellipsis text-blue-shade-60"
        >
          Erkennt und zersetzt Formaldehyd
        </div>
      </li>
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
      >
        <div
          class="text-ellipsis text-blue-shade-60"
        >
          Vermischt und verteilt die Luft in drei Modi
        </div>
      </li>
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
      >
        <div
          class="text-ellipsis text-blue-shade-60"
        >
          Luftreiniger von Dyson mit Befeuchtungs- und Ventilatorfunktion
        </div>
      </li>
      <li
        class="text-base my-0 ml-[5px] pl-0 leading-6"
      >
        <div
          class="text-ellipsis text-blue-shade-60"
        >
          Mehr als 36 Stunden lang hygienische Befeuchtung
        </div>
      </li>
    </ul>
    <div
      class="sticky bottom-0 pt-[10px]"
    >
      <a
        class="block text-secondary-main cursor-pointer hover:underline text-base text-left"
        test-target="product-highlights-read-more"
      >
        CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL
      </a>
    </div>
  </div>
</div>
`;
