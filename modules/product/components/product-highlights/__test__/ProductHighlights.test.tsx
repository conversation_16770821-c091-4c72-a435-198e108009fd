import { fireEvent, screen, waitFor } from '@testing-library/react'

import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'
import { renderWithProviders } from '@core/utils/testing'

import { COOLING_TYPE } from '@modules/product/constants/usp.constants'
import {
  FridgeProductMockV2,
  ProductIndexMock
} from '@modules/product/mocks/product-index/product-index-mock'
import {
  getBrandInteractionEventPayload,
  getCoolingType
} from '@modules/product/utils'
import { Sidebarv2Provider } from '@modules/shared/components/SidebarV2/SidebarContext'
import { EXPERIMENT_VARIATIONS } from '@modules/shared/utils/optimizely.utils'

import { ProductHighlights } from '..'

jest.mock('@core/redux/features/optimizely', () => ({
  useOptimizelyDecisionValue: jest.fn()
}))

jest.mock('@modules/product/utils', () => ({
  getPDEdata: jest.fn(),
  getBrandInteractionEventPayload: jest.fn(),
  getCoolingType: jest.fn()
}))

describe('Product highlights', () => {
  it('should match snapshot and should not have title', () => {
    const { container } = renderWithProviders(
      <ProductHighlights
        product={{
          ...ProductIndexMock,
          item: {
            ...ProductIndexMock.item,
            attributes: {
              ...ProductIndexMock.item.attributes,
              brand_series: {
                id: 'a4b68ac2-59c3-423d-b420-bcdfabca5fd7',
                code: 'brand_series',
                type: 1,
                label: 'Serie',
                value: 'Elégance Nature',
                displayValue: null,
                frontend: '1',
                measure: null
              }
            }
          }
        }}
      />
    )

    expect(container).toMatchSnapshot()
    expect(screen.queryByRole('heading')).toBeNull()
  })

  it('should display title', () => {
    renderWithProviders(
      <ProductHighlights product={ProductIndexMock} displayTitle={true} />
    )

    expect(screen.getByRole('heading')).toBeInTheDocument()
  })

  it('should not display brand info', () => {
    renderWithProviders(
      <ProductHighlights product={ProductIndexMock} displayBrandInfo={false} />
    )

    expect(screen.queryByTestId('highlights-brand')).not.toBeInTheDocument()
  })

  it('should render artificial highlights when key features array empty', () => {
    const { container } = renderWithProviders(
      <ProductHighlights
        product={{
          ...ProductIndexMock,
          item: {
            ...ProductIndexMock.item,
            attributes: {},
            keyFeatures: []
          }
        }}
        displayBrandInfo={false}
      />
    )

    expect(
      screen.getAllByTestId('artificial-highlights')[0]
    ).toBeInTheDocument()
  })

  it('should render the new fridge key features table UI', () => {
    useOptimizelyDecisionValue.mockImplementation(
      () => EXPERIMENT_VARIATIONS.VARIANT_TWO
    )
    getCoolingType.mockImplementation(() => COOLING_TYPE.VENTILATED)
    renderWithProviders(
      <Sidebarv2Provider>
        <ProductHighlights
          product={FridgeProductMockV2}
          displayBrandInfo={false}
        />
      </Sidebarv2Provider>
    )
    expect(screen.getByTestId('fridge-key-characteristics')).toBeInTheDocument()
  })

  it('should call trigger functions', async () => {
    useOptimizelyDecisionValue.mockImplementation(
      () => EXPERIMENT_VARIATIONS.VARIANT_TWO
    )
    getCoolingType.mockImplementation(() => COOLING_TYPE.COMPRESSOR)

    renderWithProviders(
      <ProductHighlights
        product={FridgeProductMockV2}
        displayBrandInfo={false}
      />
    )
    const brandLink = screen.getByTestId('brand-link')
    fireEvent.click(brandLink)

    await waitFor(() => {
      expect(getBrandInteractionEventPayload).toBeCalled()
    })
  })
})
