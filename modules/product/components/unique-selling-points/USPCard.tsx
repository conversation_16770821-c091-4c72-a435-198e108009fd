import clsx from 'clsx'

import { useMarket } from '@core/hooks'
import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'

import { USP_ICONS } from '@modules/product/constants/usp.constants'
import { Attribute } from '@modules/product/types/spi'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS
} from '@modules/shared/utils/optimizely.utils'

interface Props {
  attribute: Attribute
  inMainInfo?: boolean
  isFridge?: boolean
}

export const USPCard = ({
  attribute,
  inMainInfo = false,
  isFridge = false
}: Props) => {
  const countryCode = useMarket()
  const variation = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )

  const isFridgeExperimentNewVariation =
    isFridge && variation === EXPERIMENT_VARIATIONS.VARIANT_TWO

  return (
    <>
      {attribute && (
        <div
          data-testid={attribute?.code}
          className={clsx(
            'h-full transition-shadow bg-white-main flex flex-col rounded-lg p-3 lg:pb-4 justify-start items-center hover:shadow-[0_4px_20px_0px_rgba(70,90,110,0.1)_,_0_2px_0px_-2px_rgba(70,90,110,0.1)] cursor-pointer xs-mdMinus:border',
            inMainInfo && '!bg-blue-tint-90 md:!flex-row'
          )}
        >
          <div>
            {
              USP_ICONS(inMainInfo ? 'mb-2 md:mb-0 md:mr-8px' : 'mb-2')[
                `${attribute?.code}${isFridgeExperimentNewVariation ? '_V2' : ''}`
              ]
            }
          </div>
          <div
            lang={countryCode}
            className={clsx(
              'w-full font-bold text-base leading-5 hyphens-auto text-primary-main',
              isFridgeExperimentNewVariation
                ? 'text-center md:text-left'
                : 'text-center'
            )}
          >
            {attribute?.label}
          </div>
        </div>
      )}
    </>
  )
}
