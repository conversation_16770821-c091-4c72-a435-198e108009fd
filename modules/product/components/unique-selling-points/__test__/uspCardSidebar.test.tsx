import { screen, waitFor } from '@testing-library/react'

import { renderWithProviders } from '@core/utils/testing'

import { getUSPS } from '@modules/product/utils/getUniqueSellingPoints'
import { Sidebarv2Provider } from '@modules/shared/components/SidebarV2/SidebarContext'

import { UniqueSellingPoints } from '../UniqueSellingPoints'

jest.mock('@modules/product/utils/getUniqueSellingPoints', () => ({
  getUSPS: jest.fn()
}))

describe('UniqueSellingPoints Component', () => {
  const mockAttribute = {
    dishwasher_safe: {
      code: 'dishwasher_safe',
      displayValue: null,
      frontend: '1',
      id: 'c1de0a81-4576-4885-8aec-6c1d8768418',
      label: 'Spülmaschinenfest',
      measure: null,
      type: 2,
      value: '1'
    }
  }
  const mockCategory = 'tableware'

  beforeEach(() => {
    jest.clearAllMocks()
    ;(getUSPS as jest.Mock).mockReturnValue([
      { code: 'dishwasher_safe', label: 'Dishwasher safe' }
    ])
  })

  test('renders correctly after fetching data', async () => {
    const { container, rerender } = renderWithProviders(
      <Sidebarv2Provider>
        <UniqueSellingPoints
          isFridge={false}
          attributes={mockAttribute}
          category={mockCategory}
        />
      </Sidebarv2Provider>
    )

    // Ensure the mocked function is called with correct arguments
    expect(getUSPS).toHaveBeenCalledWith(mockAttribute, mockCategory)

    // Wait for the component to re-render after async state update
    await waitFor(() => expect(getUSPS).toHaveBeenCalled())

    // Trigger re-render after data is fetched
    rerender(
      <Sidebarv2Provider>
        <UniqueSellingPoints
          isFridge={false}
          attributes={mockAttribute}
          category={mockCategory}
        />
      </Sidebarv2Provider>
    )

    const usp = screen.getByTestId('dishwasher_safe')

    expect(container).toMatchSnapshot()
  })

  test('displays the learn more link', async () => {
    const { container, rerender } = renderWithProviders(
      <Sidebarv2Provider>
        <UniqueSellingPoints
          isFridge={false}
          attributes={mockAttribute}
          category={mockCategory}
        />
      </Sidebarv2Provider>
    )
    expect(getUSPS).toHaveBeenCalledWith(mockAttribute, mockCategory)
    await waitFor(() => expect(getUSPS).toHaveBeenCalled())

    rerender(
      <Sidebarv2Provider>
        <UniqueSellingPoints
          isFridge={false}
          attributes={mockAttribute}
          category={mockCategory}
        />
      </Sidebarv2Provider>
    )

    expect(screen.getAllByTestId('learn_more_about_usp')[0]).toBeInTheDocument()
  })

  test('does not render when no USPs are fetched', async () => {
    ;(getUSPS as jest.Mock).mockReturnValue([])

    const { container } = renderWithProviders(
      <Sidebarv2Provider>
        <UniqueSellingPoints
          isFridge={false}
          attributes={mockAttribute}
          category={mockCategory}
        />
      </Sidebarv2Provider>
    )

    expect(container).toBeEmptyDOMElement()
  })
})
