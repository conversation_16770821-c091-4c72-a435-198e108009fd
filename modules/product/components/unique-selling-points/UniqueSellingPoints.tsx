import clsx from 'clsx'
import { useTranslation } from 'next-i18next'
import { useEffect, useState } from 'react'

import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'

import { PDP } from '@modules/product/constants'
import { USPS_TEXTS, USP_ICONS } from '@modules/product/constants/usp.constants'
import { Attribute } from '@modules/product/types/spi'
import { getUSPS } from '@modules/product/utils/getUniqueSellingPoints'
import { SidebarV2 } from '@modules/shared/components/SidebarV2'
import { SIDEBAR } from '@modules/shared/components/SidebarV2/Sidebar.constant'
import { useSidebarV2 } from '@modules/shared/components/SidebarV2/SidebarContext'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS
} from '@modules/shared/utils/optimizely.utils'

import { USPCard } from './USPCard'

interface Props {
  id?: string
  attributes: Record<string, Attribute>
  category: string
  inMainInfo?: boolean
  isFridge: boolean
}

const LearnMoreButton = ({ label, onClick, className = '' }) => {
  return (
    <a
      onClick={onClick}
      className={clsx(
        'hover:underline text-base text-secondary-main inline-flex cursor-pointer',
        className
      )}
      data-testid="learn_more_about_usp"
    >
      {label}
    </a>
  )
}

export const UniqueSellingPoints = ({
  id,
  attributes,
  category,
  isFridge,
  inMainInfo = false
}: Props) => {
  const { t } = useTranslation([PDP])
  const usps = getUSPS(attributes, category)
  const { handleOpen, handleClose, isOpen } = useSidebarV2()

  const [acitveUsp, setActiveUsp] = useState<string>(null)

  const fridgeHighlightsVariation = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )

  const isFridgeHighlightsNewVariation =
    isFridge && fridgeHighlightsVariation === EXPERIMENT_VARIATIONS.VARIANT_TWO

  const sidebarId = `${SIDEBAR.USP_ALL_IN_ONE} - ${id}`
    useEffect(() => {
    if (acitveUsp) {
      handleOpen(sidebarId)
    } else {
      handleClose(sidebarId)
    }
  }, [acitveUsp])

  useEffect(() => {
    if(!isOpen[sidebarId]) {
      setActiveUsp(null)
    }
  }, [isOpen])

  return (
    <>
      {!!usps?.length && (
        <>
          <div className="not-prose bg-white-main md:bg-transparent">
            <div className="flex items-center justify-between md:justify-start md:gap-2 leading-6 mb-2.5">
              <h4 className="text-regular leading-[26px] font-bold text-metro-blue-main">
                {t('CATALOG.DETAILS_PAGE.TABLEWARE.DESIGNED_FOR_PROFESSIONALS')}
              </h4>
              <LearnMoreButton
                className={clsx(
                  isFridgeHighlightsNewVariation ? 'hidden lgXl:flex' : 'block'
                )}
                label={t('CATALOG.DETAILS_PAGE.USPS.LEARN_MORE')}
                onClick={() => setActiveUsp('all')}
              />
            </div>
            <div
              className={clsx(
                'grid gap-3',
                isFridgeHighlightsNewVariation
                  ? 'grid-cols-2'
                  : 'grid-cols-3 lg:flex'
              )}
            >
              {usps.map((usp) => (
                <div
                  className="lg:min-w-[82px] lg:flex-[1_1_30%]"
                  key={usp.code}
                  onClick={() => setActiveUsp(usp.code)}
                >
                  <USPCard
                    inMainInfo={inMainInfo}
                    attribute={usp}
                    isFridge={isFridge}
                  />
                </div>
              ))}
            </div>
            <LearnMoreButton
              className={clsx(
                isFridgeHighlightsNewVariation
                  ? 'flex lgXl:hidden mt-1.5'
                  : 'hidden'
              )}
              label={t('CATALOG.DETAILS_PAGE.USPS.LEARN_MORE')}
              onClick={() => setActiveUsp('all')}
            />
          </div>

          <SidebarV2
            id={sidebarId}
            ignoreTrigger={true}
            trigger={<></>}
            onClose={() => setActiveUsp(null)}
          >
            {acitveUsp === 'all' && (
              <div className="text-2xl leading-8 text-primary-main font-bold pt-1 md:pt-4">
                {t('CATALOG.DETAILS_PAGE.TABLEWARE.DESIGNED_FOR_PROFESSIONALS')}
                {': '}
              </div>
            )}
            {usps
              .filter((usp) => acitveUsp === 'all' || acitveUsp == usp.code)
              .map((item) => (
                <div key={item.code} className='flex flex-col gap-2'>
                  <div className="flex items-center pt-1 md:pt-4">
                    <div>{USP_ICONS('mb-0 !block')[item.code]}</div>
                    <div className="ml-2 text-[20px] font-bold leading-6 text-primary-main font-lato">
                      {item.label}
                    </div>
                  </div>
                  <div className="border-b border-b-primary-tint-80 pb-4 mb-4 last:border-none">
                    <span className="text-base leading-6 text-primary-main font-normal">
                      {t(USPS_TEXTS[item.code])}
                    </span>
                  </div>
                </div>
              ))}
          </SidebarV2>
        </>
      )}
    </>
  )
}
