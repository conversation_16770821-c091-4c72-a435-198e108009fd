import { getAppBuyerUrl } from '@core/config/url'
import { useGtag } from '@core/hooks'
import { usePDPTranslation } from '@core/hooks/useTranslation'
import { CountryCode } from '@core/types'

import { BRAND_ROUTE } from '@modules/product/constants'
import {
  COOLING_TYPE,
  COOLING_TYPE_ICON,
  getFridgeAttributes
} from '@modules/product/constants/usp.constants'
import { Attribute, Item } from '@modules/product/types/spi'
import {
  getBrandInteractionEventPayload,
  getCoolingType
} from '@modules/product/utils'
import { SVGIcon } from '@modules/shared/components'
import { SidebarV2 } from '@modules/shared/components/SidebarV2'
import { SIDEBAR } from '@modules/shared/components/SidebarV2/Sidebar.constant'
import { SVG_NAMES, SvgNames } from '@modules/shared/icons/constants'

interface Props {
  item: Item
  market: CountryCode
  displayTitle: boolean
}

export const ProductHighlightsFridge = ({
  item,
  market,
  displayTitle
}: Props) => {
  const brand = item?.brand
  const fridgeKeyFeatures = getFridgeAttributes(item?.attributes)

  const coolingType = getCoolingType(
    item?.attributes?.['cooling_type']?.value,
    market
  )
  const coolingIcon = COOLING_TYPE_ICON[coolingType || '']

  const webAppUrl = getAppBuyerUrl(market)

  const { gtagEvent } = useGtag()
  const { t } = usePDPTranslation()

  const trackBrandInteraction = () => {
    gtagEvent(getBrandInteractionEventPayload(brand))
  }

  return (
    <div data-testid='fridge-key-characteristics' className="flex flex-col flex-1">
      {displayTitle && (
        <h2 className="lgXl:hidden block text-blue-shade-60 mt-0 font-bold md:!leading-[26px] text-[16px] !leading-6 mb-0 subpixel-antialiased [overflow-wrap:anywhere]">
          {t('CATALOG.DETAILS_PAGE.PRODUCT_CHARACTERISTICS')}
        </h2>
      )}
      <div
        key="brand"
        className="flex flex-row border-b border-b-grey-tint-90 py-5px gap-6"
      >
        <div className="flex-1 max-w-[40%] text-metro-blue-tint-20 text-[14px]">
          {t('CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND')}
        </div>
        <div className="text-[14px] font-bold text-metro-blue-main">
          {brand?.slug && brand?.name && (
            <a
              data-testid='brand-link'
              className="text-secondary-main hover:underline"
              href={`${webAppUrl}/${BRAND_ROUTE}/${brand.slug}`}
              target="_blank"
              rel="noreferrer"
              onClick={trackBrandInteraction}
            >
              {brand.name}
            </a>
          )}
          {!brand?.slug && brand?.name && (
            <span className="text-xs md:text-lg ">{brand.name}</span>
          )}
        </div>
      </div>
      {fridgeKeyFeatures.map((item) => {
        // console.log((item as Attribute)?.code)
        if (Array.isArray(item)) {
          return (
            <div
              key="dimensions"
              className="flex flex-row border-b border-b-grey-tint-90 py-5px gap-6"
            >
              <div className="flex-1 max-w-[40%] text-metro-blue-tint-20 text-[14px]">
                {t('CATALOG.DETAILS_PAGE.DIMENSIONS')}
              </div>
              <div className="text-[14px] font-bold text-metro-blue-main">
                {item
                  .map(
                    (attr) =>
                      `${Number(attr.value) * 100} ${attr?.measure?.displayUnit || ''}`
                  )
                  .join(' x ')}
              </div>
            </div>
          )
        }
        if ((item as Attribute)?.code === 'cooling_type') {
          return (
            <div
              key="cooling_type"
              className="flex items-center border-b border-b-grey-tint-90 py-5px gap-6"
            >
              <div className="flex-1 max-w-[40%] text-metro-blue-tint-20 text-[14px]">
                {(item as Attribute)?.label}
              </div>
              <div className="flex items-center gap-2.5">
                {coolingType === COOLING_TYPE.COMPRESSOR ? (
                  <div className="flex items-center gap-1.5">
                    <div className="text-[14px] font-bold text-metro-blue-main flex items-center gap-4px bg-blue-tint-90 px-8px py-4px rounded-md">
                      {t(`CATALOG.DETAILS_PAGE.COOLING.${coolingType}`)}
                    </div>
                  </div>
                ) : (
                  <SidebarV2
                    key={(item as Attribute)?.code}
                    id={SIDEBAR.COOLING_TYPE}
                    trigger={
                      <div className="flex items-center gap-1.5 cursor-pointer">
                        <div className="text-[14px] font-bold text-metro-blue-main flex items-center gap-4px bg-blue-tint-90 px-8px py-4px rounded-md">
                          {!!coolingIcon && (
                            <SVGIcon
                              width="20px"
                              height="20px"
                              name={coolingIcon as SvgNames}
                            />
                          )}
                          {t(`CATALOG.DETAILS_PAGE.COOLING.${coolingType}`)}
                        </div>
                        <SVGIcon
                          className="cursor-pointer"
                          width="16px"
                          height="16px"
                          name={SVG_NAMES.QUESTION_MARK}
                        />
                      </div>
                    }
                  >
                    <div className="flex flex-col gap-4 text-base leading-[21px] text-metro-blue-main">
                      <div className="flex items-center gap-2">
                        {!!coolingIcon && (
                          <SVGIcon
                            width="40px"
                            height="40px"
                            name={coolingIcon as SvgNames}
                          />
                        )}
                        <div className="flex flex-col text-lg leading-6">
                          <h3 className="font-normal text-metro-blue-tint-40">
                            {(item as Attribute)?.label}
                          </h3>
                          <h3 className="font-bold text-metro-blue-main">
                            {t(`CATALOG.DETAILS_PAGE.COOLING.${coolingType}`)}
                          </h3>
                        </div>
                      </div>
                      <p className="font-bold">
                        {t(
                          `CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.${coolingType}.DESCRIPTION_BOLD`
                        )}
                      </p>
                      <p className="font-normal">
                        {t(
                          `CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.${coolingType}.DESCRIPTION_NORMAL`
                        )}
                      </p>
                      <div className="flex flex-col gap-4 p-4 bg-blue-tint-95">
                        <p>
                          <b>
                            {t(
                              'CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.BEST_FOR'
                            )}
                            :
                          </b>{' '}
                          {t(
                            `CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.${coolingType}.BEST_FOR`
                          )}
                        </p>
                        <div className="flex flex-col gap-4">
                          <p className="font-bold">
                            {t(
                              'CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.PROS'
                            )}
                          </p>
                          <ul className="list-disc pl-5 space-y-1">
                            {t(
                              `CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.${coolingType}.PROS`
                            )
                              .split('\n')
                              .map((point, index) => (
                                <li key={`pros-${index}`}>{point}</li>
                              ))}
                          </ul>
                        </div>
                        <div className="flex flex-col gap-4">
                          <p className="font-bold">
                            {t(
                              'CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.CONS'
                            )}
                          </p>
                          <ul className="list-disc pl-5 space-y-1">
                            {t(
                              `CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.${coolingType}.CONS`
                            )
                              .split('\n')
                              .map((point, index) => (
                                <li key={`pros-${index}`}>{point}</li>
                              ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </SidebarV2>
                )}
              </div>
            </div>
          )
        }
        return (
          <div
            key={(item as Attribute)?.code}
            className="flex flex-row border-b border-b-grey-tint-90 py-5px gap-6"
          >
            <div className="flex-1 max-w-[40%] text-metro-blue-tint-20 text-[14px]">
              {(item as Attribute)?.label}
            </div>
            <div className="text-[14px] font-bold text-metro-blue-main">
              {(item as Attribute)?.value}
            </div>
          </div>
        )
      })}
    </div>
  )
}
