import clsx from 'clsx'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { ComponentType, useEffect, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'

import { NUMBER_0 } from '@core/constants/numbers'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { useOptimizelyDecisionValue } from '@core/redux/features/optimizely'
import {
  toggleDetailsSection,
  toggleGeneralInfo
} from '@core/redux/features/product/productSlice'
import { APIResponse } from '@core/services/http/types'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'
import { CountryCode } from '@core/types'

import { GaTrackingStringEnum } from '@modules/ga-tracking/types/gaTracking.enum'
import { TablewareBannerProps } from '@modules/product/components/pdp-tableware-banner'
import { SeriesProductsProps } from '@modules/product/components/product-series-carousel'
import { FRIDGE_USPS } from '@modules/product/constants/usp.constants'
import { PdpStories, ProductVariant } from '@modules/product/types'
import { ItemMarket } from '@modules/product/types/spi'
import { getSelectedVariantsAsString } from '@modules/product/utils'
import { getAreChildNodesHigherOrEqualThanValue } from '@modules/product/utils/getAreChildNodesHigherOrEqualThanValue'
import { viewportOnlyHOC } from '@modules/shared/components/ViewportOnlyHOC'
import {
  EXPERIMENT_NAMES,
  EXPERIMENT_VARIATIONS,
  optimizelyInstance
} from '@modules/shared/utils/optimizely.utils'

import { Information } from '../information'
import { InformationSkeleton } from '../information/skeleton'
import { ProductBuybox } from '../product-buybox'
import { ProductDescriptionComponent } from '../product-description'
import { ProductHighlights } from '../product-highlights'
import { ProductMainComponent } from '../product-main'
import { ProductFlixMediaContent } from '../product-rich-media-contents/flixmedia'
import { StoreAvailability } from '../store-availability'
import { UniqueSellingPoints } from '../unique-selling-points/UniqueSellingPoints'

const ProductSeriesCarousel: ComponentType<SeriesProductsProps> = dynamic(
  () =>
    import('@modules/product/components/product-series-carousel').then(
      (component) => viewportOnlyHOC(component.SeriesProducts, NUMBER_0)
    ),
  { ssr: false }
)

const PDPTablewareBanner: ComponentType<TablewareBannerProps> = dynamic(
  () =>
    import('@modules/product/components/pdp-tableware-banner').then(
      (component) => viewportOnlyHOC(component.PDPTablewareBanner, NUMBER_0)
    ),
  { ssr: false }
)

interface Props {
  locale: string
  product: ItemMarket
  itemListName: string
  pdpStories: PdpStories
  market: CountryCode
  setHideCarousels: any
  productVariants: APIResponse<ProductVariant[]> | null
}

export const ProductContent = ({
  market,
  locale,
  product,
  pdpStories,
  itemListName,
  productVariants,
  setHideCarousels
}: Props) => {
  const router = useRouter()
  const dispatch = useDispatch()

  const componentGeneralInfoRef = useRef(null)
  const productMainComponentRef = useRef<HTMLDivElement>(null)
  const desktopBuyboxSectionRef = useRef<HTMLDivElement>(null)
  const characteristicsContainerRef = useRef<HTMLDivElement>(null)

  const isNewIdFF = useFeatureFlag(FeatureFlag.FF_OPTIMIZELY_NEW_ID)

  const [offerAddedOnRedirection, setOfferAddedOnRedirection] = useState(null)
  const [highlightsInDescription, setHighlightsInDescription] = useState(false)
  const [newProductDataViewOnPdp, setNewProductDataViewOnPdp] = useState(null)

  const fridgeKeyHighlightsExperiment = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES
  )
  const newProductDataDesignExperiment = useOptimizelyDecisionValue(
    EXPERIMENT_NAMES.NEW_PRODUCT_DATA_VIEW_ON_PDP
  )

  const handleSimilarProducts = () => {
    setHideCarousels(true)
  }

  const showHighlights = (value) => {
    setHighlightsInDescription(value)
  }

  const scrollToInfoComponent = () => {
    const ref =
      newProductDataDesignExperiment === EXPERIMENT_VARIATIONS.VARIANT_TWO
        ? characteristicsContainerRef
        : componentGeneralInfoRef
    ref?.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
    //Show open by default when shoe more link clicked
    dispatch(toggleGeneralInfo(true))
    dispatch(toggleDetailsSection(true))
  }

  // if product main section is less than buybox section toggle general info accordeon, recheck on offerId change
  useEffect(() => {
    if (
      productMainComponentRef?.current &&
      desktopBuyboxSectionRef?.current &&
      !getAreChildNodesHigherOrEqualThanValue(
        productMainComponentRef.current,
        desktopBuyboxSectionRef.current.offsetHeight
      )
    ) {
      dispatch(toggleGeneralInfo(true))
    }
  }, [router.query?.offerId, desktopBuyboxSectionRef?.current?.offsetHeight])

  useEffect(() => {
    const newProductDataViewOnPdp: Promise<string> =
      optimizelyInstance.getExperimentVariant<EXPERIMENT_VARIATIONS>(
        EXPERIMENT_NAMES.NEW_PRODUCT_DATA_VIEW_ON_PDP,
        isNewIdFF,
        {
          parent_category:
            product?.breadcrumb?.parents?.at(-1)?.id ??
            product?.breadcrumb?.id ??
            ''
        }
      )

    newProductDataViewOnPdp.then((variant) => {
      setNewProductDataViewOnPdp(variant ?? EXPERIMENT_VARIATIONS.CONTROL_GROUP)
    })
  }, [])

  const item = product?.item
  const shouldShowCallback = !!pdpStories?.callback
  const shouldAddOfferToCart = router.query?.addToCartOffer as string
  const selectedVariants = item?.hasVariants
    ? getSelectedVariantsAsString(productVariants)
    : GaTrackingStringEnum.No

  const isFridge =
    product?.breadcrumb?.parents[product?.breadcrumb?.parents?.length - 1]
      ?.id === 'e45413de-3229-40b6-be52-2f77f97bcdc2'

  const fridgeUSPSAttributes = Object.values(FRIDGE_USPS).reduce((obj, key) => {
    if (key in item?.attributes) obj[key] = item?.attributes[key]
    return obj
  }, {})

  const isNewFridgeKeyFeaturesDesign =
    isFridge &&
    fridgeKeyHighlightsExperiment === EXPERIMENT_VARIATIONS.VARIANT_TWO

  return (
    <div className="flex flex-col lg:flex-row justify-between">
      <div
        ref={productMainComponentRef}
        className="flex flex-col lg:max-w-[61%] xl:max-w-[67%] max-h-[67%] lg:mr-6 lg:[flex:61] xl:[flex:67]"
      >
        <ProductMainComponent
          itemListName={itemListName}
          market={market}
          showHighlights={showHighlights}
          product={product}
          productVariants={productVariants}
          productVariant={selectedVariants}
          isCallbackShown={shouldShowCallback}
          scrollToInfoComponent={scrollToInfoComponent}
        />
        <div
          className="flex flex-col lg:hidden"
          test-target="other-offers-pdp-link-mobile"
        >
          <ProductBuybox
            market={market}
            product={product}
            buyboxType="mobile"
            itemListName={itemListName}
            productVariant={selectedVariants}
            test-id="PRODUCT_DETAILS.BUYBOX.MOBILE"
            shouldShowCallback={shouldShowCallback}
            shouldAddOfferToCart={shouldAddOfferToCart}
            offerAddedOnRedirection={offerAddedOnRedirection}
            setOfferAddedOnRedirection={setOfferAddedOnRedirection}
          />
        </div>

        <div
          className={clsx(
            'flex flex-col md:p-0 mt-6',
            isNewFridgeKeyFeaturesDesign
              ? 'flex md:hidden bg-white-main'
              : 'gap-6 flex-col-reverse'
          )}
        >
          {(newProductDataViewOnPdp !== EXPERIMENT_VARIATIONS.VARIANT_TWO ||
            isNewFridgeKeyFeaturesDesign) && (
            <ProductHighlights
              className={clsx(
                'block p-4 md:hidden bg-white-main',
                isNewFridgeKeyFeaturesDesign ? 'pb-0' : ''
              )}
              product={product}
              market={market}
              displayTitle={true}
              scrollToInfoComponent={scrollToInfoComponent}
            />
          )}

          <div
            className={clsx(
              'bg-white-main md:bg-transparent p-4 md:p-0',
              isNewFridgeKeyFeaturesDesign ? 'pt-0' : ''
            )}
          >
            <UniqueSellingPoints
              id="product-content"
              category={item?.idCategory}
              attributes={isFridge ? fridgeUSPSAttributes : item?.attributes}
              isFridge={isFridge}
              inMainInfo={isNewFridgeKeyFeaturesDesign}
            />
          </div>
        </div>

        <ProductSeriesCarousel
          locale={locale}
          market={market}
          brand={item?.brand}
          productId={item?.idItem}
          attributes={item?.attributes}
          productVariants={productVariants}
        />

        {!newProductDataViewOnPdp && <InformationSkeleton />}
        {(newProductDataViewOnPdp === EXPERIMENT_VARIATIONS.CONTROL_GROUP ||
          newProductDataViewOnPdp === EXPERIMENT_VARIATIONS.VARIANT_ONE) && (
          <ProductDescriptionComponent
            product={product}
            showHighlightsInDescription={highlightsInDescription}
            descriptionRef={componentGeneralInfoRef}
          />
        )}

        {newProductDataViewOnPdp === EXPERIMENT_VARIATIONS.VARIANT_TWO && (
          <Information
            product={product}
            characteristicsContainerRef={characteristicsContainerRef}
          />
        )}
        <ProductFlixMediaContent
          product={product}
          hideSimilarProducts={handleSimilarProducts}
        />
        <PDPTablewareBanner pdpStories={pdpStories} />
      </div>
      <div
        ref={desktopBuyboxSectionRef}
        test-target="other-offers-pdp-link-desktop"
        className="hidden lg:flex flex-col lg:w-[39%] xl:w-[33%]"
      >
        <ProductBuybox
          market={market}
          product={product}
          buyboxType="desktop"
          itemListName={itemListName}
          test-id="PRODUCT_DETAILS.BUYBOX"
          productVariant={selectedVariants}
          shouldShowCallback={shouldShowCallback}
          shouldAddOfferToCart={shouldAddOfferToCart}
          offerAddedOnRedirection={offerAddedOnRedirection}
          setOfferAddedOnRedirection={setOfferAddedOnRedirection}
        />
        <StoreAvailability market={market} product={product} />
      </div>
    </div>
  )
}
