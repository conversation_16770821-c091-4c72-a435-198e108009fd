// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`pdp layout should render pdp layout successfully 1`] = `
<div>
  <div
    class="flex flex-col lg:flex-row justify-between"
  >
    <div
      class="flex flex-col lg:max-w-[61%] xl:max-w-[67%] max-h-[67%] lg:mr-6 lg:[flex:61] xl:[flex:67]"
    >
      <div
        class="flex flex-col lg-lgXl:flex-col w-full bg-white-main xs-mdMinus:px-4 py-4 lgXl:p-4 px-3 lg-xlMinus:pb-4 md:flex-row md:flex-nowrap gap-0 md:gap-10 lg-lgXl:gap-2 lgXl:gap-8"
      >
        <div
          class="md:flex-[1_1_100%] md:max-w-[fit-content] lg-lgXl:max-w-[100%]"
        >
          <div
            class="block sticky top-3 z-[5]"
          >
            <div
              class="md:hidden swiper-container image-carousel overflow-hidden relative max-w-100% swiper-container-initialized swiper-container-horizontal"
            >
              <div
                class="swiper-wrapper flex items-center"
                id="product-image-mobile"
                style="transition-duration: 300ms; transform: translate3d(NaNpx, 0px, 0px);"
              >
                <button
                  class="swiper-slide !w-full !flex !w-max-100% justify-center items-center"
                >
                  <img
                    alt="mobile_image"
                    src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510"
                  />
                </button>
                <button
                  class="swiper-slide !w-full !flex !w-max-100% justify-center items-center"
                >
                  <img
                    alt="mobile_image"
                    src="https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7"
                  />
                </button>
              </div>
            </div>
            <div
              class="bottom-0 mb-2 text-center md:hidden"
              data-testid="carousel-bullets"
            >
              <button
                aria-label="Slide 1"
                class="swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 swiper-pagination-bullet-active !bg-blue-main"
              />
              <button
                aria-label="Slide 2"
                class="swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 "
              />
            </div>
            <div
              class="hidden md:flex box-border place-content-stretch justify-center items-stretch relative mb-6 xl:mb-2"
              data-testid="desktop-carousel-image"
            >
              <div
                class="flex flex-col"
              >
                <div
                  class="hidden mr-3 md:flex md:flex-col xl:h-[443px] lg:h-[275px] md:h-[365px] lg-lgXl:max-h-[375px] lg-lgXl:h-full"
                >
                  <button
                    aria-label="carousel arrow up"
                    class="!flex flex-row flex-nowrap justify-center items-center z-[5] cursor-pointer my-auto mx-0 w-52px text-blue-shade-60 h-[48px] text-metro-blue-tint-80 pointer-events-none cursor-default absolute top-0 bg-white-main transition-all !duration-500 invisible !z-0 opacity-0"
                    data-testid="swiper-button-p"
                  >
                    <svg
                      aria-hidden="true"
                      class="block align-middle"
                      data-testid=""
                      fill=""
                      height="24px"
                      viewBox="0 0 32 32"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <path
                        d="M8.00029795,19.99975 C7.74029795,19.99975 7.48129795,19.89975 7.28629795,19.69975 C6.89929795,19.30575 6.90529795,18.67175 7.30029795,18.28575 L15.464298,10.28575 C15.853298,9.90475 16.476298,9.90475 16.864298,10.28575 L24.700298,17.96475 C25.095298,18.35175 25.101298,18.98475 24.714298,19.37975 C24.327298,19.77375 23.695298,19.77875 23.300298,19.39375 L16.164298,12.39975 L8.70029795,19.71375 C8.50529795,19.90475 8.25329795,19.99975 8.00029795,19.99975"
                        fill="#002D72"
                        stroke="#002D72"
                      />
                    </svg>
                  </button>
                  <div
                    class="swiper-container flex image-carousel overflow-hidden relative max-h-100% swiper-container-initialized swiper-container-vertical"
                  >
                    <div
                      class="swiper-wrapper flex flex-col"
                      style="transition-duration: 300ms; transform: translate3d(0px, NaNpx, 0px);"
                    >
                      <button
                        aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.PREVIEW.ARIA_LABEL"
                        class="swiper-slide my-0 mb-6px last:mb-0 border-metro-tint-80 box-border p-4px border rounded-sm md:!w-[50px] md:!h-[50px] border-2 border-metro-blue-tint-20"
                      >
                        <img
                          alt="thumbnail_image"
                          src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510?h=80&ixlib=php-2.3.0&q=100&w=80"
                        />
                      </button>
                      <button
                        aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.PREVIEW.ARIA_LABEL"
                        class="swiper-slide my-0 mb-6px last:mb-0 border-metro-tint-80 box-border p-4px border rounded-sm md:!w-[50px] md:!h-[50px]"
                      >
                        <img
                          alt="thumbnail_image"
                          src="https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7?h=80&ixlib=php-2.3.0&q=100&w=80"
                        />
                      </button>
                    </div>
                  </div>
                  <button
                    aria-label="carousel arrow down"
                    class="!flex flex-row flex-nowrap justify-center items-center z-[5] cursor-pointer my-auto mx-0 w-52px text-blue-shade-60 h-[48px] text-metro-blue-tint-80 pointer-events-none cursor-default absolute bg-white-main transition-all !duration-500 bottom-0 invisible !z-0 opacity-0"
                    data-testid="swiper-button-n"
                  >
                    <svg
                      aria-hidden="true"
                      class="block align-middle"
                      data-testid=""
                      fill=""
                      height="24px"
                      viewBox="0 0 32 32"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <path
                        d="M24.000298,12 C24.260298,12 24.519298,12.1 24.714298,12.3 C25.101298,12.694 25.095298,13.328 24.700298,13.714 L16.536298,21.714 C16.147298,22.095 15.524298,22.095 15.136298,21.714 L7.30029795,14.035 C6.90529795,13.648 6.89929795,13.015 7.28629795,12.62 C7.67329795,12.226 8.30529795,12.221 8.70029795,12.606 L15.836298,19.6 L23.300298,12.286 C23.495298,12.095 23.747298,12 24.000298,12"
                        fill="#002D72"
                        stroke="#002D72"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div
                class="flex flex-col w-full"
              >
                <div
                  class="flex justify-center md:h-[365px] md:w-[365px] lg:h-[275px] lg:w-[275px] xl:h-[393px] xl:w-[393px] lg-lgXl:w-full lg-lgXl:h-full lg-lgXl:max-h-[375px]"
                >
                  <button
                    aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.MAIN_IMAGE.ZOOM.ARIA_LABEL"
                    class="relative flex justify-center place-content-center items-center w-full h-full"
                    data-testid="desktop-carousel-image-modal-link"
                  >
                    <img
                      alt="METRO Professional Teller tief Madleen, Steinzeug, Ø 21 cm, grau, 6 Stück"
                      src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510"
                    />
                  </button>
                </div>
                <div
                  class="xs:hidden xl:flex content-center justify-center text-grey-tint-40 text-base mb-2"
                >
                  CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.ROLL_OVER.TEXT
                </div>
              </div>
            </div>
            <div
              class="md-xxxl:ml-16"
            />
          </div>
        </div>
        <div
          class="md-xxxl:w-[30%] lg-lgXl:w-full flex flex-col gap-2 md:flex-[1_1_100%]"
        >
          <div
            class="md-xxxl:w-full"
          >
            <div
              class="prose md:overflow-y-hidden"
            >
              <h1
                class="text-blue-shade-60 font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 mb-0 subpixel-antialiased [overflow-wrap:anywhere]"
                test-target="PRODUCT_TITLE.MOBILE"
              >
                METRO Professional Teller tief Madleen, Steinzeug, Ø 21 cm, grau, 6 Stück
              </h1>
              <span
                class="group/product-mid inline-flex relative text-grey-tint-40 text-sm cursor-pointer"
              >
                CATALOG.DETAILS_PAGE.PRODUCT_ID.LABEL
                : 
                AAA0000496661
                <div
                  class="relative"
                  id="clipboard"
                >
                  <svg
                    aria-hidden="true"
                    class="text-grey-tint-40 group-hover/product-mid:text-blue-main group-[.is-opened]/product-mid:text-blue-main"
                    data-testid=""
                    fill=""
                    height="20px"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <svg
                      fill="currentColor"
                      height="20"
                      stroke="none"
                      viewBox="0 0 20 20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 6H5V16H12V6ZM13 14H15V4H8V5H12C12.2652 5 12.5196 5.10536 12.7071 5.29289C12.8946 5.48043 13 5.73478 13 6V14ZM13 16C13 16.2652 12.8946 16.5196 12.7071 16.7071C12.5196 16.8946 12.2652 17 12 17H5C4.73478 17 4.48043 16.8946 4.29289 16.7071C4.10536 16.5196 4 16.2652 4 16V6C4 5.73478 4.10536 5.48043 4.29289 5.29289C4.48043 5.10536 4.73478 5 5 5H7V4C7 3.73478 7.10536 3.48043 7.29289 3.29289C7.48043 3.10536 7.73478 3 8 3H15C15.2652 3 15.5196 3.10536 15.7071 3.29289C15.8946 3.48043 16 3.73478 16 4V14C16 14.2652 15.8946 14.5196 15.7071 14.7071C15.5196 14.8946 15.2652 15 15 15H13V16Z"
                        fill="currentColor"
                        stroke="none"
                      />
                    </svg>
                  </svg>
                </div>
              </span>
              <div
                class="relative flex flex-col md:mb-[16px] pt-[12px] xs:pb-[16px] md:pb-0 gap-[8px]"
                data-testid="variants"
              >
                <div
                  class="flex flex-col justify-start text-grey-main"
                  data-testid="variant_group"
                >
                  <div
                    class="flex flex-row text-metro-blue-main text-[14px]"
                  >
                    <span>
                      Tamaño Gastronorm
                      : 
                    </span>
                    <span
                      class="font-bold"
                    >
                      1/3
                    </span>
                  </div>
                  <div
                    class="flex flex-row xs:overflow-x-scroll md:overflow-visible md:flex-wrap gap-[10px] pt-[4px]"
                  >
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/148e25f1-2597-4213-80e2-a2d597e80efb"
                      id="gn_size-148e25f1-2597-4213-80e2-a2d597e80efb"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                      >
                        1/1
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                          href="https://marketplace-test.metro.de/marktplatz/product/148e25f1-2597-4213-80e2-a2d597e80efb"
                        >
                          CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border-2 border-metro-blue-tint-20 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                      id="gn_size-989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        1/3
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                        >
                          59,11 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/bcfacd90-6763-4a92-9f90-01090b23d311"
                      id="gn_size-bcfacd90-6763-4a92-9f90-01090b23d311"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        1/4
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/bcfacd90-6763-4a92-9f90-01090b23d311"
                        >
                          56,65 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/507492c3-6b8c-414d-b854-7561603aab55"
                      id="gn_size-507492c3-6b8c-414d-b854-7561603aab55"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        1/6
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/507492c3-6b8c-414d-b854-7561603aab55"
                        >
                          45,02 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/d43c3ecb-82cf-4ea1-8fcf-8b17293bf5b6"
                      id="gn_size-d43c3ecb-82cf-4ea1-8fcf-8b17293bf5b6"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        1/9
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/d43c3ecb-82cf-4ea1-8fcf-8b17293bf5b6"
                        >
                          31,55 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/050e1742-0419-4642-8b63-ebbe41402bc4"
                      id="gn_size-050e1742-0419-4642-8b63-ebbe41402bc4"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        2/1
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/050e1742-0419-4642-8b63-ebbe41402bc4"
                        >
                          265,44 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/b1c5a512-429d-46cf-8145-845e0a6732fe"
                      id="gn_size-b1c5a512-429d-46cf-8145-845e0a6732fe"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        2/4
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/b1c5a512-429d-46cf-8145-845e0a6732fe"
                        >
                          112,38 €
                        </a>
                      </div>
                    </a>
                  </div>
                </div>
                <div
                  class="flex flex-col justify-start text-grey-main"
                  data-testid="variant_group"
                >
                  <div
                    class="flex flex-row text-metro-blue-main text-[14px]"
                  >
                    <span>
                      Profundidad Gastronorm
                      : 
                    </span>
                    <span
                      class="font-bold"
                    >
                      65 mm
                    </span>
                  </div>
                  <div
                    class="flex flex-row xs:overflow-x-scroll md:overflow-visible md:flex-wrap gap-[10px] pt-[4px]"
                  >
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/8ab19111-4e97-4869-ad9b-736497fef5c1"
                      id="gn_depth-8ab19111-4e97-4869-ad9b-736497fef5c1"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                      >
                        20 mm
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                          href="https://marketplace-test.metro.de/marktplatz/product/8ab19111-4e97-4869-ad9b-736497fef5c1"
                        >
                          CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/148e25f1-2597-4213-80e2-a2d597e80efb"
                      id="gn_depth-148e25f1-2597-4213-80e2-a2d597e80efb"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                      >
                        40 mm
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                          href="https://marketplace-test.metro.de/marktplatz/product/148e25f1-2597-4213-80e2-a2d597e80efb"
                        >
                          CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border-2 border-metro-blue-tint-20 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                      id="gn_depth-989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                      >
                        65 mm
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                          href="https://marketplace-test.metro.de/marktplatz/product/989c0444-62c2-4a71-a3d9-6043ea6b82bd"
                        >
                          59,11 €
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/523f862e-244e-40c4-b8ab-620d7f4d5ec6"
                      id="gn_depth-523f862e-244e-40c4-b8ab-620d7f4d5ec6"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                      >
                        100 mm
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                          href="https://marketplace-test.metro.de/marktplatz/product/523f862e-244e-40c4-b8ab-620d7f4d5ec6"
                        >
                          CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                        </a>
                      </div>
                    </a>
                    <a
                      class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                      data-testid="variant_item"
                      href="https://marketplace-test.metro.de/marktplatz/product/5d7547be-8d56-4728-a43c-507e591a776d"
                      id="gn_depth-5d7547be-8d56-4728-a43c-507e591a776d"
                    >
                      <span
                        class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                      >
                        150 mm
                      </span>
                      <div
                        class="
    flex
    md:hidden"
                      >
                        <a
                          class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                          href="https://marketplace-test.metro.de/marktplatz/product/5d7547be-8d56-4728-a43c-507e591a776d"
                        >
                          CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                        </a>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="hidden md:block lg:grid lg:grid-cols-2 lg:gap-6 lgXl:block"
              >
                <div
                  class=""
                >
                  <ul
                    class="ml-2 pl-3 mb-0 list-outside list-disc not-prose marker:"
                    test-target="product-artificial-highlights"
                  >
                    <li
                      class="text-base my-0 ml-[5px] pl-0 leading-6"
                      data-testid="highlights-brand"
                    >
                      <span
                        class="text-blue-shade-60"
                      >
                        CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND
                      </span>
                      : 
                      <a
                        class="text-secondary-main hover:underline"
                        href="https://marketplace-test.metro.de/marktplatz/b/metro-professional"
                        rel="noreferrer"
                        target="_blank"
                      >
                        METRO Professional
                      </a>
                    </li>
                    <li
                      class="text-base my-0 ml-[5px] pl-0 leading-6"
                    >
                      <div
                        class="text-ellipsis text-blue-shade-60"
                      >
                        Hergestellt aus hochwertigem Steinzeug
                      </div>
                    </li>
                    <li
                      class="text-base my-0 ml-[5px] pl-0 leading-6"
                    >
                      <div
                        class="text-ellipsis text-blue-shade-60"
                      >
                        Mikrowellen- und Spülmaschinengeeignet
                      </div>
                    </li>
                    <li
                      class="text-base my-0 ml-[5px] pl-0 leading-6"
                    >
                      <div
                        class="text-ellipsis text-blue-shade-60"
                      >
                        Stapelbar
                      </div>
                    </li>
                    <li
                      class="text-base my-0 ml-[5px] pl-0 leading-6"
                    >
                      <div
                        class="text-ellipsis text-blue-shade-60"
                      >
                        Sehr buntes und trendiges sortiment
                      </div>
                    </li>
                  </ul>
                  <div
                    class="sticky bottom-0 pt-[10px]"
                  >
                    <a
                      class="block text-secondary-main cursor-pointer hover:underline text-base text-left"
                      test-target="product-highlights-read-more"
                    >
                      CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex flex-col lg:hidden"
        test-target="other-offers-pdp-link-mobile"
      >
        <div
          class="w-full h-full"
        >
          <div
            class="self-start lg:sticky top-[15px]"
          >
            <div
              class="w-full"
              id="buybox-mobile"
            >
              <div
                class="mt-6 md:mt-6 lg:mt-0 bg-white-main"
              >
                <div
                  class="flex flex-col flex-nowrap justify-between items-start lgPlus:p-6 p-4 pb-6 leading-[1.43] font-[900] [text-decoration:none]"
                >
                  <div
                    class="!m-0 pt-0 w-full"
                    test-target="buybox-container"
                  >
                    <div
                      class="flex flex-col gap-6"
                    >
                      <div
                        class="flex flex-col gap-4"
                      >
                        <div
                          class="flex flex-col w-full gap-3 items-end"
                        >
                          <div
                            class="w-full flex justify-between items-start pb-3 border-b border-grey-tint-90"
                            data-testid="pricebox-unitprice"
                          >
                            <span
                              class="text-base leading-6 font-normal text-metro-blue-tint-20"
                            >
                              CATALOG.DETAILS_PAGE.OFFERS_BOX.UNIT_PRICE.LABEL
                            </span>
                            <span
                              class="text-[20px] leading-6 font-black text-metro-blue-main"
                            >
                              16,00 €
                            </span>
                          </div>
                          <div
                            class="w-full flex justify-between"
                          >
                            <span
                              class="text-base leading-4 font-normal text-metro-blue-tint-20"
                            >
                              CATALOG.DETAILS_PAGE.OFFERS_BOX.PACK_PRICE.LABEL
                            </span>
                            <div
                              class="flex flex-col gap-1 items-end"
                            >
                              <div
                                class="flex items-center gap-6px"
                              >
                                <button
                                  class="flex p-0 font-black text-metro-blue-main text-2xl leading-7"
                                  test-target="price-box__total"
                                >
                                  96,00 €
                                </button>
                              </div>
                              <p
                                class="m-0 text-metro-blue-tint-20 text-base font-normal leading-6"
                                test-target="PRICE_BOX_VAT"
                              >
                                CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.VAT
                              </p>
                            </div>
                          </div>
                        </div>
                        <div
                          class="flex flex-col gap-4"
                        >
                          <div
                            class="flex flex-col md-lg:items-center md-lg:flex-row xl:flex-row xl:flex-nowrap justify-between xl:items-center relative"
                            id="add-to-cart-wrapper-mobile"
                          >
                            <div
                              class="flex flex-col space-y-2 w-full"
                            >
                              <div
                                class="flex gap-4"
                              >
                                <div
                                  class="relative !m-0 rounded-md flex-row flex-nowrap items-stretch !h-[48px] border-grey-tint-80 hover:border-secondary-main border-[1px] border-solid flex items-center prose font-bold text-metro-blue-main md:w-auto mb-4 smd:mb-0 !w-[118px] md:!flex-[1_1_0%] md-lgMinus:!w-auto"
                                  data-testid="quantity-picker"
                                  id="quantity-picker-mobile"
                                  test-target="quantity-picker--desktop"
                                >
                                  <button
                                    aria-label="QUANTITY_PICKER.BUTTON.DECREASE_QUANTITY"
                                    class="w-13 bg-white-main hover:bg-blue-tint-95 cursor-pointer rounded-tl-md rounded-bl-md border-none text-secondary-main flex justify-center items-center !p-0 !w-[40px] !h-full max-w-[40px] disabled:!bg-white-main disabled:!bg-none disabled:!text-grey-tint-60   cursor-not-allowed !text-grey-tint-40"
                                    data-testid="decrement"
                                    disabled=""
                                    test-target="picker-decrease-button"
                                    type="button"
                                  >
                                    <svg
                                      aria-hidden="true"
                                      class="w-[19px] h-[19px]"
                                      data-testid=""
                                      fill=""
                                      height="100%"
                                      viewBox="0 0 32 32"
                                      width="1px"
                                      xmlns="http://www.w3.org/2000/svg"
                                      xmlns:xlink="http://www.w3.org/1999/xlink"
                                    >
                                      <svg
                                        fill="none"
                                        height="32"
                                        viewBox="0 0 32 32"
                                        width="32"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          clip-rule="evenodd"
                                          d="M27 17H5C4.447 17 4 16.552 4 16C4 15.448 4.447 15 5 15H27C27.553 15 28 15.448 28 16C28 16.552 27.553 17 27 17Z"
                                          fill="currentColor"
                                          fill-rule="evenodd"
                                        />
                                      </svg>
                                    </svg>
                                  </button>
                                  <input
                                    aria-label="CATALOG.DETAILS_PAGE.BUYBOX.QUANTITY_PICKER.ARIA_LABEL"
                                    class="flex-1 border-x-0 text-center border-none text-primary-main font-normal !h-[auto] !w-[calc(100%-80px)] !p-0"
                                    data-testid="quantity"
                                    pattern="[0-9]*"
                                    test-target="picker-input"
                                    type="text"
                                    value="1"
                                  />
                                  <button
                                    aria-label="QUANTITY_PICKER.BUTTON.INCREASE_QUANTITY"
                                    class="w-13 bg-white-main p-15px hover:bg-blue-tint-95 rounded-tr-md rounded-br-md border-none text-secondary-main flex justify-center items-center !p-0 !w-[40px] !h-full max-w-[40px] disabled:!bg-white-main disabled:!bg-none disabled:!text-grey-tint-60"
                                    data-testid="increment"
                                    test-target="picker-increase-button"
                                    type="button"
                                  >
                                    <svg
                                      aria-hidden="true"
                                      class="w-[19px] h-[19px]"
                                      data-testid=""
                                      fill=""
                                      height="100%"
                                      viewBox="0 0 32 32"
                                      width="1px"
                                      xmlns="http://www.w3.org/2000/svg"
                                      xmlns:xlink="http://www.w3.org/1999/xlink"
                                    >
                                      <svg
                                        fill="none"
                                        height="32"
                                        viewBox="0 0 32 32"
                                        width="32"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          clip-rule="evenodd"
                                          d="M28 16C28 16.552 27.553 17 27 17H17V27C17 27.552 16.553 28 16 28C15.447 28 15 27.552 15 27V17H5C4.447 17 4 16.552 4 16C4 15.448 4.447 15 5 15H15V5C15 4.448 15.447 4 16 4C16.553 4 17 4.448 17 5V15H27C27.553 15 28 15.448 28 16Z"
                                          fill="currentColor"
                                          fill-rule="evenodd"
                                        />
                                      </svg>
                                    </svg>
                                  </button>
                                </div>
                                <div
                                  aria-atomic="true"
                                  aria-live="polite"
                                  class="sr-only"
                                  data-testid="quantity-update"
                                  id="quantity-update-mobile"
                                  role="status"
                                />
                                <button
                                  aria-live="polite"
                                  class="rounded-sm transition px-8 py-2 text-lg bg-secondary-main hover:bg-blue-shade-20 focus:ring-2 focus:ring-blue-share-20 focus:ring-opacity-50 text-white-main transition cursor-wait flex justify-center items-center !rounded-md flex grow md:flex-[2_2_0%] lg:flex items-center justify-center p-0 mt-0 h-12 min-h-12 text-regular lg:text-lg shadow-none disabled:pointer-events-none"
                                  data-testid="addToCart"
                                  role="button"
                                  test-target="add-to-cart-button-mobile"
                                  type="button"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="animate-rotate"
                                    data-testid="button_loader_icon"
                                    fill=""
                                    height="21"
                                    viewBox="0 0 42 42"
                                    width="21"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                  >
                                    <circle
                                      class="animate-dash"
                                      cx="50%"
                                      cy="50%"
                                      fill="none"
                                      id="loader-circle"
                                      r="20"
                                      stroke="#f2f7ff"
                                      stroke-linecap="round"
                                      stroke-width="2px"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="w-full text-base "
                        data-testid="VOLUME_PRICE_TABLE"
                        test-target="VOLUME_PRICE_TABLE"
                      >
                        <ul
                          class="p-0 m-0 list-none text-base leading-6"
                        >
                          <li
                            class="m-0 p-0 mb-2"
                          >
                            <div
                              class="flex justify-between items-center no-underline text-metro-blue-main font-semibold"
                            >
                              <span
                                class="w-[40%] lg:w-[43%] text-left"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.BUY.LABEL
                              </span>
                              <span
                                class="flex justify-end w-1/4 gap-1.5"
                              >
                                <span>
                                  CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.PRICE.LABEL
                                </span>
                                <div
                                  class="relative"
                                >
                                  <div
                                    aria-describedby=""
                                    aria-label="More information"
                                    data-testid="on_click_and_hover"
                                    role="button"
                                    tabindex="0"
                                  >
                                    <div
                                      class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-4 !h-4 cursor-default before:font-light border-4 border-white-main box-content"
                                    />
                                  </div>
                                </div>
                              </span>
                              <span
                                class="text-right border-none w-[35%]"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.SAVINGS.LABEL
                              </span>
                            </div>
                          </li>
                          <li
                            class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90 FIRST_MOBILE FIRST_DESKTOP"
                            data-testid="VOLUME_PRICE_TABLE_ITEM"
                          >
                            <a
                              class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                              data-testid="tableItemLink"
                              tabindex="0"
                            >
                              <span
                                class="text-left w-[40%]"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                                 
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                                 
                              </span>
                              <span
                                class="text-right w-1/4"
                              >
                                81,60 €
                              </span>
                              <span
                                class="text-right border-none w-[35%] text-metro-red"
                              >
                                - 
                                15
                                 %
                              </span>
                            </a>
                          </li>
                          <li
                            class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90  "
                            data-testid="VOLUME_PRICE_TABLE_ITEM"
                          >
                            <a
                              class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                              data-testid="tableItemLink"
                              tabindex="0"
                            >
                              <span
                                class="text-left w-[40%]"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                                 
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                                 
                              </span>
                              <span
                                class="text-right w-1/4"
                              >
                                86,40 €
                              </span>
                              <span
                                class="text-right border-none w-[35%] text-metro-red"
                              >
                                - 
                                10
                                 %
                              </span>
                            </a>
                          </li>
                          <li
                            class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90  "
                            data-testid="VOLUME_PRICE_TABLE_ITEM"
                          >
                            <a
                              class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                              data-testid="tableItemLink"
                              tabindex="0"
                            >
                              <span
                                class="text-left w-[40%]"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                                 
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                                 
                              </span>
                              <span
                                class="text-right w-1/4"
                              >
                                91,20 €
                              </span>
                              <span
                                class="text-right border-none w-[35%] text-metro-red"
                              >
                                - 
                                5
                                 %
                              </span>
                            </a>
                          </li>
                          <li
                            class="m-0 px-4 py-1 border-b-[1px] text-metro-blue-main hover:drop-shadow-md
            hover:shadow-grey-main !border-metro-blue-main !border-[1.5px] rounded-md DEFAULT_MOBILE DEFAULT_DESKTOP"
                            data-testid="VOLUME_PRICE_TABLE_ITEM"
                          >
                            <a
                              class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                              data-testid="tableItemLink"
                            >
                              <span
                                class="text-left w-[40%]"
                              >
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                              </span>
                              <span
                                class="text-right w-1/4 font-semibold text-metro-blue-main"
                              >
                                96,00 €
                              </span>
                              <span
                                class="text-right px-[11px] py-[5px] border-none w-[35%]"
                              />
                            </a>
                          </li>
                        </ul>
                      </div>
                      <div
                        class="flex w-full flex-row justify-between items-center rounded-sm"
                        data-testid="shipping-info"
                      >
                        <div
                          class="w-full"
                        >
                          <div
                            class="flex flex-row"
                          >
                            <div
                              class="box-border"
                            >
                              <svg
                                aria-hidden="true"
                                class="mt-2px mr-2 object-contain"
                                data-testid=""
                                fill=""
                                height="20px"
                                viewBox="0 0 20 21"
                                width="20px"
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                              >
                                <svg
                                  fill="#002D72"
                                  height="21"
                                  stroke="none"
                                  viewBox="0 0 20 21"
                                  width="20"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    clip-rule="evenodd"
                                    d="M12.0382 3.62506H0.625122C0.280122 3.62506 0.00012207 3.90506 0.00012207 4.25006V15.6632C0.00012207 16.0082 0.280122 16.2882 0.625122 16.2882H2.137C2.41075 17.2263 3.26887 17.9182 4.29325 17.9182C5.31887 17.9182 6.177 17.2263 6.45075 16.2882H13.5501C13.8232 17.2263 14.6814 17.9182 15.707 17.9182C16.732 17.9182 17.5901 17.2263 17.8632 16.2882H19.3751C19.7207 16.2882 20.0001 16.0082 20.0001 15.6632V11.1794C20.0001 11.0138 19.9345 10.8544 19.817 10.7376L18.2489 9.16881L16.2532 5.57631C16.1432 5.37819 15.9339 5.25506 15.707 5.25506H12.6632V4.25006C12.6632 3.90506 12.3839 3.62506 12.0382 3.62506ZM2.137 15.0382H1.25012V4.87506H11.4132V15.0382H6.45075C6.177 14.0994 5.31887 13.4076 4.29325 13.4076C3.26887 13.4076 2.41075 14.0994 2.137 15.0382ZM4.29325 16.6682C4.84825 16.6682 5.2995 16.2169 5.2995 15.6632C5.2995 15.1082 4.84825 14.6576 4.29325 14.6576C3.7395 14.6576 3.28825 15.1082 3.28825 15.6632C3.28825 16.2169 3.7395 16.6682 4.29325 16.6682ZM16.712 15.6632C16.712 16.2169 16.2614 16.6682 15.707 16.6682C15.152 16.6682 14.7014 16.2169 14.7014 15.6632C14.7014 15.1082 15.152 14.6576 15.707 14.6576C16.2614 14.6576 16.712 15.1082 16.712 15.6632ZM18.7501 11.4382L17.3032 9.99069C17.262 9.94944 17.227 9.90319 17.1989 9.85256L15.3389 6.50506H12.6632V15.0382H13.5501C13.8232 14.0994 14.6814 13.4076 15.707 13.4076C16.732 13.4076 17.5901 14.0994 17.8632 15.0382H18.7501V11.4382ZM16.3939 10.5157C16.7026 10.3613 16.8276 9.98569 16.6732 9.67694L15.4507 7.23131C15.3451 7.01944 15.1282 6.88569 14.8914 6.88569H13.6682C13.3232 6.88569 13.0432 7.16569 13.0432 7.51069C13.0432 7.85569 13.3232 8.13569 13.6682 8.13569H14.5051L15.5551 10.2363C15.6645 10.4551 15.8851 10.5813 16.1145 10.5813C16.2089 10.5813 16.3039 10.5607 16.3939 10.5157Z"
                                    fill="#002D72"
                                    fill-rule="evenodd"
                                    stroke="none"
                                  />
                                </svg>
                              </svg>
                            </div>
                            <span
                              class="w-full"
                            >
                              <div
                                class="flex flex-col"
                              >
                                <div
                                  class="flex justify-between md:justify-start lg:justify-between"
                                >
                                  <div
                                    class="max-w-[60%] flex flex-col"
                                  >
                                    <span
                                      class="text-base text-primary-main leading-[21px] font-normal font-bold"
                                    >
                                      <b>
                                        Mo., 28. Apr.
                                         - 
                                        Mi., 30. Apr.
                                      </b>
                                    </span>
                                  </div>
                                  <div
                                    class="flex flex-col"
                                  >
                                    <div
                                      class="flex px-2 py-1 rounded-sm text-black font-bold leading-[13px] text-[13px] bg-[linear-gradient(93deg,#80E764,#64E7C4)] md-lgMinus:ml-2"
                                      data-testid="defaultFreeShipping"
                                      test-target="dynamic-shipping-price"
                                    >
                                      CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.NEW.LAYOUT.DEFAULT.SHIPPING
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </span>
                          </div>
                          <div
                            class="flex pt-6px"
                          >
                            <div
                              class="pl-[28px] flex flex-col"
                            >
                              <span
                                class="inline-flex w-fit"
                              >
                                <span
                                  class="text-base leading-[21px] text-primary-main font-normal text-left align-middle cursor-pointer"
                                  test-target="dynamic-shipping-label"
                                >
                                  CATALOG.DETAILS_PAGE.SHIPPING.PARCEL.DESCRIPTION
                                  <span
                                    class="whitespace-nowrap"
                                  >
                                     
                                    <div
                                      class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-[14px] !h-[14px] inline-flex min-w-[14px] min-h-[14px] cursor-pointer before:font-bold bg-white-main border-primary-main border-[1px] box-content"
                                      data-testid="delivery-option-info"
                                    />
                                  </span>
                                </span>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex flex-col bg-white-main border-none md-lg:mt-3 !m-0 pt-0 lgPlus:px-6 px-4 pb-4 lgPlus:pb-6 gap-6"
              >
                <div
                  class="flex"
                >
                  <div
                    class="box-border"
                  >
                    <svg
                      aria-hidden="true"
                      class="object-contain mt-2px mr-2 mb-[-1px]"
                      data-testid=""
                      fill=""
                      height="20px"
                      viewBox="0 0 20 20"
                      width="20px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <svg
                        fill="#002D72"
                        height="20"
                        stroke="none"
                        viewBox="0 0 20 20"
                        width="20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M19.3754 6.87494V18.0556C19.3754 18.4006 19.0954 18.6806 18.7504 18.6806C18.4047 18.6806 18.1254 18.4006 18.1254 18.0556V7.26119L10.0004 3.19869L1.87537 7.26119V18.0556C1.87537 18.4006 1.59537 18.6806 1.25037 18.6806C0.904741 18.6806 0.625366 18.4006 0.625366 18.0556V6.87494C0.625366 6.85674 0.630001 6.84008 0.63468 6.82327L0.634681 6.82327C0.637876 6.81179 0.641091 6.80023 0.642866 6.78806L0.645681 6.76691C0.650614 6.72902 0.655511 6.6914 0.669741 6.65556C0.680758 6.62551 0.697391 6.59988 0.714498 6.57352L0.727241 6.55369L0.73966 6.53282L0.739662 6.53282C0.756103 6.50493 0.772214 6.47759 0.794741 6.45306C0.820217 6.42546 0.850649 6.40462 0.88183 6.38327L0.881837 6.38327L0.898491 6.37181C0.908491 6.36481 0.917491 6.35681 0.926491 6.34881C0.939991 6.33681 0.953491 6.32481 0.970366 6.31619L9.72037 1.94119C9.89599 1.85306 10.1041 1.85306 10.2797 1.94119L19.0297 6.31619C19.0466 6.32481 19.0601 6.33681 19.0736 6.34881L19.0736 6.34882C19.0826 6.35682 19.0916 6.36481 19.1016 6.37181L19.1187 6.38357C19.1498 6.40482 19.1801 6.42559 19.206 6.45306C19.2278 6.47733 19.2437 6.50435 19.2599 6.53194L19.2599 6.53196L19.2729 6.55369L19.2856 6.57353C19.3027 6.59989 19.3193 6.62552 19.3304 6.65556C19.3446 6.6914 19.3495 6.72901 19.3544 6.76691L19.3572 6.78806C19.359 6.80038 19.3624 6.81206 19.3658 6.82368C19.3706 6.84035 19.3754 6.85688 19.3754 6.87494ZM16.4585 13.1943V18.0556C16.4585 18.4006 16.1785 18.6806 15.8335 18.6806H10.0004H4.16662C3.82162 18.6806 3.54162 18.4006 3.54162 18.0556V13.1943C3.54162 12.8493 3.82162 12.5693 4.16662 12.5693H6.45849V8.33306C6.45849 7.98806 6.73849 7.70806 7.08349 7.70806H12.9166C13.2616 7.70806 13.5416 7.98806 13.5416 8.33306V12.5693H15.8335C16.1785 12.5693 16.4585 12.8493 16.4585 13.1943ZM12.9166 13.8193H15.2085V17.4306H10.6254V13.8193H12.9166ZM9.37537 17.4306V13.8193H7.08349H4.79162V17.4306H9.37537ZM10.0004 12.5693H7.70849V8.95806H12.2916V12.5693H10.0004Z"
                          fill="#002D72"
                          fill-rule="evenodd"
                          stroke="none"
                        />
                      </svg>
                    </svg>
                  </div>
                  <span
                    class="leading-6"
                  >
                    <span
                      class="mt-[30px] p-0 text-base text-primary-main !leading-6"
                    >
                      <span>
                        CATALOG.DETAILS_PAGE.BUYBOX.SELLER.SOLD_BY
                        :
                         
                      </span>
                      <a
                        class="[text-decoration:none] text-secondary-main cursor-pointer hover:underline"
                        data-seller-id="e13514f4-18fa-479e-88d7-e79eb3e02ef2"
                        href="https://marketplace-test.metro.de/marktplatz/seller/e13514f4-18fa-479e-88d7-e79eb3e02ef2#about"
                        rel="noopener noreferrer"
                        target="_blank"
                        test-target="seller-name"
                      >
                        Nader Ikladious Shop
                      </a>
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex flex-col md:p-0 mt-6 gap-6 flex-col-reverse"
      >
        <div
          class="block p-4 md:hidden bg-white-main"
        >
          <h2
            class="font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 antialiased m-0 mb-6 text-blue-shade-60"
          >
            CATALOG.DETAILS_PAGE.ADDITIONAL_INFO_PANEL.KEY_FEATURES.TITLE
          </h2>
          <ul
            class="ml-2 pl-3 mb-0 list-outside list-disc not-prose marker:"
            test-target="product-artificial-highlights"
          >
            <li
              class="text-base my-0 ml-[5px] pl-0 leading-6"
              data-testid="highlights-brand"
            >
              <span
                class="text-blue-shade-60"
              >
                CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND
              </span>
              : 
              <a
                class="text-secondary-main hover:underline"
                href="https://marketplace-test.metro.de/marktplatz/b/metro-professional"
                rel="noreferrer"
                target="_blank"
              >
                METRO Professional
              </a>
            </li>
            <li
              class="text-base my-0 ml-[5px] pl-0 leading-6"
            >
              <div
                class="text-ellipsis text-blue-shade-60"
              >
                Hergestellt aus hochwertigem Steinzeug
              </div>
            </li>
            <li
              class="text-base my-0 ml-[5px] pl-0 leading-6"
            >
              <div
                class="text-ellipsis text-blue-shade-60"
              >
                Mikrowellen- und Spülmaschinengeeignet
              </div>
            </li>
            <li
              class="text-base my-0 ml-[5px] pl-0 leading-6"
            >
              <div
                class="text-ellipsis text-blue-shade-60"
              >
                Stapelbar
              </div>
            </li>
            <li
              class="text-base my-0 ml-[5px] pl-0 leading-6"
            >
              <div
                class="text-ellipsis text-blue-shade-60"
              >
                Sehr buntes und trendiges sortiment
              </div>
            </li>
          </ul>
          <div
            class="sticky bottom-0 pt-[10px]"
          >
            <a
              class="block text-secondary-main cursor-pointer hover:underline text-base text-left"
              test-target="product-highlights-read-more"
            >
              CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL
            </a>
          </div>
        </div>
        <div
          class="bg-white-main md:bg-transparent p-4 md:p-0"
        >
          <div
            class="not-prose bg-white-main md:bg-transparent"
          >
            <div
              class="flex items-center justify-between md:justify-start md:gap-2 leading-6 mb-2.5"
            >
              <h4
                class="text-regular leading-[26px] font-bold text-metro-blue-main"
              >
                CATALOG.DETAILS_PAGE.TABLEWARE.DESIGNED_FOR_PROFESSIONALS
              </h4>
              <a
                class="hover:underline text-base text-secondary-main inline-flex cursor-pointer block"
                data-testid="learn_more_about_usp"
              >
                CATALOG.DETAILS_PAGE.USPS.LEARN_MORE
              </a>
            </div>
            <div
              class="grid gap-3 grid-cols-3 lg:flex"
            >
              <div
                class="lg:min-w-[82px] lg:flex-[1_1_30%]"
              >
                <div
                  class="h-full transition-shadow bg-white-main flex flex-col rounded-lg p-3 lg:pb-4 justify-start items-center hover:shadow-[0_4px_20px_0px_rgba(70,90,110,0.1)_,_0_2px_0px_-2px_rgba(70,90,110,0.1)] cursor-pointer xs-mdMinus:border"
                  data-testid="int_rbg"
                >
                  <div>
                    <svg
                      aria-hidden="true"
                      class="mb-2"
                      data-testid=""
                      fill=""
                      height="48px"
                      viewBox="0 0 48 48"
                      width="48px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <circle
                        cx="24"
                        cy="24"
                        fill="#e5efff"
                        r="24"
                      />
                      <path
                        d="M21.66,21.66l-2.1-2.1a6.28,6.28,0,1,0,0,8.88l8.2-8.21.68-.67a6.28,6.28,0,1,1,0,8.88h0l-2.33-2.33"
                        fill="none"
                        stroke="#002d72"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                    </svg>
                  </div>
                  <div
                    class="w-full font-bold text-base leading-5 hyphens-auto text-primary-main text-center"
                    lang="de"
                  >
                    INT_rbg
                  </div>
                </div>
              </div>
              <div
                class="lg:min-w-[82px] lg:flex-[1_1_30%]"
              >
                <div
                  class="h-full transition-shadow bg-white-main flex flex-col rounded-lg p-3 lg:pb-4 justify-start items-center hover:shadow-[0_4px_20px_0px_rgba(70,90,110,0.1)_,_0_2px_0px_-2px_rgba(70,90,110,0.1)] cursor-pointer xs-mdMinus:border"
                  data-testid="dishwasher_safe"
                >
                  <div>
                    <svg
                      aria-hidden="true"
                      class="mb-2"
                      data-testid=""
                      fill=""
                      height="48px"
                      viewBox="0 0 48 48"
                      width="48px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <circle
                        cx="24"
                        cy="24"
                        fill="#e5efff"
                        r="24"
                      />
                      <path
                        d="M10.67,35.31H35.56V12.2H10.67Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M9.78,38.86H36.44V35.3H9.78Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M13.64,32.64H32.3V14.86H13.64Z"
                        fill="none"
                        stroke="#002d72"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M18.67,19.57a.54.54,0,0,1-.48.59.54.54,0,0,1-.48-.59,5.81,5.81,0,0,1,.48-1.33A5.81,5.81,0,0,1,18.67,19.57Z"
                        fill="none"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M23.47,19.57a.54.54,0,0,1-.48.59.54.54,0,0,1-.48-.59A5.81,5.81,0,0,1,23,18.24,5.81,5.81,0,0,1,23.47,19.57Z"
                        fill="none"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M28.27,19.57a.54.54,0,0,1-.48.59.54.54,0,0,1-.48-.59,5.81,5.81,0,0,1,.48-1.33A5.81,5.81,0,0,1,28.27,19.57Z"
                        fill="none"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M38.35,13.69a5,5,0,1,1-5-5A5,5,0,0,1,38.35,13.69Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M35.55,12.49l-2.64,2.64-1.76-1.76"
                        fill="none"
                        stroke="#002d72"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M26.48,23.11c1.58,0,2.77,1.6,2.77,3.56s-1.19,3.56-2.77,3.56Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M21.85,23.11c1.59,0,2.78,1.6,2.78,3.56s-1.19,3.56-2.78,3.56Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                      <path
                        d="M17.23,23.11c1.58,0,2.77,1.6,2.77,3.56s-1.19,3.56-2.77,3.56Z"
                        fill="#fff"
                        fill-rule="evenodd"
                        stroke="#002d72"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                      />
                    </svg>
                  </div>
                  <div
                    class="w-full font-bold text-base leading-5 hyphens-auto text-primary-main text-center"
                    lang="de"
                  >
                    Spülmaschinenfest
                  </div>
                </div>
              </div>
            </div>
            <a
              class="hover:underline text-base text-secondary-main inline-flex cursor-pointer hidden"
              data-testid="learn_more_about_usp"
            >
              CATALOG.DETAILS_PAGE.USPS.LEARN_MORE
            </a>
          </div>
          <span />
        </div>
      </div>
      <div
        class="flex animate-pulse flex-col h-[607px] bg-white-main rounded-[2px]  p-6 mt-6"
      />
      <div
        class="hidden bg-white-main p-6 mt-6"
      >
        <div
          data-testid="flix-inpage"
          id="flix-inpage"
        />
      </div>
    </div>
    <div
      class="hidden lg:flex flex-col lg:w-[39%] xl:w-[33%]"
      test-target="other-offers-pdp-link-desktop"
    >
      <div
        class="w-full h-full"
      >
        <div
          class="self-start lg:sticky top-[15px]"
        >
          <div
            class="w-full"
            id="buybox-desktop"
          >
            <div
              class="mt-6 md:mt-6 lg:mt-0 bg-white-main"
            >
              <div
                class="flex flex-col flex-nowrap justify-between items-start lgPlus:p-6 p-4 pb-6 leading-[1.43] font-[900] [text-decoration:none]"
              >
                <div
                  class="!m-0 pt-0 w-full"
                  test-target="buybox-container"
                >
                  <div
                    class="flex flex-col gap-6"
                  >
                    <div
                      class="flex flex-col gap-4"
                    >
                      <div
                        class="flex flex-col w-full gap-3 items-end"
                      >
                        <div
                          class="w-full flex justify-between items-start pb-3 border-b border-grey-tint-90"
                          data-testid="pricebox-unitprice"
                        >
                          <span
                            class="text-base leading-6 font-normal text-metro-blue-tint-20"
                          >
                            CATALOG.DETAILS_PAGE.OFFERS_BOX.UNIT_PRICE.LABEL
                          </span>
                          <span
                            class="text-[20px] leading-6 font-black text-metro-blue-main"
                          >
                            16,00 €
                          </span>
                        </div>
                        <div
                          class="w-full flex justify-between"
                        >
                          <span
                            class="text-base leading-4 font-normal text-metro-blue-tint-20"
                          >
                            CATALOG.DETAILS_PAGE.OFFERS_BOX.PACK_PRICE.LABEL
                          </span>
                          <div
                            class="flex flex-col gap-1 items-end"
                          >
                            <div
                              class="flex items-center gap-6px"
                            >
                              <button
                                class="flex p-0 font-black text-metro-blue-main text-2xl leading-7"
                                test-target="price-box__total"
                              >
                                96,00 €
                              </button>
                            </div>
                            <p
                              class="m-0 text-metro-blue-tint-20 text-base font-normal leading-6"
                              test-target="PRICE_BOX_VAT"
                            >
                              CATALOG.DETAILS_PAGE.BUYBOX.PRICE_BOX.VAT
                            </p>
                          </div>
                        </div>
                      </div>
                      <div
                        class="flex flex-col gap-4"
                      >
                        <div
                          class="flex flex-col md-lg:items-center md-lg:flex-row xl:flex-row xl:flex-nowrap justify-between xl:items-center relative"
                          id="add-to-cart-wrapper-desktop"
                        >
                          <div
                            class="flex flex-col space-y-2 w-full"
                          >
                            <div
                              class="flex gap-4"
                            >
                              <div
                                class="relative !m-0 rounded-md flex-row flex-nowrap items-stretch !h-[48px] border-grey-tint-80 hover:border-secondary-main border-[1px] border-solid flex items-center prose font-bold text-metro-blue-main md:w-auto mb-4 smd:mb-0 !w-[118px] md:!flex-[1_1_0%] md-lgMinus:!w-auto"
                                data-testid="quantity-picker"
                                id="quantity-picker-desktop"
                                test-target="quantity-picker--desktop"
                              >
                                <button
                                  aria-label="QUANTITY_PICKER.BUTTON.DECREASE_QUANTITY"
                                  class="w-13 bg-white-main hover:bg-blue-tint-95 cursor-pointer rounded-tl-md rounded-bl-md border-none text-secondary-main flex justify-center items-center !p-0 !w-[40px] !h-full max-w-[40px] disabled:!bg-white-main disabled:!bg-none disabled:!text-grey-tint-60   cursor-not-allowed !text-grey-tint-40"
                                  data-testid="decrement"
                                  disabled=""
                                  test-target="picker-decrease-button"
                                  type="button"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="w-[19px] h-[19px]"
                                    data-testid=""
                                    fill=""
                                    height="100%"
                                    viewBox="0 0 32 32"
                                    width="1px"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                  >
                                    <svg
                                      fill="none"
                                      height="32"
                                      viewBox="0 0 32 32"
                                      width="32"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        clip-rule="evenodd"
                                        d="M27 17H5C4.447 17 4 16.552 4 16C4 15.448 4.447 15 5 15H27C27.553 15 28 15.448 28 16C28 16.552 27.553 17 27 17Z"
                                        fill="currentColor"
                                        fill-rule="evenodd"
                                      />
                                    </svg>
                                  </svg>
                                </button>
                                <input
                                  aria-label="CATALOG.DETAILS_PAGE.BUYBOX.QUANTITY_PICKER.ARIA_LABEL"
                                  class="flex-1 border-x-0 text-center border-none text-primary-main font-normal !h-[auto] !w-[calc(100%-80px)] !p-0"
                                  data-testid="quantity"
                                  pattern="[0-9]*"
                                  test-target="picker-input"
                                  type="text"
                                  value="1"
                                />
                                <button
                                  aria-label="QUANTITY_PICKER.BUTTON.INCREASE_QUANTITY"
                                  class="w-13 bg-white-main p-15px hover:bg-blue-tint-95 rounded-tr-md rounded-br-md border-none text-secondary-main flex justify-center items-center !p-0 !w-[40px] !h-full max-w-[40px] disabled:!bg-white-main disabled:!bg-none disabled:!text-grey-tint-60"
                                  data-testid="increment"
                                  test-target="picker-increase-button"
                                  type="button"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="w-[19px] h-[19px]"
                                    data-testid=""
                                    fill=""
                                    height="100%"
                                    viewBox="0 0 32 32"
                                    width="1px"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                  >
                                    <svg
                                      fill="none"
                                      height="32"
                                      viewBox="0 0 32 32"
                                      width="32"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        clip-rule="evenodd"
                                        d="M28 16C28 16.552 27.553 17 27 17H17V27C17 27.552 16.553 28 16 28C15.447 28 15 27.552 15 27V17H5C4.447 17 4 16.552 4 16C4 15.448 4.447 15 5 15H15V5C15 4.448 15.447 4 16 4C16.553 4 17 4.448 17 5V15H27C27.553 15 28 15.448 28 16Z"
                                        fill="currentColor"
                                        fill-rule="evenodd"
                                      />
                                    </svg>
                                  </svg>
                                </button>
                              </div>
                              <div
                                aria-atomic="true"
                                aria-live="polite"
                                class="sr-only"
                                data-testid="quantity-update"
                                id="quantity-update-desktop"
                                role="status"
                              />
                              <button
                                aria-live="polite"
                                class="rounded-sm transition px-8 py-2 text-lg bg-secondary-main hover:bg-blue-shade-20 focus:ring-2 focus:ring-blue-share-20 focus:ring-opacity-50 text-white-main transition cursor-wait flex justify-center items-center !rounded-md flex grow md:flex-[2_2_0%] lg:flex items-center justify-center p-0 mt-0 h-12 min-h-12 text-regular lg:text-lg shadow-none disabled:pointer-events-none"
                                data-testid="addToCart"
                                role="button"
                                test-target="add-to-cart-button-desktop"
                                type="button"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="animate-rotate"
                                  data-testid="button_loader_icon"
                                  fill=""
                                  height="21"
                                  viewBox="0 0 42 42"
                                  width="21"
                                  xmlns="http://www.w3.org/2000/svg"
                                  xmlns:xlink="http://www.w3.org/1999/xlink"
                                >
                                  <circle
                                    class="animate-dash"
                                    cx="50%"
                                    cy="50%"
                                    fill="none"
                                    id="loader-circle"
                                    r="20"
                                    stroke="#f2f7ff"
                                    stroke-linecap="round"
                                    stroke-width="2px"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="w-full text-base "
                      data-testid="VOLUME_PRICE_TABLE"
                      test-target="VOLUME_PRICE_TABLE"
                    >
                      <ul
                        class="p-0 m-0 list-none text-base leading-6"
                      >
                        <li
                          class="m-0 p-0 mb-2"
                        >
                          <div
                            class="flex justify-between items-center no-underline text-metro-blue-main font-semibold"
                          >
                            <span
                              class="w-[40%] lg:w-[43%] text-left"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.BUY.LABEL
                            </span>
                            <span
                              class="flex justify-end w-1/4 gap-1.5"
                            >
                              <span>
                                CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.PRICE.LABEL
                              </span>
                              <div
                                class="relative"
                              >
                                <div
                                  aria-describedby=""
                                  aria-label="More information"
                                  data-testid="on_click_and_hover"
                                  role="button"
                                  tabindex="0"
                                >
                                  <div
                                    class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-4 !h-4 cursor-default before:font-light border-4 border-white-main box-content"
                                  />
                                </div>
                              </div>
                            </span>
                            <span
                              class="text-right border-none w-[35%]"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.HEADER.SAVINGS.LABEL
                            </span>
                          </div>
                        </li>
                        <li
                          class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90 FIRST_MOBILE FIRST_DESKTOP"
                          data-testid="VOLUME_PRICE_TABLE_ITEM"
                        >
                          <a
                            class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                            data-testid="tableItemLink"
                            tabindex="0"
                          >
                            <span
                              class="text-left w-[40%]"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                               
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                               
                            </span>
                            <span
                              class="text-right w-1/4"
                            >
                              81,60 €
                            </span>
                            <span
                              class="text-right border-none w-[35%] text-metro-red"
                            >
                              - 
                              15
                               %
                            </span>
                          </a>
                        </li>
                        <li
                          class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90  "
                          data-testid="VOLUME_PRICE_TABLE_ITEM"
                        >
                          <a
                            class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                            data-testid="tableItemLink"
                            tabindex="0"
                          >
                            <span
                              class="text-left w-[40%]"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                               
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                               
                            </span>
                            <span
                              class="text-right w-1/4"
                            >
                              86,40 €
                            </span>
                            <span
                              class="text-right border-none w-[35%] text-metro-red"
                            >
                              - 
                              10
                               %
                            </span>
                          </a>
                        </li>
                        <li
                          class="m-0 px-4 py-1 border-b-[1px]  font-normal hover:shadow-md hover:shadow-gray-main hover:border-grey-tint-90  "
                          data-testid="VOLUME_PRICE_TABLE_ITEM"
                        >
                          <a
                            class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                            data-testid="tableItemLink"
                            tabindex="0"
                          >
                            <span
                              class="text-left w-[40%]"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                               
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT_INTERVAL
                               
                            </span>
                            <span
                              class="text-right w-1/4"
                            >
                              91,20 €
                            </span>
                            <span
                              class="text-right border-none w-[35%] text-metro-red"
                            >
                              - 
                              5
                               %
                            </span>
                          </a>
                        </li>
                        <li
                          class="m-0 px-4 py-1 border-b-[1px] text-metro-blue-main hover:drop-shadow-md
            hover:shadow-grey-main !border-metro-blue-main !border-[1.5px] rounded-md DEFAULT_MOBILE DEFAULT_DESKTOP"
                          data-testid="VOLUME_PRICE_TABLE_ITEM"
                        >
                          <a
                            class="flex justify-between items-center no-underline cursor-pointer text-metro-blue-tint-20"
                            data-testid="tableItemLink"
                          >
                            <span
                              class="text-left w-[40%]"
                            >
                              CATALOG.DETAILS_PAGE.VOLUME_PRICE_TABLE.ITEMS_COUNT.LABEL_NEXT
                            </span>
                            <span
                              class="text-right w-1/4 font-semibold text-metro-blue-main"
                            >
                              96,00 €
                            </span>
                            <span
                              class="text-right px-[11px] py-[5px] border-none w-[35%]"
                            />
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="flex w-full flex-row justify-between items-center rounded-sm"
                      data-testid="shipping-info"
                    >
                      <div
                        class="w-full"
                      >
                        <div
                          class="flex flex-row"
                        >
                          <div
                            class="box-border"
                          >
                            <svg
                              aria-hidden="true"
                              class="mt-2px mr-2 object-contain"
                              data-testid=""
                              fill=""
                              height="20px"
                              viewBox="0 0 20 21"
                              width="20px"
                              xmlns="http://www.w3.org/2000/svg"
                              xmlns:xlink="http://www.w3.org/1999/xlink"
                            >
                              <svg
                                fill="#002D72"
                                height="21"
                                stroke="none"
                                viewBox="0 0 20 21"
                                width="20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  clip-rule="evenodd"
                                  d="M12.0382 3.62506H0.625122C0.280122 3.62506 0.00012207 3.90506 0.00012207 4.25006V15.6632C0.00012207 16.0082 0.280122 16.2882 0.625122 16.2882H2.137C2.41075 17.2263 3.26887 17.9182 4.29325 17.9182C5.31887 17.9182 6.177 17.2263 6.45075 16.2882H13.5501C13.8232 17.2263 14.6814 17.9182 15.707 17.9182C16.732 17.9182 17.5901 17.2263 17.8632 16.2882H19.3751C19.7207 16.2882 20.0001 16.0082 20.0001 15.6632V11.1794C20.0001 11.0138 19.9345 10.8544 19.817 10.7376L18.2489 9.16881L16.2532 5.57631C16.1432 5.37819 15.9339 5.25506 15.707 5.25506H12.6632V4.25006C12.6632 3.90506 12.3839 3.62506 12.0382 3.62506ZM2.137 15.0382H1.25012V4.87506H11.4132V15.0382H6.45075C6.177 14.0994 5.31887 13.4076 4.29325 13.4076C3.26887 13.4076 2.41075 14.0994 2.137 15.0382ZM4.29325 16.6682C4.84825 16.6682 5.2995 16.2169 5.2995 15.6632C5.2995 15.1082 4.84825 14.6576 4.29325 14.6576C3.7395 14.6576 3.28825 15.1082 3.28825 15.6632C3.28825 16.2169 3.7395 16.6682 4.29325 16.6682ZM16.712 15.6632C16.712 16.2169 16.2614 16.6682 15.707 16.6682C15.152 16.6682 14.7014 16.2169 14.7014 15.6632C14.7014 15.1082 15.152 14.6576 15.707 14.6576C16.2614 14.6576 16.712 15.1082 16.712 15.6632ZM18.7501 11.4382L17.3032 9.99069C17.262 9.94944 17.227 9.90319 17.1989 9.85256L15.3389 6.50506H12.6632V15.0382H13.5501C13.8232 14.0994 14.6814 13.4076 15.707 13.4076C16.732 13.4076 17.5901 14.0994 17.8632 15.0382H18.7501V11.4382ZM16.3939 10.5157C16.7026 10.3613 16.8276 9.98569 16.6732 9.67694L15.4507 7.23131C15.3451 7.01944 15.1282 6.88569 14.8914 6.88569H13.6682C13.3232 6.88569 13.0432 7.16569 13.0432 7.51069C13.0432 7.85569 13.3232 8.13569 13.6682 8.13569H14.5051L15.5551 10.2363C15.6645 10.4551 15.8851 10.5813 16.1145 10.5813C16.2089 10.5813 16.3039 10.5607 16.3939 10.5157Z"
                                  fill="#002D72"
                                  fill-rule="evenodd"
                                  stroke="none"
                                />
                              </svg>
                            </svg>
                          </div>
                          <span
                            class="w-full"
                          >
                            <div
                              class="flex flex-col"
                            >
                              <div
                                class="flex justify-between md:justify-start lg:justify-between"
                              >
                                <div
                                  class="max-w-[60%] flex flex-col"
                                >
                                  <span
                                    class="text-base text-primary-main leading-[21px] font-normal font-bold"
                                  >
                                    <b>
                                      Mo., 28. Apr.
                                       - 
                                      Mi., 30. Apr.
                                    </b>
                                  </span>
                                </div>
                                <div
                                  class="flex flex-col"
                                >
                                  <div
                                    class="flex px-2 py-1 rounded-sm text-black font-bold leading-[13px] text-[13px] bg-[linear-gradient(93deg,#80E764,#64E7C4)] md-lgMinus:ml-2"
                                    data-testid="defaultFreeShipping"
                                    test-target="dynamic-shipping-price"
                                  >
                                    CATALOG.DETAILS_PAGE.BUYBOX.SHIPPING.NEW.LAYOUT.DEFAULT.SHIPPING
                                  </div>
                                </div>
                              </div>
                            </div>
                          </span>
                        </div>
                        <div
                          class="flex pt-6px"
                        >
                          <div
                            class="pl-[28px] flex flex-col"
                          >
                            <span
                              class="text-base leading-[21px] text-primary-main font-normal text-left align-middle cursor-pointer"
                              test-target="dynamic-shipping-label"
                            >
                              CATALOG.DETAILS_PAGE.SHIPPING.PARCEL.DESCRIPTION
                              <span
                                class="whitespace-nowrap"
                              >
                                 
                              </span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex flex-col bg-white-main border-none md-lg:mt-3 !m-0 pt-0 lgPlus:px-6 px-4 pb-4 lgPlus:pb-6 gap-6"
            >
              <div
                class="flex"
              >
                <div
                  class="box-border"
                >
                  <svg
                    aria-hidden="true"
                    class="object-contain mt-2px mr-2 mb-[-1px]"
                    data-testid=""
                    fill=""
                    height="20px"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <svg
                      fill="#002D72"
                      height="20"
                      stroke="none"
                      viewBox="0 0 20 20"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M19.3754 6.87494V18.0556C19.3754 18.4006 19.0954 18.6806 18.7504 18.6806C18.4047 18.6806 18.1254 18.4006 18.1254 18.0556V7.26119L10.0004 3.19869L1.87537 7.26119V18.0556C1.87537 18.4006 1.59537 18.6806 1.25037 18.6806C0.904741 18.6806 0.625366 18.4006 0.625366 18.0556V6.87494C0.625366 6.85674 0.630001 6.84008 0.63468 6.82327L0.634681 6.82327C0.637876 6.81179 0.641091 6.80023 0.642866 6.78806L0.645681 6.76691C0.650614 6.72902 0.655511 6.6914 0.669741 6.65556C0.680758 6.62551 0.697391 6.59988 0.714498 6.57352L0.727241 6.55369L0.73966 6.53282L0.739662 6.53282C0.756103 6.50493 0.772214 6.47759 0.794741 6.45306C0.820217 6.42546 0.850649 6.40462 0.88183 6.38327L0.881837 6.38327L0.898491 6.37181C0.908491 6.36481 0.917491 6.35681 0.926491 6.34881C0.939991 6.33681 0.953491 6.32481 0.970366 6.31619L9.72037 1.94119C9.89599 1.85306 10.1041 1.85306 10.2797 1.94119L19.0297 6.31619C19.0466 6.32481 19.0601 6.33681 19.0736 6.34881L19.0736 6.34882C19.0826 6.35682 19.0916 6.36481 19.1016 6.37181L19.1187 6.38357C19.1498 6.40482 19.1801 6.42559 19.206 6.45306C19.2278 6.47733 19.2437 6.50435 19.2599 6.53194L19.2599 6.53196L19.2729 6.55369L19.2856 6.57353C19.3027 6.59989 19.3193 6.62552 19.3304 6.65556C19.3446 6.6914 19.3495 6.72901 19.3544 6.76691L19.3572 6.78806C19.359 6.80038 19.3624 6.81206 19.3658 6.82368C19.3706 6.84035 19.3754 6.85688 19.3754 6.87494ZM16.4585 13.1943V18.0556C16.4585 18.4006 16.1785 18.6806 15.8335 18.6806H10.0004H4.16662C3.82162 18.6806 3.54162 18.4006 3.54162 18.0556V13.1943C3.54162 12.8493 3.82162 12.5693 4.16662 12.5693H6.45849V8.33306C6.45849 7.98806 6.73849 7.70806 7.08349 7.70806H12.9166C13.2616 7.70806 13.5416 7.98806 13.5416 8.33306V12.5693H15.8335C16.1785 12.5693 16.4585 12.8493 16.4585 13.1943ZM12.9166 13.8193H15.2085V17.4306H10.6254V13.8193H12.9166ZM9.37537 17.4306V13.8193H7.08349H4.79162V17.4306H9.37537ZM10.0004 12.5693H7.70849V8.95806H12.2916V12.5693H10.0004Z"
                        fill="#002D72"
                        fill-rule="evenodd"
                        stroke="none"
                      />
                    </svg>
                  </svg>
                </div>
                <span
                  class="leading-6"
                >
                  <span
                    class="mt-[30px] p-0 text-base text-primary-main !leading-6"
                  >
                    <span>
                      CATALOG.DETAILS_PAGE.BUYBOX.SELLER.SOLD_BY
                      :
                       
                    </span>
                    <a
                      class="[text-decoration:none] text-secondary-main cursor-pointer hover:underline"
                      data-seller-id="e13514f4-18fa-479e-88d7-e79eb3e02ef2"
                      href="https://marketplace-test.metro.de/marktplatz/seller/e13514f4-18fa-479e-88d7-e79eb3e02ef2#about"
                      rel="noopener noreferrer"
                      target="_blank"
                      test-target="seller-name"
                    >
                      Nader Ikladious Shop
                    </a>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
