import { useRouter } from 'next/router'
import { useSearchParam } from 'react-use'

import { useCurrentRegion } from '@core/hooks/useCurrentProductRegion'
import { usePriceType } from '@core/hooks/usePriceType'
import {
  useFeatureFlag,
  useFeatureFlagValue
} from '@core/redux/features/featureFlags'
import { APIResponse } from '@core/services/http/types'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'
import { CountryCode } from '@core/types'

import { usePDPViewEvent } from '@modules/ga-tracking/hooks/usePDPViewEvent'
import { ImageCarousel } from '@modules/product/components/product-image-carousel/ImageCarousel'
import { ProductInformation } from '@modules/product/components/product-information'
import { RebuyGuaranteeLabel } from '@modules/product/components/product-labels/rebuy-gurantee-label'
import {
  BLACK_DEAL_TAGS,
  PRODUCT_SERIES_ATTRIBUTE
} from '@modules/product/constants'
import { ProductVariant } from '@modules/product/types'
import { ItemMarket } from '@modules/product/types/spi'
import {
  getVatRegionInfoV2,
  productHasRebuyGuarantee
} from '@modules/product/utils'

import { ProductAttributeLabelsV2 } from '../product-attributes-labels/V2'
import { ProductLabels } from '../product-labels'

export interface MainComponentProps {
  product: ItemMarket
  productVariants: APIResponse<ProductVariant[]>
  market: CountryCode
  scrollToInfoComponent: () => void
  showHighlights: (value: boolean) => void
  itemListName?: string
  productVariant?: string
  isCallbackShown?: boolean
}

export const ProductMainComponent = ({
  product,
  itemListName,
  productVariants,
  market,
  scrollToInfoComponent,
  showHighlights,
  productVariant,
  isCallbackShown
}: MainComponentProps) => {
  const { price } = usePriceType()
  const router = useRouter()
  const offerId = router.query?.offerId as string
  const { region } = useCurrentRegion()
  const fromRecommendations = useSearchParam('fromSimilarProducts') === '1'

  const tableWare = useFeatureFlag(FeatureFlag.FF_PDP_TABLEWARE_FEATURES)
  const isPdpVideoEnabled = useFeatureFlagValue(FeatureFlag.FF_PDP_VIDEO)

  const showSeriesLabel =
    tableWare &&
    product?.item?.attributes?.[PRODUCT_SERIES_ATTRIBUTE] && 
    product?.item?.attributes?.[PRODUCT_SERIES_ATTRIBUTE]?.value !== '0'

  const hasRebuyGuarantee = tableWare && productHasRebuyGuarantee(product)

  usePDPViewEvent(
    product,
    price,
    false,
    offerId,
    fromRecommendations,
    region,
    productVariant,
    isCallbackShown,
    itemListName
  )
  const isZeroVAT = useFeatureFlag(FeatureFlag.FF_ZERO_VAT)
  const isBlackDealsEnabled = useFeatureFlag(
    FeatureFlag.FF_BLACK_FRIDAY_COUNTDOWN
  )
  const belongsToBlackDeals = product?.tags?.some((item) =>
    BLACK_DEAL_TAGS.includes(item)
  )
  const cheapestOfferId =
    product?.bestOffer?.cheapest?.[region]?.[
      price === 'consumer' ? 'b2c' : 'b2b'
    ]?.[0]
  const currentOfferId = offerId || cheapestOfferId
  const currentOffer = product?.offers?.[currentOfferId]
  const currentRegionInfo = getVatRegionInfoV2(currentOffer, price, isZeroVAT)

  if (!product) return <></>

  return (
    <div className="flex flex-col lg-lgXl:flex-col w-full bg-white-main xs-mdMinus:px-4 py-4 lgXl:p-4 px-3 lg-xlMinus:pb-4 md:flex-row md:flex-nowrap gap-0 md:gap-10 lg-lgXl:gap-2 lgXl:gap-8">
      <div className="md:flex-[1_1_100%] md:max-w-[fit-content] lg-lgXl:max-w-[100%]">
        <div className="block sticky top-3 z-[5]">
          <ProductLabels
            offers={product.offers}
            currentOffer={currentOffer}
            currentRegionInfo={currentRegionInfo}
            isBlackDealsEnabled={isBlackDealsEnabled && belongsToBlackDeals}
            showSeriesLabel={!!showSeriesLabel}
          />

          <RebuyGuaranteeLabel hasGuarantee={hasRebuyGuarantee} />
          <ImageCarousel
            productName={product.item?.name}
            images={product.item?.images}
            videos={isPdpVideoEnabled ? product.item?.videos : []}
          />
          <div className="md-xxxl:ml-16">
            <ProductAttributeLabelsV2
              className="block"
              productItem={product.item}
              category={product.breadcrumb}
            />
          </div>
        </div>
      </div>
      <div className="md-xxxl:w-[30%] lg-lgXl:w-full flex flex-col gap-2 md:flex-[1_1_100%]">
        <div className="md-xxxl:w-full">
          <ProductInformation
            showHighlights={showHighlights}
            scrollToInfoComponent={scrollToInfoComponent}
            product={product}
            productVariants={productVariants}
            market={market}
          />
        </div>
      </div>
    </div>
  )
}
