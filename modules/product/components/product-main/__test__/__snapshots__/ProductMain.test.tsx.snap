// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Product main component should match snapshot 1`] = `
<div>
  <div
    class="flex flex-col lg-lgXl:flex-col w-full bg-white-main xs-mdMinus:px-4 py-4 lgXl:p-4 px-3 lg-xlMinus:pb-4 md:flex-row md:flex-nowrap gap-0 md:gap-10 lg-lgXl:gap-2 lgXl:gap-8"
  >
    <div
      class="md:flex-[1_1_100%] md:max-w-[fit-content] lg-lgXl:max-w-[100%]"
    >
      <div
        class="block sticky top-3 z-[5]"
      >
        <div
          class="md:hidden swiper-container image-carousel overflow-hidden relative max-w-100% swiper-container-initialized swiper-container-horizontal"
        >
          <div
            class="swiper-wrapper flex items-center"
            id="product-image-mobile"
            style="transition-duration: 300ms; transform: translate3d(NaNpx, 0px, 0px);"
          >
            <button
              class="swiper-slide !w-full !flex !w-max-100% justify-center items-center"
            >
              <img
                alt="mobile_image"
                src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510"
              />
            </button>
            <button
              class="swiper-slide !w-full !flex !w-max-100% justify-center items-center"
            >
              <img
                alt="mobile_image"
                src="https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7"
              />
            </button>
          </div>
        </div>
        <div
          class="bottom-0 mb-2 text-center md:hidden"
          data-testid="carousel-bullets"
        >
          <button
            aria-label="Slide 1"
            class="swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 swiper-pagination-bullet-active !bg-blue-main"
          />
          <button
            aria-label="Slide 2"
            class="swiper-pagination-bullet !w-1.5 !h-1.5 !mr-1 "
          />
        </div>
        <div
          class="hidden md:flex box-border place-content-stretch justify-center items-stretch relative mb-6 xl:mb-2"
          data-testid="desktop-carousel-image"
        >
          <div
            class="flex flex-col"
          >
            <div
              class="hidden mr-3 md:flex md:flex-col xl:h-[443px] lg:h-[275px] md:h-[365px] lg-lgXl:max-h-[375px] lg-lgXl:h-full"
            >
              <button
                aria-label="carousel arrow up"
                class="!flex flex-row flex-nowrap justify-center items-center z-[5] cursor-pointer my-auto mx-0 w-52px text-blue-shade-60 h-[48px] text-metro-blue-tint-80 pointer-events-none cursor-default absolute top-0 bg-white-main transition-all !duration-500 invisible !z-0 opacity-0"
                data-testid="swiper-button-p"
              >
                <svg
                  aria-hidden="true"
                  class="block align-middle"
                  data-testid=""
                  fill=""
                  height="24px"
                  viewBox="0 0 32 32"
                  width="24px"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <path
                    d="M8.00029795,19.99975 C7.74029795,19.99975 7.48129795,19.89975 7.28629795,19.69975 C6.89929795,19.30575 6.90529795,18.67175 7.30029795,18.28575 L15.464298,10.28575 C15.853298,9.90475 16.476298,9.90475 16.864298,10.28575 L24.700298,17.96475 C25.095298,18.35175 25.101298,18.98475 24.714298,19.37975 C24.327298,19.77375 23.695298,19.77875 23.300298,19.39375 L16.164298,12.39975 L8.70029795,19.71375 C8.50529795,19.90475 8.25329795,19.99975 8.00029795,19.99975"
                    fill="#002D72"
                    stroke="#002D72"
                  />
                </svg>
              </button>
              <div
                class="swiper-container flex image-carousel overflow-hidden relative max-h-100% swiper-container-initialized swiper-container-vertical"
              >
                <div
                  class="swiper-wrapper flex flex-col"
                  style="transition-duration: 300ms; transform: translate3d(0px, NaNpx, 0px);"
                >
                  <button
                    aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.PREVIEW.ARIA_LABEL"
                    class="swiper-slide my-0 mb-6px last:mb-0 border-metro-tint-80 box-border p-4px border rounded-sm md:!w-[50px] md:!h-[50px] border-2 border-metro-blue-tint-20"
                  >
                    <img
                      alt="thumbnail_image"
                      src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510?h=80&ixlib=php-2.3.0&q=100&w=80"
                    />
                  </button>
                  <button
                    aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.PREVIEW.ARIA_LABEL"
                    class="swiper-slide my-0 mb-6px last:mb-0 border-metro-tint-80 box-border p-4px border rounded-sm md:!w-[50px] md:!h-[50px]"
                  >
                    <img
                      alt="thumbnail_image"
                      src="https://pp-de-metro-markets.imgix.net/item_image/13c4f377-f0c2-46a8-977f-dd8a1adb05f7?h=80&ixlib=php-2.3.0&q=100&w=80"
                    />
                  </button>
                </div>
              </div>
              <button
                aria-label="carousel arrow down"
                class="!flex flex-row flex-nowrap justify-center items-center z-[5] cursor-pointer my-auto mx-0 w-52px text-blue-shade-60 h-[48px] text-metro-blue-tint-80 pointer-events-none cursor-default absolute bg-white-main transition-all !duration-500 bottom-0 invisible !z-0 opacity-0"
                data-testid="swiper-button-n"
              >
                <svg
                  aria-hidden="true"
                  class="block align-middle"
                  data-testid=""
                  fill=""
                  height="24px"
                  viewBox="0 0 32 32"
                  width="24px"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <path
                    d="M24.000298,12 C24.260298,12 24.519298,12.1 24.714298,12.3 C25.101298,12.694 25.095298,13.328 24.700298,13.714 L16.536298,21.714 C16.147298,22.095 15.524298,22.095 15.136298,21.714 L7.30029795,14.035 C6.90529795,13.648 6.89929795,13.015 7.28629795,12.62 C7.67329795,12.226 8.30529795,12.221 8.70029795,12.606 L15.836298,19.6 L23.300298,12.286 C23.495298,12.095 23.747298,12 24.000298,12"
                    fill="#002D72"
                    stroke="#002D72"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div
            class="flex flex-col w-full"
          >
            <div
              class="flex justify-center md:h-[365px] md:w-[365px] lg:h-[275px] lg:w-[275px] xl:h-[393px] xl:w-[393px] lg-lgXl:w-full lg-lgXl:h-full lg-lgXl:max-h-[375px]"
            >
              <button
                aria-label="CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.MAIN_IMAGE.ZOOM.ARIA_LABEL"
                class="relative flex justify-center place-content-center items-center w-full h-full"
                data-testid="desktop-carousel-image-modal-link"
              >
                <img
                  alt="METRO Professional Teller tief Madleen, Steinzeug, Ø 21 cm, grau, 6 Stück"
                  src="https://pp-de-metro-markets.imgix.net/item_image/51204af0-f338-402e-9354-3e5b4994b510"
                />
              </button>
            </div>
            <div
              class="xs:hidden xl:flex content-center justify-center text-grey-tint-40 text-base mb-2"
            >
              CATALOG.DETAILS_PAGE.IMAGE_CAROUSEL.ROLL_OVER.TEXT
            </div>
          </div>
        </div>
        <div
          class="md-xxxl:ml-16"
        />
      </div>
    </div>
    <div
      class="md-xxxl:w-[30%] lg-lgXl:w-full flex flex-col gap-2 md:flex-[1_1_100%]"
    >
      <div
        class="md-xxxl:w-full"
      >
        <div
          class="prose md:overflow-y-hidden"
        >
          <h1
            class="text-blue-shade-60 font-bold md:text-[20px] md:!leading-[26px] text-lg !leading-6 mb-0 subpixel-antialiased [overflow-wrap:anywhere]"
            test-target="PRODUCT_TITLE.MOBILE"
          >
            METRO Professional Teller tief Madleen, Steinzeug, Ø 21 cm, grau, 6 Stück
          </h1>
          <span
            class="group/product-mid inline-flex relative text-grey-tint-40 text-sm cursor-pointer"
          >
            CATALOG.DETAILS_PAGE.PRODUCT_ID.LABEL
            : 
            AAA0000496661
            <div
              class="relative"
              id="clipboard"
            >
              <svg
                aria-hidden="true"
                class="text-grey-tint-40 group-hover/product-mid:text-blue-main group-[.is-opened]/product-mid:text-blue-main"
                data-testid=""
                fill=""
                height="20px"
                viewBox="0 0 20 20"
                width="20px"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="20"
                  stroke="none"
                  viewBox="0 0 20 20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 6H5V16H12V6ZM13 14H15V4H8V5H12C12.2652 5 12.5196 5.10536 12.7071 5.29289C12.8946 5.48043 13 5.73478 13 6V14ZM13 16C13 16.2652 12.8946 16.5196 12.7071 16.7071C12.5196 16.8946 12.2652 17 12 17H5C4.73478 17 4.48043 16.8946 4.29289 16.7071C4.10536 16.5196 4 16.2652 4 16V6C4 5.73478 4.10536 5.48043 4.29289 5.29289C4.48043 5.10536 4.73478 5 5 5H7V4C7 3.73478 7.10536 3.48043 7.29289 3.29289C7.48043 3.10536 7.73478 3 8 3H15C15.2652 3 15.5196 3.10536 15.7071 3.29289C15.8946 3.48043 16 3.73478 16 4V14C16 14.2652 15.8946 14.5196 15.7071 14.7071C15.5196 14.8946 15.2652 15 15 15H13V16Z"
                    fill="currentColor"
                    stroke="none"
                  />
                </svg>
              </svg>
            </div>
          </span>
          <div
            class="relative flex flex-col md:mb-[16px] pt-[12px] xs:pb-[16px] md:pb-0 gap-[8px]"
            data-testid="variants"
          >
            <div
              class="flex flex-col justify-start text-grey-main"
              data-testid="variant_group"
            >
              <div
                class="flex flex-row text-metro-blue-main text-[14px]"
              >
                <span>
                  GN Größe
                  : 
                </span>
                <span
                  class="font-bold"
                >
                  1/1
                </span>
              </div>
              <div
                class="flex flex-row xs:overflow-x-scroll md:overflow-visible md:flex-wrap gap-[10px] pt-[4px]"
              >
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border-2 border-metro-blue-tint-20 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/39aabfac-a424-45dc-8806-93a91b4ece76"
                  id="gn_size-39aabfac-a424-45dc-8806-93a91b4ece76"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                  >
                    1/1
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                      href="https://marketplace-test.metro.de/marktplatz/product/39aabfac-a424-45dc-8806-93a91b4ece76"
                    >
                      17,90 €
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/c823c7ad-d031-452b-a24a-75375ec89c1a"
                  id="gn_size-c823c7ad-d031-452b-a24a-75375ec89c1a"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    1/2
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/c823c7ad-d031-452b-a24a-75375ec89c1a"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/e4e26ea6-48b0-451a-8734-31776c38e3c5"
                  id="gn_size-e4e26ea6-48b0-451a-8734-31776c38e3c5"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    1/3
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/e4e26ea6-48b0-451a-8734-31776c38e3c5"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/a8e0066a-b4df-45dc-a96a-fd1c9d406a04"
                  id="gn_size-a8e0066a-b4df-45dc-a96a-fd1c9d406a04"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    1/4
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/a8e0066a-b4df-45dc-a96a-fd1c9d406a04"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/0eb7538f-e6ce-4d53-b24d-cd7eb81d9503"
                  id="gn_size-0eb7538f-e6ce-4d53-b24d-cd7eb81d9503"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    1/6
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/0eb7538f-e6ce-4d53-b24d-cd7eb81d9503"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="flex flex-col justify-start text-grey-main"
              data-testid="variant_group"
            >
              <div
                class="flex flex-row text-metro-blue-main text-[14px]"
              >
                <span>
                  GN Tiefe
                  : 
                </span>
                <span
                  class="font-bold"
                >
                  6.5 cm
                </span>
              </div>
              <div
                class="flex flex-row xs:overflow-x-scroll md:overflow-visible md:flex-wrap gap-[10px] pt-[4px]"
              >
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/5539ad26-e4fc-4915-b786-cd7ba510430b"
                  id="gn_depth-5539ad26-e4fc-4915-b786-cd7ba510430b"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                  >
                    4 cm
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                      href="https://marketplace-test.metro.de/marktplatz/product/5539ad26-e4fc-4915-b786-cd7ba510430b"
                    >
                      17,90 €
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border-2 border-metro-blue-tint-20 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/39aabfac-a424-45dc-8806-93a91b4ece76"
                  id="gn_depth-39aabfac-a424-45dc-8806-93a91b4ece76"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                  >
                    6.5 cm
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                      href="https://marketplace-test.metro.de/marktplatz/product/39aabfac-a424-45dc-8806-93a91b4ece76"
                    >
                      17,90 €
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/facfb852-fb43-45a3-b2d2-43ce9c1da1fa"
                  id="gn_depth-facfb852-fb43-45a3-b2d2-43ce9c1da1fa"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-main"
                  >
                    10 cm
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="
    !text-metro-blue-tint-40
    text-base
    leading-[21px]
    font-normal
    bg-transparent
    whitespace-nowrap"
                      href="https://marketplace-test.metro.de/marktplatz/product/facfb852-fb43-45a3-b2d2-43ce9c1da1fa"
                    >
                      20,20 €
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/c823c7ad-d031-452b-a24a-75375ec89c1a"
                  id="gn_depth-c823c7ad-d031-452b-a24a-75375ec89c1a"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    15 cm
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/c823c7ad-d031-452b-a24a-75375ec89c1a"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
                <a
                  class="flex md:justify-center items-start hover:shadow-md hover:shadow-gray-main rounded-md border border-[1px] border-dashed border-grey-tint-80 md:h-[34px] flex-col px-2 py-6px"
                  data-testid="variant_item"
                  href="https://marketplace-test.metro.de/marktplatz/product/7dbc52c3-c1c7-4aa4-a6fa-c22cd9dfcfef"
                  id="gn_depth-7dbc52c3-c1c7-4aa4-a6fa-c22cd9dfcfef"
                >
                  <span
                    class="
    flex
    items-center
    w-full
    text-base
    font-bold
    leading-6
    cursor-pointer
    whitespace-nowrap text-metro-blue-tint-40"
                  >
                    20 cm
                  </span>
                  <div
                    class="
    flex
    md:hidden"
                  >
                    <a
                      class="text-metro-blue-tint-40 font-normal whitespace-pre-line text-base leading-[21px] w-[117px]"
                      href="https://marketplace-test.metro.de/marktplatz/product/7dbc52c3-c1c7-4aa4-a6fa-c22cd9dfcfef"
                    >
                      CATALOG.DETAILS_PAGE.VARIANTS.TOOLTIP.SEE_AVAILABLE_OPTIONS
                    </a>
                  </div>
                </a>
              </div>
            </div>
          </div>
          <div
            class="hidden md:block lg:grid lg:grid-cols-2 lg:gap-6 lgXl:block"
          >
            <div
              class=""
            >
              <ul
                class="ml-2 pl-3 mb-0 list-outside list-disc not-prose marker:"
                test-target="product-artificial-highlights"
              >
                <li
                  class="text-base my-0 ml-[5px] pl-0 leading-6"
                  data-testid="highlights-brand"
                >
                  <span
                    class="text-blue-shade-60"
                  >
                    CATALOG.CATALOG_PAGE.FILTERS.TITLE.BRAND
                  </span>
                  : 
                  <a
                    class="text-secondary-main hover:underline"
                    href="https://marketplace-test.metro.de/marktplatz/b/metro-professional"
                    rel="noreferrer"
                    target="_blank"
                  >
                    METRO Professional
                  </a>
                </li>
                <li
                  class="text-base my-0 ml-[5px] pl-0 leading-6"
                >
                  <div
                    class="text-ellipsis text-blue-shade-60"
                  >
                    Hergestellt aus hochwertigem Steinzeug
                  </div>
                </li>
                <li
                  class="text-base my-0 ml-[5px] pl-0 leading-6"
                >
                  <div
                    class="text-ellipsis text-blue-shade-60"
                  >
                    Mikrowellen- und Spülmaschinengeeignet
                  </div>
                </li>
                <li
                  class="text-base my-0 ml-[5px] pl-0 leading-6"
                >
                  <div
                    class="text-ellipsis text-blue-shade-60"
                  >
                    Stapelbar
                  </div>
                </li>
                <li
                  class="text-base my-0 ml-[5px] pl-0 leading-6"
                >
                  <div
                    class="text-ellipsis text-blue-shade-60"
                  >
                    Sehr buntes und trendiges sortiment
                  </div>
                </li>
              </ul>
              <div
                class="sticky bottom-0 pt-[10px]"
              >
                <a
                  class="block text-secondary-main cursor-pointer hover:underline text-base text-left"
                  test-target="product-highlights-read-more"
                >
                  CATALOG.DETAILS_PAGE.MORE_PRODUCT_INFO.LABEL
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
