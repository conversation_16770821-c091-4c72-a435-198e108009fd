import { NUMBER_0 } from '@core/constants/numbers'
import { CountryCode, CountryCodes } from '@core/types'

import { SVGIcon } from '@modules/shared/components'
import { SVG_NAMES } from '@modules/shared/icons/constants'
import { clsx } from '@modules/shared/utils'

import { Attribute } from '../types/spi'

export const FRIDGE_USPS = {
  TEMPERATURE_DISPLAY: 'temperature_display',
  TEMPERATURE_DISPLAY_V2: 'temperature_display_V2',
  CONTROL_PANEL: 'control_panel',
  CONTROL_PANEL_V2: 'control_panel_V2',
  LOCKABLE: 'lockable',
  LOCKABLE_V2: 'lockable_V2',
  DOOR_OPEN_ALARM: 'door_open_alarm',
  DOOR_OPEN_ALARM_V2: 'door_open_alarm_V2',
  ADJUSTABLE_SHELVES: 'adjustable_shelves',
  ADJUSTABLE_SHELVES_V2: 'adjustable_shelves_V2',
  FRID<PERSON>_LIGHT: 'fridge_interior_light',
  FRIDGE_LIGHT_V2: 'fridge_interior_light_V2'
}

export const USPS = {
  REBUY_GUARANTEE: 'int_rbg',
  STACKABLE: 'stackable',
  DISHWASHER_SAFE: 'dishwasher_safe',
  MICROWAVE_SAFE: 'suitable_for_microwaves',
  OVENPROOF: 'ovenproof',
  THERMAL_SHOCK_RESISTANT: 'thermal_shock_resistant',
  NON_STICKING: 'non_stick_coating',
  CORROSION_RESISTANT: 'corrosion_resistant',
  INDUCTION_COMPATIBLE: 'induction_compatible',
  SANDWICH_BOTTOM: 'sandwich_bottom',
  GN_COMPATIBLE: 'gn_compatible',
  TEMPERED_GLASS: 'tempered_glass',
  WITH_CALLIBRATION: 'with_calibration',
  FOLDABLE: 'foldable',
  EXTENDABLE: 'extendable',
  WEATHER_PROOF: 'weatherproof',
  UV_PROTECTION: 'uv_protection',
  WATER_RESISTANT: 'waterresistant_boolean',
  WATER_REPELLANT: 'water_repellent_boolean',
  ...FRIDGE_USPS
} as const

export const USPS_CONFIG = {
  defaultOrder: [
    USPS.TEMPERATURE_DISPLAY,
    USPS.CONTROL_PANEL,
    USPS.LOCKABLE,
    USPS.DOOR_OPEN_ALARM,
    USPS.ADJUSTABLE_SHELVES,
    USPS.FRIDGE_LIGHT,
    USPS.REBUY_GUARANTEE,
    USPS.STACKABLE,
    USPS.DISHWASHER_SAFE,
    USPS.MICROWAVE_SAFE,
    USPS.OVENPROOF,
    USPS.THERMAL_SHOCK_RESISTANT,
    USPS.NON_STICKING,
    USPS.CORROSION_RESISTANT,
    USPS.INDUCTION_COMPATIBLE,
    USPS.SANDWICH_BOTTOM,
    USPS.GN_COMPATIBLE,
    USPS.TEMPERED_GLASS,
    USPS.WITH_CALLIBRATION,
    USPS.FOLDABLE,
    USPS.EXTENDABLE,
    USPS.WEATHER_PROOF,
    USPS.UV_PROTECTION,
    USPS.WATER_RESISTANT,
    USPS.WATER_REPELLANT
  ]
}

export const USP_ICONS = (className?: string, widthHeight?: string) => {
  const createIcon = (
    name,
    size: { width: string; height: string } = { width: '48px', height: '48px' }
  ): JSX.Element => (
    <SVGIcon
      name={name}
      width={size.width}
      height={size.height}
      className={clsx(className)}
    />
  )

  return {
    [USPS.THERMAL_SHOCK_RESISTANT]: createIcon(
      SVG_NAMES.THERMAL_SHOCK_RESISTANT
    ),
    [USPS.DISHWASHER_SAFE]: createIcon(SVG_NAMES.DISHWASHER_SAFE),
    [USPS.MICROWAVE_SAFE]: createIcon(SVG_NAMES.MICROWAVE_SAFE),
    [USPS.OVENPROOF]: createIcon(SVG_NAMES.OVENPROOF),
    [USPS.REBUY_GUARANTEE]: createIcon(SVG_NAMES.REBUY_GUARANTEE),
    [USPS.STACKABLE]: createIcon(SVG_NAMES.STACKABLE),
    [USPS.FOLDABLE]: createIcon(SVG_NAMES.FOLDABLE),
    [USPS.WEATHER_PROOF]: createIcon(SVG_NAMES.WEATHER_PROOF),
    [USPS.UV_PROTECTION]: createIcon(SVG_NAMES.UV_PROTECTION),
    [USPS.WATER_RESISTANT]: createIcon(SVG_NAMES.WATER_RESISTANT),
    [USPS.WATER_REPELLANT]: createIcon(SVG_NAMES.WATER_REPELLANT),
    [USPS.EXTENDABLE]: createIcon(SVG_NAMES.EXTENDABLE),
    [USPS.WITH_CALLIBRATION]: createIcon(SVG_NAMES.WITH_CALLIBRATION),
    [USPS.TEMPERED_GLASS]: createIcon(SVG_NAMES.TEMPERED_GLASS),
    [USPS.NON_STICKING]: createIcon(SVG_NAMES.NON_STICKING),
    [USPS.INDUCTION_COMPATIBLE]: createIcon(SVG_NAMES.INDUCTION_COMPATIBLE),
    [USPS.CORROSION_RESISTANT]: createIcon(SVG_NAMES.CORROSION_RESISTANT),
    [USPS.SANDWICH_BOTTOM]: createIcon(SVG_NAMES.SANDWICH_BOTTOM),
    [USPS.GN_COMPATIBLE]: createIcon(SVG_NAMES.GN_COMPATIBLE),
    [USPS.ADJUSTABLE_SHELVES]: createIcon(SVG_NAMES.ADJUSTABLE_SHELVES),
    [USPS.ADJUSTABLE_SHELVES_V2]: createIcon(SVG_NAMES.ADJUSTABLE_SHELVES_V2),
    [USPS.FRIDGE_LIGHT]: createIcon(SVG_NAMES.FRIDGE_LIGHT),
    [USPS.FRIDGE_LIGHT_V2]: createIcon(SVG_NAMES.FRIDGE_LIGHT_V2),
    [USPS.LOCKABLE]: createIcon(SVG_NAMES.LOCKABLE),
    [USPS.LOCKABLE_V2]: createIcon(SVG_NAMES.LOCKABLE_V2),
    [USPS.TEMPERATURE_DISPLAY]: createIcon(SVG_NAMES.TEMPERATURE_DISPLAY),
    [USPS.TEMPERATURE_DISPLAY_V2]: createIcon(SVG_NAMES.TEMPERATURE_DISPLAY_V2),
    [USPS.DOOR_OPEN_ALARM]: createIcon(SVG_NAMES.DOOR_OPEN_ALARM),
    [USPS.DOOR_OPEN_ALARM_V2]: createIcon(SVG_NAMES.DOOR_OPEN_ALARM_V2),
    [USPS.CONTROL_PANEL]: createIcon(SVG_NAMES.CONTROL_PANEL),
    [USPS.CONTROL_PANEL_V2]: createIcon(SVG_NAMES.CONTROL_PANEL_V2)
  }
}

export const USPS_TEXTS = {
  [USPS.DISHWASHER_SAFE]: 'CATALOG.DETAILS_PAGE.TABLEWARE.DISHWASHER_SAFE',
  [USPS.MICROWAVE_SAFE]: 'CATALOG.DETAILS_PAGE.TABLEWARE.MICROWAVE_SAFE',
  [USPS.OVENPROOF]: 'CATALOG.DETAILS_PAGE.TABLEWARE.OVENPROOF',
  [USPS.REBUY_GUARANTEE]: 'CATALOG.DETAILS_PAGE.TABLEWARE.REBUY_GUARANTEE',
  [USPS.STACKABLE]: 'CATALOG.DETAILS_PAGE.TABLEWARE.STACKABLE',
  [USPS.THERMAL_SHOCK_RESISTANT]:
    'CATALOG.DETAILS_PAGE.TABLEWARE.THERMAL_SHOCK_RESISTANT',
  [USPS.FOLDABLE]: 'CATALOG.DETAILS_PAGE.USPS.FOLDABLE',
  [USPS.EXTENDABLE]: 'CATALOG.DETAILS_PAGE.USPS.EXTENDABLE',
  [USPS.WEATHER_PROOF]: 'CATALOG.DETAILS_PAGE.USPS.WEATHER_PROOF',
  [USPS.UV_PROTECTION]: 'CATALOG.DETAILS_PAGE.USPS.UV_PROTECTION',
  [USPS.WATER_RESISTANT]: 'CATALOG.DETAILS_PAGE.USPS.WATER_RESISTANT',
  [USPS.WATER_REPELLANT]: 'CATALOG.DETAILS_PAGE.USPS.WATER_REPELLANT',
  [USPS.WITH_CALLIBRATION]: 'CATALOG.DETAILS_PAGE.USPS.WITH_CALLIBRATION',
  [USPS.TEMPERED_GLASS]: 'CATALOG.DETAILS_PAGE.USPS.TEMPERED_GLASS',
  [USPS.NON_STICKING]: 'CATALOG.DETAILS_PAGE.USPS.NON_STICKING',
  [USPS.INDUCTION_COMPATIBLE]: 'CATALOG.DETAILS_PAGE.USPS.INDUCTION_COMPATIBLE',
  [USPS.CORROSION_RESISTANT]: 'CATALOG.DETAILS_PAGE.USPS.CORROSION_RESISTANT',
  [USPS.SANDWICH_BOTTOM]: 'CATALOG.DETAILS_PAGE.USPS.SANDWICH_BOTTOM',
  [USPS.GN_COMPATIBLE]: 'CATALOG.DETAILS_PAGE.USPS.GN_COMPATIBLE',
  [USPS.ADJUSTABLE_SHELVES]: 'CATALOG.DETAILS_PAGE.USPS.ADJUSTABLE_SHELVES',
  [USPS.CONTROL_PANEL]: 'CATALOG.DETAILS_PAGE.USPS.CONTROL_PANEL',
  [USPS.FRIDGE_LIGHT]: 'CATALOG.DETAILS_PAGE.USPS.FRIDGE_LIGHT',
  [USPS.TEMPERATURE_DISPLAY]: 'CATALOG.DETAILS_PAGE.USPS.TEMPERATURE_DISPLAY',
  [USPS.LOCKABLE]: 'CATALOG.DETAILS_PAGE.USPS.LOCKABLE',
  [USPS.DOOR_OPEN_ALARM]: 'CATALOG.DETAILS_PAGE.USPS.DOOR_OPEN_ALARM'
}

export const getFridgeAttributes = (attributes: Record<string, Attribute>) =>
  [
    attributes?.['cooling_type'],
    attributes?.['height_product'] &&
    attributes?.['width_product'] &&
    attributes?.['length_depth_product']
      ? [
          attributes?.['height_product'],
          attributes?.['width_product'],
          attributes?.['length_depth_product']
        ]
      : false,
    attributes?.['interior_capacity'],
    attributes?.['capacity'],
    attributes?.['total_gross_capacity'],
    attributes?.['gross_capacity_cooling'],
    attributes?.['temperature_range'],
    attributes?.['energy_consumption'],
    attributes?.['defrosting'],
    attributes?.['number_of_shelves'],
    attributes?.['loading_capacity_grates'],
    attributes?.['number_drawers'],
    attributes?.['number_of_bottles'],
    attributes?.['gn_size'],
    attributes?.['door_hinge']
  ].filter((item) => Boolean(item))

export const COOLING_TYPE = {
  STATIC: 'STATIC',
  VENTILATED: 'VENTILATED',
  COMPRESSOR: 'COMPRESSOR'
}
export const COOLING_TYPE_ICON = {
  [COOLING_TYPE.VENTILATED]: SVG_NAMES.VENTILATED_COOLING,
  [COOLING_TYPE.STATIC]: SVG_NAMES.STATIC_COOLING
}
export const COOLING_TYPES_BY_MARKET = {
  [COOLING_TYPE.VENTILATED]: {
    [CountryCodes.Germany]: [
      'Absorberkühlung',
      'Umluft',
      'Umluft/Luftschleier',
      'Umluft/Stille Kühlung umschaltbar',
      'Luftkühlung',
      'Zentralkimaanlage',
      'Mini-Split Klimaanlage',
      'Verdunstungskühlanlage',
      'Fan-Kühler',
      'Wärmepumpe',
      'Ventiliert',
      'dynamisch'
    ],
    [CountryCodes.France]: [
      'Refroidissement par absorption',
      'Froid brassé',
      'Circulation de l’air/Rideau d’air',
      'Circulation de l’air/Refroidissement silencieux interchangeables',
      'Circulation d’air',
      'Climatisation centrale',
      'Mini-splits sans conduits',
      'Climatisation par évaporation',
      'Ventilateurs',
      'Pompes à chaleur',
      'Froid ventilé',
      'Froid dynamique'
    ],
    [CountryCodes.Italy]: [
      'chiller ad assorbimento',
      'ricircolo dell’aria',
      'ricircolo/barriera d’aria',
      'ricircolo dell’aria/raffreddamento silenzioso commutabile',
      'raffreddamento dell’aria',
      'climatizzatore centralizzato',
      'climatizzatore mini-split',
      'impianto di raffreddamento a condensatore',
      'ventola',
      'pompa di calore',
      'ventilato',
      'dinamico'
    ],
    [CountryCodes.Spain]: [
      'Enfriamiento por absorción',
      'Aire de circulación',
      'Circulación de aire/Cortina de aire',
      'Circulación de aire/Refrigeración silenciosa conmutable',
      'Refrigeración por aire',
      'Sistema central de aire acondicionado',
      'Sistema de aire acondicionado minisplit',
      'Climatizador evaporativo',
      'Ventilador refrigerador',
      'Bomba de calor',
      'Ventilado',
      'Dinámico'
    ],
    [CountryCodes.Netherland]: [
      'Absorptiekoeling',
      'Recirculatie',
      'Recirculatie/luchtgordijn',
      'Recirculatie/geluidsarme koeling omschakelbaar',
      'Luchtkoeling',
      'Centraal airconditioningsysteem',
      'Mini-split airconditioningsysteem',
      'Verdampingskoeling',
      'Ventilatorkoeler',
      'Warmtepomp',
      'Geventileerd',
      'dynamisch'
    ],
    [CountryCodes.Portugal]: [
      'Refrigeração por absorção',
      'Circulação de ar',
      'Circulação de ar/cortina de ar',
      'Alternável entre circulação de ar e refrigeração silenciosa',
      'Arrefecimento do ar',
      'Ar condicionado central',
      'Pequeno ar condicionado',
      'Sistema de arrefecimento evaporativo',
      'Refrigerador do ventilador',
      'Bomba de calor',
      'Ventilado',
      'Dinâmica'
    ]
  },
  [COOLING_TYPE.STATIC]: {
    [CountryCodes.Germany]: [
      'Wärmerohre',
      'Heatsink',
      'Wasserkühlung',
      'Passive Kühlung',
      'Stille Kühlung',
      'statisch',
      'statische Kühlung mit Umluftventilator',
      'Thermoelektrik'
    ],
    [CountryCodes.France]: [
      'Caloducs',
      'Dissipateurs thermiques',
      'Refroidissement par liquide',
      'Refroidissement passif',
      'Refroidissement silencieux',
      'Chaleur statique',
      'Froid statique avec ventilation de brassage',
      'Thermoélectrique'
    ],
    [CountryCodes.Italy]: [
      'heat pipe',
      'dissipatore',
      'raffreddamento ad acqua',
      'raffreddamento passivo',
      'raffreddamento silenzioso',
      'statico',
      'Raffreddamento statico con ventola di ricircolo',
      'Termoelettrico'
    ],
    [CountryCodes.Spain]: [
      'Tubos de calor',
      'Disipador térmico',
      'Refrigeración por agua',
      'Refrigeración pasiva',
      'Refrigeración silenciosa',
      'Estática',
      'Enfriamiento estático con ventilador de recirculación',
      'Termoeléctrico'
    ],
    [CountryCodes.Netherland]: [
      'Warmtebuizen',
      'Koelplaat',
      'Waterkoeling',
      'Passieve koeling',
      'Geluidsarme koeling',
      'statisch',
      'statische koeling met recirculatieventilator',
      'Thermoelektrisch'
    ],
    [CountryCodes.Portugal]: [
      'Tubos de calor',
      'Dissipador de calor',
      'Refrigeração a água',
      'Refrigeração passiva',
      'Refrigeração silenciosa',
      'Estática',
      'Arrefecimento estático com ventilador de recirculação',
      'Termoelétrico'
    ]
  },
  [COOLING_TYPE.COMPRESSOR]: {
    [CountryCodes.Germany]: ['Kompressorkühlung'],
    [CountryCodes.France]: ['Refroidissement par compression'],
    [CountryCodes.Italy]: ['raffreddamento a compressore'],
    [CountryCodes.Spain]: ['Enfriamiento por compresor'],
    [CountryCodes.Netherland]: ['Compressiekoeling'],
    [CountryCodes.Portugal]: ['Refrigeração do compressor']
  }
}

// export  const COOLING_TYPES_CONTENT = {
//   'STATIC': {},
//   'VENTILATED': {},
//   'COMPRESSOR': {
//     iconName: null,
//     label: 'CATALOG.DETAILS_PAGE.COOLING.COMPRESSOR',
//     description: {
//       bold: 'CATALOG.DETAILS_PAGE.COOLING_TYPE.SIDEBAR.STATIC.DESCRIPTION_BOLD',
//       normal:
//     }
//   }
// }
export const getCoolingTypeNameAndIcon = (
  isStatic,
  isVentilated,
  isCompressor
) => {
  if (isStatic)
    return ['CATALOG.DETAILS_PAGE.COOLING.STATIC', SVG_NAMES.STATIC_COOLING]
  if (isVentilated)
    return [
      'CATALOG.DETAILS_PAGE.COOLING.VENTILATED',
      SVG_NAMES.VENTILATED_COOLING
    ]
  if (isCompressor) return ['CATALOG.DETAILS_PAGE.COOLING.COMPRESSOR', null]
}
