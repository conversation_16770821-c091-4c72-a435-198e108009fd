import { Attribute } from '@modules/product/types/spi'

import { USPS_CONFIG } from '../constants/usp.constants'

export const getUSPS = (
  attributes: Record<string, Attribute>,
  category: string
): Attribute[] => {
  if (!attributes || !category) return []

  try {
    // Destructure necessary attributes
    const {
      waterresistant_boolean,
      water_repellent_boolean,
      uv_protection,
      extendable,
      ...restAttributes
    } = attributes

    // Conditionally overwrite attributes if both waterresistant and water_repellent are true
    const updatedAttributes =
      waterresistant_boolean && water_repellent_boolean
        ? { ...restAttributes, water_repellent_boolean }
        : attributes

    // For the outdoor category, include UV_PROTECTION, WATER_RESISTANT & EXTENDABLE
    const finalAttributes =
      category === '5e66271a-89b0-48c6-8472-4d30a4dfe1f6'
        ? {
            ...updatedAttributes,
            uv_protection,
            waterresistant_boolean,
            extendable
          }
        : updatedAttributes

    // Return the filtered results based on USPS priority order
    return USPS_CONFIG.defaultOrder
      .map((code) => finalAttributes[code])
      .filter((item: Attribute) => item?.value === '1')
  } catch (error) {
    return []
  }
}
