import { CountryCode } from '@core/types'

import { ATTRIBUTE_CODE_TYPES } from '@modules/product/constants'

import { COOLING_TYPES_BY_MARKET } from '../constants/usp.constants'
import { Attribute, ItemMarket } from '../types/spi'

export const productHasRebuyGuarantee = (product: ItemMarket): boolean => {
  return (
    product?.item?.attributes?.[ATTRIBUTE_CODE_TYPES.REBUY_GUARANTEE]?.value ===
    '1'
  )
}

export const getPDEdata = (
  attributes: Record<string, Attribute>,
  key: string
): any | null => {
  const PDEAttribute = attributes?.['pde_data']
  if (!PDEAttribute) {
    return null
  }
  const parsedPDEAttribute = JSON.parse(PDEAttribute.value)
  return parsedPDEAttribute[key] ? parsedPDEAttribute[key] : null
}

export const getCoolingType = (
  productCoolingType: string,
  market: CountryCode
) => {
  if (!productCoolingType) return null
  const [coolingType, _] = Object.entries(COOLING_TYPES_BY_MARKET).find(
    ([coolingType, typesByMarket]) =>
      typesByMarket[market].find(
        (marketCoolingType) => productCoolingType === marketCoolingType
      )
  )
  return coolingType
}
