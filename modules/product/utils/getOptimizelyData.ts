import { NUMBER_0, NUMBER_4 } from '@core/constants/numbers'
import { Price } from '@core/types'

import { DEFAULT_REGION } from '@modules/shared/components/RegionSelector/const/regionSelector-constants'
import { RegionResponse } from '@modules/shared/components/RegionSelector/types/region.type'
import { EXPERIMENT_NAMES } from '@modules/shared/utils/optimizely.utils'

import { METRO_SELLER_ID, OK, SHIPPING_TYPE } from '../constants'
import { getPrice } from './general.utils'

export const getOptimizelyData = (
  priceTypeFromCookie,
  regionCookieValue,
  regionInfo,
  market,
  query,
  product
) => {
  const selectedRegion =
    regionCookieValue ??
    (regionInfo?.type === OK &&
    !!(regionInfo.result as RegionResponse)?.items?.length
      ? (regionInfo.result as RegionResponse)?.items?.[NUMBER_0]?.id
      : null) ??
    DEFAULT_REGION[market]

  const selectedOfferId = query?.offerId as string
  const bestRegionalOfferId =
    product?.result?.bestOffer?.cheapest?.[selectedRegion]?.[
      priceTypeFromCookie === Price.b2c ? 'b2c' : 'b2b'
    ]?.[NUMBER_0]

  const activeOfferId = selectedOfferId ?? bestRegionalOfferId

  const offer = activeOfferId ? product?.result?.offers?.[activeOfferId] : null

  const isShippingThreshold =
    !!offer?.shippingGroup?.shippingCosts?.[selectedRegion]?.[NUMBER_0]
      ?.thresholdEnabled
  const isMetroSeller = offer?.organization?.id === METRO_SELLER_ID

  const offerPrice = getPrice(
    offer?.destinationRegionInfo?.price,
    priceTypeFromCookie
  )

  const shippingType =
    offer?.shippingGroup?.shippingCosts?.[selectedRegion]?.[0]?.shippingType

  const isParcelDelivery =
    (shippingType === SHIPPING_TYPE.DEFAULT && !offer?.freightForwarding) ||
    shippingType === SHIPPING_TYPE.PARCEL

  const isFridgeCategory =
    product?.result?.breadcrumb?.parents[product?.result?.breadcrumb?.parents?.length - 1]
      ?.id === 'e45413de-3229-40b6-be52-2f77f97bcdc2'

  return {
    experimentKeys: [EXPERIMENT_NAMES.FRIDGE_KEY_FEATURES, EXPERIMENT_NAMES.NEW_PRODUCT_DATA_VIEW_ON_PDP],
    experimentAttributes: {
      has_FST: isShippingThreshold,
      offer_own_brand: isMetroSeller,
      offer_price: Number(offerPrice),
      delivery_type: isParcelDelivery ? Number(NUMBER_4) : null,
      is_fridge_category: isFridgeCategory
    }
  }
}
