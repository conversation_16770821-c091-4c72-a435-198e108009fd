import { OK } from '@modules/product/constants'
import { ProductIndexMockV2 } from '@modules/product/mocks/product-index/product-index-mock'
import { DEFAULT_REGION } from '@modules/shared/components/RegionSelector/const/regionSelector-constants'

import { getOptimizelyData } from '../getOptimizelyData'

describe('should return optimizely data for PDP', () => {
  it('should return correct data without selected values', () => {
    expect(
      getOptimizelyData(
        'consumer',
        null,
        { result: [], type: OK },
        'de',
        {},
        {
          result: ProductIndexMockV2,
          type: OK
        }
      )
    ).toEqual({
      experimentAttributes: {
        delivery_type: null,
        has_FST: true,
        offer_own_brand: false,
        offer_price: 114.24,
        is_fridge_category: false
      },
      experimentKeys: ['fridge_key_features', 'new_product_data_view_on_pdp']
    })
  })

  it('should return correct data with selected values', () => {
    expect(
      getOptimizelyData(
        'consumer',
        DEFAULT_REGION['de'],
        { result: [], type: OK },
        'de',
        { offerId: '51c2dee2-7632-428c-a7e1-e476e1e991c7' },
        {
          result: ProductIndexMockV2,
          type: OK
        }
      )
    ).toEqual({
      experimentAttributes: {
        delivery_type: null,
        has_FST: true,
        offer_own_brand: false,
        offer_price: 114.24,
        is_fridge_category: false
      },
      experimentKeys: ['fridge_key_features', 'new_product_data_view_on_pdp']
    })
  })
})
