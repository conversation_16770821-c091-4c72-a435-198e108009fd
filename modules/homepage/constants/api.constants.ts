import { config } from '@core/config'
import { CountryCode } from '@core/types/countryCode'

const API = 'api/v1/public'
const SEARCH_API = 'api/v3/search'
const LOCAL_API = 'api/v1'
const BRAND_API = 'api/v1/brands'

export const API_TOP_PRODUCT_CAROUSEL_SEARCH = `${config.svcSearch2BaseUrl}/${SEARCH_API}`
export const API_TOP_PRODUCT_CAROUSEL_SB = `${config.svcStoryblokBridge}/${SEARCH_API}`
export const API_BRANDS_SB = `${config.svcStoryblokBridge}/${BRAND_API}`
export const API_SEARCH_SUGGESTIONS = `${config.svcSearch2BaseUrl}/api/v3/suggestions`

interface RecomendationsUrlProps {
  recommendationsId: string
  productId: string
  limit?: number
  fillWithRandom?: number
  forceSearch2ProdUrl?: boolean
}

export const getApiSearchRecommendationsUrl = ({
  recommendationsId,
  productId,
  limit,
  fillWithRandom,
  forceSearch2ProdUrl
}: RecomendationsUrlProps) =>
  `${forceSearch2ProdUrl ? config.svcSearch2ProdUrl : config.svcSearch2BaseUrl}/api/v3/recommendations/${recommendationsId}?productId=${productId}&fillWithRandom=${fillWithRandom ?? 0}&limit=${limit ?? 1}`

export const API_TOP_CATEGORIES_NAVIGATION = (region?: string) => {
  if (!region) {
    return `${config.svcCategoriesBaseUrl}/api/categories`
  }
  return `${config.svcCategoriesBaseUrl}/api/categories?region=${region}`
}
export const API_TOP_CATEGORIES_NAVIGATION_PROMOTIONS = `${config.svcCategoriesBaseUrl}/api/categories?flat=1&onlyWithPromotions=1&pageSize=10`
export const API_MINI_CART_REMOTE_QUANTITY = `${config.svcCheckoutBaseUrl}/${LOCAL_API}/cart/quantity`
export const API_MINI_CART_REMOTE_CART = `${config.svcCheckoutBaseUrl}/${LOCAL_API}/cart`

export const API_PUBLIC_CART = `${config.svcAppCartBaseUrl}/api/public/v2/cart`
export const API_MINI_CART_MERGE = `${config.svcCheckoutBaseUrl}/${LOCAL_API}/cart/all`

const API_APP_CART = (version: 'v1' | 'v2' = 'v1') =>
  `${config.buyerGateway}/api/account/proxy/cart/${version}`

export const AppCartAPI = {
  postOffer: `${API_APP_CART('v1')}/cart`,
  getCart: `${API_APP_CART('v2')}/carts/my`,
  deliveryOption: `${API_APP_CART('v1')}/cart/delivery`,
  mergeCart: `${API_APP_CART('v1')}/cart/all`,
  notifications: `${API_APP_CART('v1')}/cart/notifications`,
  checkInventory: `${config.svcAppCartBaseUrl}/api/public/v1/cart/validate`,

  getCartQuantity: () =>
    `${API_APP_CART('v1')}/cart/quantity`,

  deleteOffer: (cartLineId: string) =>
    `${API_APP_CART('v1')}/cart/${cartLineId}`,

  patchOffer: (cartLineId: string) =>
    `${API_APP_CART('v1')}/cart/${cartLineId}`,

  postService: (cartLineId: string) =>
    `${API_APP_CART('v1')}/cart/${cartLineId}/services`,

  deleteService: (cartLineId: string, serviceLineId: string) =>
    `${API_APP_CART('v1')}/cart/${cartLineId}/services/${serviceLineId}`
}

export const API_MINI_CART_CHECK_INVENTORY_OFFER = `${config.svcCheckoutBaseUrl}/${API}/cart/offer-inventory`
export const API_BUYER_ACCOUNT_EMPLOYEE_SHARE_PAYMENT_LINK = `${config.svcBuyerAccountBaseUrl}/api/employee/v1/shared-payment-links`
export const API_BUYER_ACCOUNT_EMPLOYEE_COMPLETED_ORDERS = `${config.svcBuyerAccountBaseUrl}/api/employee/v1/employee/completed-orders`
export const API_BUYER_ACCOUNT_EMPLOYEE_RESEND_SHARED_PAYMENT_LINK = (id) =>
  `${config.svcBuyerAccountBaseUrl}/api/v1/shared-payment-links/${id}/resend`
export const API_ADDRESS_VALIDATE = `${config.svcCheckoutBaseUrl}/${LOCAL_API}/checkout/validate-addresses`
export const API_ADDRESS_VALIDATE_V2 = `${config.buyerGateway}/api/account/proxy/checkout_v2/checkout/addresses/validate`
export const API_CART_PROCEED_CHECKOUT = `${config.svcCheckoutBaseUrl}/${LOCAL_API}/checkout`
export const API_CART_CHECKOUT_INITIALIZE = `${config.buyerGateway}/api/account/proxy/checkout_v2/checkout/initialize`
export const BLOCKED_ACCOUNT = 'blocked-account'
export const CUSTOMERS_LIST = 'auth/customers-list'
export const ASSISTED_SALES_OVERVIEW = 'assisted-sales/overview'
export const ORDERS_HISTORY = 'account/orders-history'
export const APP_CUSTOM_PLP_ROUTE = 'landing'
export const APP_CATALOG_ROUTE = 'catalog'

export const getBaseUrlWithApi = `${config.svcStorefrontBaseUrl}/${API}`

export const getEnergyEfficiencyLabelImage = (label: string) =>
  `${config.cdnBaseUrl}/images/energy-efficiency-labels/${label}.svg`

export const apiStoryblok = {
  BASE: '/cdn/stories',
  CURRENT_SPACE: '/cdn/spaces/me'
}

export const getApiPromoWidgetLink = (market: CountryCode) => {
  return `cdn/stories/${market}/global/header-promo-widget`
}

export const getNewsLetterImageSrcUrlV2 = (color: string): string => {
  return !color || color === 'white'
    ? `${config.cdnBaseUrl}/images/voucher%20illustration.svg`
    : `${config.cdnBaseUrl}/images/voucher%20illustration-1.svg`
}
