import { CountryCode, CountryCodes } from '@core/types'

import {
  BUTTON_VARIANTS,
  ButtonVariant
} from '@modules/shared/components/Button/types/variant'
import { Option } from '@modules/shared/types'

import { OrdersHistoryPageState } from '../types'
import { OrderLineStatusView } from '../types/order-line-status-view'
import { ShippingType } from '../types/shipping-type.enum'

export const INITIAL_PAGE_STATE_HISTORY: OrdersHistoryPageState = {
  offset: 0,
  limit: 3
}

export const CONTACT_SELLER_OPTIONS: Option[] = [
  {
    id: 1,
    value:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.TIME'
  },
  {
    id: 2,
    value:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.ENQUIRY'
  }
]

export const labelClassNames: Record<OrderLineStatusView, string | undefined> =
  {
    [OrderLineStatusView.waitingForPayment]: 'bg-grey-tint-90',
    [OrderLineStatusView.paymentFailed]: 'bg-red-tint-80',
    [OrderLineStatusView.paid]: 'bg-grey-tint-80',
    [OrderLineStatusView.waitingForApproval]: 'bg-yellow-tint-80',
    [OrderLineStatusView.rejected]: 'bg-red-tint-80',
    [OrderLineStatusView.placed]: 'bg-green-tint-80',
    [OrderLineStatusView.placedDelayed]: 'bg-orange-tint-80',
    [OrderLineStatusView.canceledByBuyer]: 'bg-red-tint-80',
    [OrderLineStatusView.canceledBySeller]: 'bg-red-tint-80',
    [OrderLineStatusView.canceledSecurity]: 'bg-red-tint-80',
    [OrderLineStatusView.confirmed]: 'bg-green-tint-80',
    [OrderLineStatusView.confirmedDelayed]: 'bg-orange-tint-80',
    [OrderLineStatusView.shippedNotIntegratedWithCarrier]: 'bg-green-tint-80',
    [OrderLineStatusView.shippedReadyToShip]: 'bg-green-tint-80',
    [OrderLineStatusView.shippedOnItsWay]: 'bg-green-tint-80',
    [OrderLineStatusView.shippedDeliveredFirst14Days]: 'bg-green-tint-80',
    [OrderLineStatusView.shippedDeliveredAfter14Days]: undefined,
    [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay]:
      undefined,
    [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfter14DaysEndDay]:
      undefined,
    [OrderLineStatusView.shippedFailed]: 'bg-orange-tint-80',
    [OrderLineStatusView.returnRequested]: 'bg-green-tint-80',
    [OrderLineStatusView.returnAccepted]: 'bg-green-tint-80',
    [OrderLineStatusView.returnDeclined]: 'bg-red-tint-80',
    [OrderLineStatusView.refunded]: 'bg-green-tint-80'
  }

export const extraInformationNotBold: OrderLineStatusView[] = [
  OrderLineStatusView.placed,
  OrderLineStatusView.confirmed,
  OrderLineStatusView.shippedReadyToShip,
  OrderLineStatusView.shippedOnItsWay,
  OrderLineStatusView.shippedNotIntegratedWithCarrier
]

export const extraInformation: Record<OrderLineStatusView, string | undefined> =
  {
    [OrderLineStatusView.waitingForPayment]: undefined,
    [OrderLineStatusView.waitingForApproval]: undefined,
    [OrderLineStatusView.paid]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PAID',
    [OrderLineStatusView.paymentFailed]: undefined,
    [OrderLineStatusView.rejected]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REJECTED',
    [OrderLineStatusView.placed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY',
    [OrderLineStatusView.placedDelayed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED',
    [OrderLineStatusView.canceledByBuyer]: undefined,
    [OrderLineStatusView.canceledBySeller]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SELLER',
    [OrderLineStatusView.canceledSecurity]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SECURITY',
    [OrderLineStatusView.confirmed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY',
    [OrderLineStatusView.confirmedDelayed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED',
    [OrderLineStatusView.shippedNotIntegratedWithCarrier]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY',
    [OrderLineStatusView.shippedReadyToShip]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY',
    [OrderLineStatusView.shippedOnItsWay]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY',
    [OrderLineStatusView.shippedDeliveredFirst14Days]: '',
    [OrderLineStatusView.shippedDeliveredAfter14Days]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_ON',
    [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_BY',
    [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfter14DaysEndDay]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_BY',
    [OrderLineStatusView.shippedFailed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.SHIPPED_FAILED',
    [OrderLineStatusView.returnRequested]: undefined,
    [OrderLineStatusView.returnDeclined]: undefined,
    [OrderLineStatusView.returnAccepted]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.RETURNED',
    [OrderLineStatusView.refunded]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REFUNDED'
  }

export const overridedLabelText: Partial<Record<OrderLineStatusView, string>> =
  {
    [OrderLineStatusView.waitingForPayment]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_PAYMENT',
    [OrderLineStatusView.waitingForApproval]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_APPROVAL',
    [OrderLineStatusView.paymentFailed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.PAYMENT_FAILED',
    [OrderLineStatusView.placedDelayed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED',
    [OrderLineStatusView.placed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED',
    [OrderLineStatusView.confirmed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED',
    [OrderLineStatusView.confirmedDelayed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED',
    [OrderLineStatusView.canceledByBuyer]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
    [OrderLineStatusView.canceledSecurity]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
    [OrderLineStatusView.canceledBySeller]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
    [OrderLineStatusView.rejected]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REJECTED',
    [OrderLineStatusView.shippedReadyToShip]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.READY_SHIP',
    [OrderLineStatusView.shippedOnItsWay]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_ON_ITS_WAY',
    [OrderLineStatusView.shippedDeliveredFirst14Days]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED',
    [OrderLineStatusView.shippedNotIntegratedWithCarrier]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED',
    [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED',
    [OrderLineStatusView.shippedFailed]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_FAILED',
    [OrderLineStatusView.refunded]:
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REFUNDED'
  }

export const buyAgainButtonVariant: Partial<
  Record<OrderLineStatusView, ButtonVariant>
> = {
  [OrderLineStatusView.canceledBySeller]: BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.shippedDeliveredFirst14Days]: BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.shippedDeliveredAfter14Days]: BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay]:
    BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfter14DaysEndDay]:
    BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.returnRequested]: BUTTON_VARIANTS.secondary,
  [OrderLineStatusView.returnAccepted]: BUTTON_VARIANTS.infoAccessible,
  [OrderLineStatusView.refunded]: BUTTON_VARIANTS.infoAccessible
}

export const returnInstructionsFaqUrlByCountry: Record<CountryCode, string> = {
  [CountryCodes.Germany]:
    'https://metro-markets.freshdesk.com/de/support/solutions/folders/********678',
  [CountryCodes.Spain]:
    'https://support-es.metro-marketplace.eu/es/support/solutions/folders/75000011578',
  [CountryCodes.Italy]:
    'https://support-it.metro-marketplace.eu/it/support/solutions/folders/75000013694',
  [CountryCodes.Portugal]:
    'https://support-pt.metro-marketplace.eu/pt-PT/support/solutions/folders/75000014169',
  [CountryCodes.Netherland]:
    'https://support-nl.metro-marketplace.eu/nl/support/solutions/folders/103000177496',
  [CountryCodes.France]:
    'https://support-fr.metro-marketplace.eu/fr/support/solutions/folders/103000240854'
}

export const faqDeliveryAndTrackingByCountry: Record<CountryCode, string> = {
  [CountryCodes.Germany]:
    'https://metro-markets.freshdesk.com/de/support/solutions/articles/75000121115-fehlende-tracking-informationen-',
  [CountryCodes.Spain]:
    'https://support-es.metro-marketplace.eu/es/support/solutions/articles/75000121117-%C2%BFfalta-informaci%C3%B3n-sobre-el-seguimiento-de-tu-pedido-',
  [CountryCodes.Italy]:
    'https://support-it.metro-marketplace.eu/it/support/solutions/articles/75000121116-mancano-le-informazioni-di-tracciamento-',
  [CountryCodes.Portugal]:
    'https://support-pt.metro-marketplace.eu/pt-PT/support/solutions/articles/75000121118-faltam-informa%C3%A7%C3%B5es-de-rastreamento-',
  [CountryCodes.Netherland]:
    'https://support-nl.metro-marketplace.eu/nl/support/solutions/articles/103000271717-ontbrekende-trackinginformatie-',
  [CountryCodes.France]:
    'https://support-fr.metro-marketplace.eu/fr/support/solutions/articles/103000271716-il-manque-des-informations-de-suivi-'
}

export const faqUrlInstallationServicesByCountry: Record<CountryCode, string> =
  {
    [CountryCodes.Germany]: '',
    [CountryCodes.Spain]: '',
    [CountryCodes.Italy]: '',
    [CountryCodes.Portugal]: '',
    [CountryCodes.Netherland]: '',
    [CountryCodes.France]:
      'https://support-fr.metro-marketplace.eu/fr/support/solutions/folders/103000475170'
  }

export const faqUrlByCountry: Record<CountryCode, string> = {
  [CountryCodes.Germany]:
    'https://metro-markets.freshdesk.com/de/support/solutions/articles/75000019041-fallen-f%C3%BCr-die-r%C3%BCcksendung-nach-aus%C3%BCbung-des-widerrufsrechts-kosten-f%C3%BCr-mich-an-',
  [CountryCodes.Spain]:
    'https://support-es.metro-marketplace.eu/es/support/solutions/articles/75000125409-%C2%BFtendr%C3%A9-que-pagar-el-env%C3%ADo-de-la-devoluci%C3%B3n-si-ejerzo-mi-derecho-de-desistimiento-',
  [CountryCodes.Italy]:
    'https://support-it.metro-marketplace.eu/it/support/solutions/articles/75000086439-in-caso-di-restituzione-sussistono-costi-a-mio-carico-',
  [CountryCodes.Portugal]:
    'https://support-pt.metro-marketplace.eu/pt-PT/support/solutions/articles/75000125900-terei-de-pagar-o-envio-da-devolu%C3%A7%C3%A3o-depois-de-exercer-o-meu-direito-de-rescis%C3%A3o-/',
  [CountryCodes.Netherland]:
    'https://support-nl.metro-marketplace.eu/nl/support/solutions/articles/103000051712-maak-ik-kosten-als-ik-de-goederen-retourneer-',
  [CountryCodes.France]:
    'https://support-fr.metro-marketplace.eu/fr/support/solutions/articles/103000132844-est-ce-que-je-vais-avoir-des-frais-si-je-retourne-la-marchandise-'
}

export const pendingPaymentInfoVisibleStatusView: OrderLineStatusView[] = [
  OrderLineStatusView.waitingForPayment,
  OrderLineStatusView.paid,
  OrderLineStatusView.waitingForApproval,
  OrderLineStatusView.rejected,
  OrderLineStatusView.placed,
  OrderLineStatusView.placedDelayed,
  OrderLineStatusView.canceledByBuyer,
  OrderLineStatusView.canceledBySeller,
  OrderLineStatusView.canceledSecurity,
  OrderLineStatusView.confirmed,
  OrderLineStatusView.confirmedDelayed,
  OrderLineStatusView.shippedNotIntegratedWithCarrier,
  OrderLineStatusView.shippedReadyToShip,
  OrderLineStatusView.shippedOnItsWay,
  OrderLineStatusView.shippedDeliveredFirst14Days
]

export const returnItemButtonVisibleStatusView: OrderLineStatusView[] = [
  OrderLineStatusView.shippedNotIntegratedWithCarrier,
  OrderLineStatusView.shippedReadyToShip,
  OrderLineStatusView.shippedOnItsWay,
  OrderLineStatusView.shippedDeliveredFirst14Days,
  OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay,
  OrderLineStatusView.shippedFailed
]

export const shippingTypeToTranslation: Record<ShippingType, string> = {
  [ShippingType.curbsideDelivery]:
    'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.CURBSIDE',
  [ShippingType.parcelDelivery]:
    'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PARCEL',
  [ShippingType.placeOfUseDerlivery]:
    'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PLACE_OF_USE'
}
