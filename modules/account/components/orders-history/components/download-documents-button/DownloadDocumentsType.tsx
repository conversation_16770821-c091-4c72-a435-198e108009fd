import { useDispatch } from 'react-redux'

import { ordersHistoryDownloadDocumentsAlert } from '@core/alerts/alertObjects/ordersHistoryDownloadDocumentsAlert'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { addAlert } from '@core/redux/features/alerts/AlertSlice'
import { useLocale } from '@core/redux/features/market/useLocale'

import { Button, Divider, SVGIcon } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { SVG_NAMES } from '@modules/shared/icons/constants'

import { useActiveContext } from '../../hooks/useActiveContext'
import { useOrdersHistoryGAEvents } from '../../hooks/useOrdersHistoryGAEvents'
import { downloadDocuments } from '../../services/orders-history.service'
import { OrderDocumentItem } from '../../types'
import { OrderSidebarProduct } from '../order-sidebar-product/OrderSidebarProduct'

export interface Props {
  title: string
  documents: {
    documents: OrderDocumentItem[]
    product: {
      name: string
      url: string
    }
  }[]
  onClose: () => void
}

export const DownloadDocumentsType = ({ title, documents, onClose }: Props) => {
  const market = useMarket()
  const locale = useLocale()
  const { t } = useAccountTranslation()
  const activeContext = useActiveContext()
  const { trackDownloadDocument, trackDownloadAllDocuments } =
    useOrdersHistoryGAEvents()

  const dispatch = useDispatch()

  const downloadDocument = async (
    document: OrderDocumentItem
  ): Promise<void> => {
    try {
      trackDownloadDocument()
      await downloadDocuments(
        market,
        locale,
        activeContext,
        document.type,
        document.id,
        document.name
      )
    } catch (_) {
      dispatch(addAlert(ordersHistoryDownloadDocumentsAlert()))
      onClose()
    }
  }

  const downloadAllDocuments = async (): Promise<void> => {
    try {
      trackDownloadAllDocuments()
      await downloadDocuments(
        market,
        locale,
        activeContext,
        documents[0].documents[0].type, // all documents have the same type
        documents.flatMap((doc) => doc.documents.map((item) => item.id)),
        documents.flatMap((doc) => doc.documents.map((item) => item.name))
      )
    } catch (_) {
      dispatch(addAlert(ordersHistoryDownloadDocumentsAlert()))
      onClose()
    }
  }

  return (
    <>
      <div className="font-lato space-y-4">
        <div className="flex justify-between">
          <h3 className="text-lg text-metro-blue-main font-semibold">
            {title}
          </h3>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex justify-center items-center font-semibold whitespace-nowrap h-[40px]"
            onClick={downloadAllDocuments}
            rounded
          >
            <div className="flex flex-row leading-[28px] space-x-2">
              <span className="text-regular">
                {t('PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.DOWNLOAD_ALL')}
              </span>
              <SVGIcon
                name={SVG_NAMES.DOWNLOAD}
                fill="#005AE0"
                width="20px"
                height="20px"
              />
            </div>
          </Button>
        </div>
        <div className="space-y-4">
          {documents.map((document, index) => {
            return (
              <>
                <OrderSidebarProduct
                  name={document.product.name}
                  url={document.product.url}
                >
                  <div className="flex flex-col mt-2 space-y-4 pr-4">
                    {document.documents.map((document) => (
                      <div className="flex space-x-5" key={document.id}>
                        <span className="text-blue-main truncate w-full text-base">
                          {document.name}
                        </span>

                        <SVGIcon
                          name={SVG_NAMES.DOWNLOAD}
                          dataTestid="order-detail-download-document"
                          className="cursor-pointer"
                          onClick={() => downloadDocument(document)}
                          fill="#0064FE"
                          width="20px"
                          height="20px"
                          tabIndex={0}
                        />
                      </div>
                    ))}
                  </div>
                </OrderSidebarProduct>
                {index + 1 < documents.length && <Divider />}
              </>
            )
          })}
        </div>
      </div>
    </>
  )
}
