import { useRef, useState } from 'react'

import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useUser } from '@core/redux'

import { Button, SVGIcon } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { SVG_NAMES } from '@modules/shared/icons/constants'

import { OrderHistoryItem } from '../../types'
import { DownloadDocumentsSidebar } from './DownloadDocumentsSidebar'

export interface Props {
  order: OrderHistoryItem
}

export const DownloadDocumentsButton = ({ order }: Props) => {
  const { t } = useAccountTranslation()
  const { isSubsidiaryAccount } = useUser()
  const [isShowingSidebar, setIsShowingSidebar] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null)

  let documentsButtonType: DocumentsType | undefined

  const hasInvoices = order.orderLines.some(
    (orderLine) => orderLine.documents?.invoices?.length > 0
  )
  const hasCreditNotes = order.orderLines.some(
    (orderLine) => orderLine.documents?.creditNotes?.length > 0
  )
  if (hasCreditNotes) {
    documentsButtonType = DocumentsType.documents
  } else if (hasInvoices) {
    documentsButtonType = DocumentsType.invoices
  }

  return (
    <>
      {documentsButtonType !== undefined && !isSubsidiaryAccount && (
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex text-base text-blue-main justify-center items-center lg:hidden lg:!w-[240px] font-bold text-blue-main text-regular font-semibold lg:ml-4 px-4 py-1.5 w-full whitespace-nowrap leading-[28px] min-h-[40px] mb-3"
            onClick={() => setIsShowingSidebar(true)}
            rounded
          >
            <SVGIcon
              name={SVG_NAMES.DOCUMENTS}
              className="mr-6px"
              fill="#005AE0"
              width="20px"
              height="20px"
            />
            <span>{t(buttonTextGetDocuments[documentsButtonType])}</span>
          </Button>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex hidden lg:block justify-center items-center w-full w-auto lg:!w-[240px] font-bold text-regular font-semibold whitespace-nowrap h-[40px] mb-3"
            onClick={() => setIsShowingSidebar(true)}
            ref={buttonRef}
            rounded
          >
            <div className="flex flex-row leading-[28px] justify-center items-center">
              <SVGIcon
                name={SVG_NAMES.DOCUMENTS}
                className="mr-6px mt-4px"
                fill="#005AE0"
                width="20px"
                height="20px"
              />
              {t(buttonTextGetDocuments[documentsButtonType])}
            </div>
          </Button>
          {isShowingSidebar && (
            <DownloadDocumentsSidebar
              order={order}
              onClose={() => {
                setIsShowingSidebar(false)
                buttonRef?.current?.focus()
              }}
            />
          )}
        </>
      )}
    </>
  )
}

const enum DocumentsType {
  invoices,
  documents
}

const buttonTextGetDocuments: Record<DocumentsType, string> = {
  [DocumentsType.invoices]: 'PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.INVOICES',
  [DocumentsType.documents]: 'PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.DOCUMENTS'
}
