import { fireEvent, screen, within } from '@testing-library/react'
import { useRouter } from 'next/router'
import React from 'react'

import { renderWithProviders } from '@core/utils/testing'

import { order, orderDocumentItem, orderLine } from '../../__tests__/fixtures'
import { OrdersHistoryOrderHeader } from '../OrdersHistoryOrderHeader'

jest.mock('@core/hooks/useTranslation', () => ({
  useAccountTranslation: () => ({ t: (key: string) => key })
}))
const gtagEventMock = jest.fn()
jest.mock(
  '@modules/account/components/orders-history/hooks/useOrdersHistoryGAEvents',
  () => ({
    useOrdersHistoryGAEvents: () => ({
      trackViewOrderDetails: gtagEventMock
    })
  })
)
describe('OrdersHistoryOrderHeader', () => {
  it('Should show the information of the order', () => {
    renderWithProviders(<OrdersHistoryOrderHeader order={order} autoFocus />)

    expect(
      screen.getByTestId('orders-history-order-header-status')
    ).toHaveTextContent(
      'PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDER_PLACED:30 Jul, 2024'
    )
    expect(
      screen.getByTestId('orders-history-order-header-id')
    ).toHaveTextContent(order.orderNumber)
    expect(
      screen.getByTestId('orders-history-order-header-total')
    ).toHaveTextContent('PAGES.ACCOUNT.ORDER_HISTORY.ORDER.TOTAL:1.304,40 €')
  })

  it('Should go to the order detail page when click the detail button', () => {
    const mockPush = jest.fn()

    ;(useRouter as jest.Mock).mockImplementation(() => ({
      query: { orderNumber: order.orderNumber },
      push: mockPush
    }))

    renderWithProviders(<OrdersHistoryOrderHeader order={order} autoFocus />)

    fireEvent.click(
      screen.getByTestId('orders-history-order-header-go-order-details')
    )
    expect(mockPush).toHaveBeenCalledWith({
      query: { orderNumber: order.orderNumber }
    })
  })

  it('Should show 3 buttons to get the invoice and to view the order details', () => {
    renderWithProviders(
      <OrdersHistoryOrderHeader
        order={{
          ...order,
          orderLines: [
            {
              ...orderLine,
              documents: {
                invoices: [],
                creditNotes: [orderDocumentItem],
                returnLabels: []
              }
            }
          ]
        }}
      />
    )

    expect(
      within(
        screen.getByTestId('orders-history-order-header-actions')
      ).getAllByRole('button')
    ).toHaveLength(3)
  })

  describe('GA', () => {
    it('should call to track function when is clicked', () => {
      renderWithProviders(<OrdersHistoryOrderHeader order={order} autoFocus />)

      fireEvent.click(
        screen.getByTestId('orders-history-order-header-go-order-details')
      )

      expect(gtagEventMock).toBeCalled()
    })
  })
})
