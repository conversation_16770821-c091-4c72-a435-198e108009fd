import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button, Loader } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { primary } from '@styles/palette'

interface Props {
  numOrdersDisplayed: number
  totalCount: number
  isLoading: boolean
  onClick: () => void
}

export const OrdersHistoryPagination = ({
  numOrdersDisplayed,
  totalCount,
  isLoading,
  onClick
}: Props) => {
  const { t } = useAccountTranslation()

  return numOrdersDisplayed < totalCount ? (
    <div className="flex justify-center mt-12 lg:mb-12 min-h-[40px]">
      <Button
        width="medium"
        variant={BUTTON_VARIANTS.infoAccessible}
        onClick={!isLoading ? onClick : undefined}
        rounded
        className={isLoading ? 'cursor-wait' : ''}
      >
        {isLoading ? (
          <Loader strokeColor={primary.main} strokeWidth="6.25%" />
        ) : (
          <span className="text-regular">
            {t('PAGES.ACCOUNT.ORDERS_HISTORY.LOAD_MORE_BUTTON')}
          </span>
        )}
      </Button>
    </div>
  ) : (
    <></>
  )
}
