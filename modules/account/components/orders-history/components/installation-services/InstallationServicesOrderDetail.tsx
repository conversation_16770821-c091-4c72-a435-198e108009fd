import { config } from '@core/config'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useLocale } from '@core/redux/features/market/useLocale'

import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { useActiveContext } from '../../hooks/useActiveContext'
import { getLoginToSSOUrl } from '../../services/orders-history.service'
import { OrderLine } from '../../types'
import { OrderDetailOrderLineDescription } from '../order-detail-order-line/OrderDetailOrderLineDescription'
import { OrderDetailOrderLinePrice } from '../order-detail-order-line/OrderDetailOrderLinePrice'
import { OrderDetailOrderLineSoldBy } from '../order-detail-order-line/OrderDetailOrderLineSoldBy'
import { OrderHistoryRowDesktop } from '../order-history-row-desktop/OrderHistoryRowDesktop'

interface Props {
  orderLine: OrderLine
}

export const InstallationServicesOrderDetail = ({ orderLine }: Props) => {
  const { t } = useAccountTranslation()
  const market = useMarket()
  const locale = useLocale()
  const activeContext = useActiveContext()

  const isB2B = activeContext === 'personal'

  const createSupportTicket = async () => {
    const redirectUrl = await getLoginToSSOUrl(market, locale, activeContext)
    window.open(redirectUrl, '_blank')
  }
  orderLine.productDetails.imageUrl = `${config.cdnBaseUrl}/images/icon-installation-service.svg`

  return (
    <section
      className="lg:last:rounded-b-[2px] lg:border-b-[1px] lg:border-l-[1px] lg:border-r-[1px] lg:border-[#E5E7EA] sm:my-12px lg:my-0 lg:p-[16px] w-full space-y-3"
      data-testid="order-history-installation-services"
    >
      <div className="hidden lg:block">
        <OrderHistoryRowDesktop
          firstColumn={
            <>
              <OrderDetailOrderLineDescription orderLine={orderLine} />
              <OrderDetailOrderLineSoldBy orderLine={orderLine} />
            </>
          }
          secondColumn={
            <OrderDetailOrderLinePrice
              orderLine={orderLine}
              isB2B={isB2B}
              isShowingOnlyTotals
            />
          }
          thirdColumn={
            <Button
              variant={BUTTON_VARIANTS.infoAccessible}
              className="w-full text-regular font-semibold font-lato min-h-[40px]"
              onClick={createSupportTicket}
              data-testid="order-history-installation-service-button"
              rounded
            >
              {t('PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.CONTACT')}
            </Button>
          }
        />
      </div>
      {/* Mobile version */}
      <div className="lg:hidden w-full space-y-3">
        <OrderDetailOrderLineDescription orderLine={orderLine} />
        <OrderDetailOrderLineSoldBy orderLine={orderLine} />
        <div>
          <OrderDetailOrderLinePrice
            orderLine={orderLine}
            isB2B={isB2B}
            isShowingOnlyTotals
          />
        </div>
        <Button
          variant={BUTTON_VARIANTS.infoAccessible}
          className="w-full text-regular font-semibold font-lato min-h-[40px]"
          onClick={createSupportTicket}
          data-testid="order-history-installation-service-button"
          rounded
        >
          {t('PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.CONTACT')}
        </Button>
      </div>
    </section>
  )
}
