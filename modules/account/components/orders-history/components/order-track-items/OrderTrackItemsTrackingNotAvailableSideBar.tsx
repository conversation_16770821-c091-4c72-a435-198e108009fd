import { useRef, useState } from 'react'

import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'

import { handleEnterOrSpacePress } from '@shared/utils/accessibility.utils'

import { faqDeliveryAndTrackingByCountry } from '../../constants'
import { OrderSidebar } from '../order-sidebar/OrderSidebar'

export const OrderTrackItemsTrackingNotAvailableSideBar = () => {
  const { t } = useAccountTranslation()
  const market = useMarket()

  const [isShowingSideBar, setIsShowingSideBar] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null)

  return (
    <div
      className="font-lato text-center"
      data-testId="order-detail-order-line-actions-no-tracking"
    >
      <button
        className="text-blue-main text-base cursor-pointer bg-none border-none p-0 m-0 appearance-none"
        ref={buttonRef}
        onClick={(e) => {
          setIsShowingSideBar(true)
        }}
        onKeyDown={handleEnterOrSpacePress(() => setIsShowingSideBar(true))}
      >
        {t(
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.LABEL'
        )}
      </button>

      {isShowingSideBar && (
        <OrderSidebar
          title={t(
            'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.TITLE'
          )}
          onClose={() => {
            setIsShowingSideBar(false)
            buttonRef?.current?.focus()
          }}
        >
          <div className="flex flex-col text-base text-primary-main space-y-2">
            <span>
              {t(
                'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.INFO'
              )}
            </span>
            <ul className="list-disc leading-[24px] space-y-2 ml-6">
              <li>
                {' '}
                {t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_1'
                )}
              </li>
              <li>
                {t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_2'
                )}
              </li>
              <li>
                {t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_3'
                )}
              </li>
              <li>
                {' '}
                {t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_4'
                )}
              </li>
            </ul>
            <span>
              {t(
                'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_1'
              )}{' '}
              <a
                className="text-blue-main cursor-pointer"
                href={faqDeliveryAndTrackingByCountry[market]}
                target="_blank"
              >
                {t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_2'
                )}
              </a>
            </span>
          </div>
        </OrderSidebar>
      )}
    </div>
  )
}
