import { v4 } from 'uuid'

import {
  BUTTON_VARIANTS,
  ButtonVariant
} from '@modules/shared/components/Button/types/variant'

import { OrderLine } from '../../types'
import { OrderLineStatusView } from '../../types/order-line-status-view'
import { convertOrderLineStatusAndTrackingStatusToOrderLineStatusView } from '../../utils/utils'
import { OrderTrackItem } from './OrderTrackItem'
import { OrderTrackItemsTrackingNotAvailableSideBar } from './OrderTrackItemsTrackingNotAvailableSideBar'

interface Props {
  orderLine: OrderLine
  variant: ButtonVariant
}

export const OrderTrackItems = ({ orderLine, variant }: Props) => {
  const trackingButtonVariant = orderLine.isBuyAgain
    ? BUTTON_VARIANTS.infoAccessible
    : variant

  const trackingsStatus =
    orderLine.trackingDetails?.map((_, index) =>
      convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(
        orderLine,
        index
      )
    ) ?? []

  if (trackingsStatus.length === 0) {
    trackingsStatus.push(
      convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(orderLine, 0)
    )
  }

  const showIsTrackingNotAvailable =
    orderLine.trackingDetails?.some((tracking) => {
      return !tracking.trackingUrl
    }) ?? true

  let isShowingTrackingInformation = trackingsStatus.some((trackingStatus) => {
    return [
      OrderLineStatusView.shippedNotIntegratedWithCarrier,
      OrderLineStatusView.shippedDeliveredFirst14Days,
      OrderLineStatusView.shippedReadyToShip,
      OrderLineStatusView.shippedOnItsWay
    ].includes(trackingStatus)
  })

  return (
    <>
      {isShowingTrackingInformation && (
        <>
          <div
            data-testId="order-detail-order-line-actions-tracking"
            className="flex flex-col space-y-1 justify-end"
          >
            <div className="space-y-3">
              {trackingsStatus.map((_, index) => (
                <OrderTrackItem
                  key={v4()}
                  tracking={
                    orderLine.trackingDetails?.length > index
                      ? orderLine.trackingDetails[index]
                      : null
                  }
                  numPackage={
                    orderLine.trackingDetails?.length === 1 ? null : index + 1
                  }
                  variant={trackingButtonVariant}
                />
              ))}
            </div>

            {showIsTrackingNotAvailable && (
              <OrderTrackItemsTrackingNotAvailableSideBar />
            )}
          </div>
        </>
      )}
    </>
  )
}
