import { v4 } from 'uuid'

import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button, SVGIcon } from '@modules/shared/components'
import {
  BUTTON_VARIANTS,
  ButtonVariant
} from '@modules/shared/components/Button/types/variant'
import { SVG_NAMES } from '@modules/shared/icons/constants'

import { useOrdersHistoryGAEvents } from '../../hooks/useOrdersHistoryGAEvents'
import { DeliveryTracking } from '../../types'

interface Props {
  tracking?: DeliveryTracking
  numPackage?: number
  variant: ButtonVariant
}

export const OrderTrackItem = ({ tracking, numPackage, variant }: Props) => {
  const { t } = useAccountTranslation()
  const { trackTrackingButton } = useOrdersHistoryGAEvents()

  const colorClassName =
    variant === BUTTON_VARIANTS.infoAccessible ? 'text-blue-interaction' : ''

  const isTrackingAvailable = !!tracking?.trackingUrl

  const clickButton = (): void => {
    trackTrackingButton()
  }

  return (
    <>
      {isTrackingAvailable ? (
        <a
          key={v4()}
          className="flex flex-row font-lato w-full min-h-[40px]"
          target="_blank"
          onClick={clickButton}
          href={tracking.trackingUrl}
          aria-label={
            !numPackage
              ? t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING_ITEM')
              : t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_PACKAGE', {
                  numPackage: numPackage
                })
          }
        >
          <Button
            variant={variant}
            className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px]"
            rounded
            data-testId="order-detail-order-line-actions-tracking-button"
          >
            <span className={colorClassName}>
              {!numPackage ? (
                <>{t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING_ITEM')}</>
              ) : (
                <>
                  {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_PACKAGE', {
                    numPackage: numPackage
                  })}
                </>
              )}
            </span>
            <SVGIcon
              name={SVG_NAMES.CHEVRON_RIGHT}
              className={`ml-6px ${colorClassName}`}
              width="20px"
              height="20px"
            />
          </Button>
        </a>
      ) : (
        <Button
          variant={BUTTON_VARIANTS.infoAccessible}
          disabled
          className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px] !text-[#677283]"
          data-testId="order-detail-order-line-actions-tracking-button-disabled"
          rounded
        >
          {t(
            'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.BUTTON'
          )}
        </Button>
      )}
    </>
  )
}
