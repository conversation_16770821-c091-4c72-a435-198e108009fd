import { getAppBuyerUrl } from '@core/config/url'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button } from '@modules/shared/components'
import {
  BUTTON_VARIANTS,
  ButtonVariant
} from '@modules/shared/components/Button/types/variant'

import { buyAgainButtonVariant } from '../../constants'
import { useOrdersHistoryGAEvents } from '../../hooks/useOrdersHistoryGAEvents'
import { OrderLine } from '../../types'
import { OrderLineStatusView } from '../../types/order-line-status-view'
import { convertOrderLineStatusAndTrackingStatusToOrderLineStatusView } from '../../utils/utils'

interface Props {
  orderLine: OrderLine
  defaultButtonVariant?: ButtonVariant
}

export const BuyAgainButton = ({ orderLine, defaultButtonVariant }: Props) => {
  const { t } = useAccountTranslation()
  const market = useMarket()
  const webAppBuyerUrl = getAppBuyerUrl(market)
  const { trackBuyAgain } = useOrdersHistoryGAEvents()

  let buttonVariant = defaultButtonVariant

  const goToBuyAgain = (): void => {
    trackBuyAgain()
    window.location.href = `${webAppBuyerUrl}/account/buy-again?id=${orderLine.productDetails.productId}`
  }

  let showBuyAgainButton = false

  const statusSet = new Set(
    orderLine.trackingDetails?.map((_, index) =>
      convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(
        orderLine,
        index
      )
    ) ?? [
      convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(orderLine, 0)
    ]
  )

  if (statusSet.size > 0) {
    const status = statusSet.values().next().value

    showBuyAgainButton = !!buyAgainButtonVariant[status]
    buttonVariant ??= buyAgainButtonVariant[status]
  }

  if (statusSet.size > 1) {
    for (const status of statusSet) {
      showBuyAgainButton = !!buyAgainButtonVariant[status]
      if (!!!buyAgainButtonVariant[status]) {
        break
      }
    }
  }

  if (
    statusSet.has(OrderLineStatusView.returnRequested) &&
    !orderLine.productDetails.isBulky
  ) {
    showBuyAgainButton = false
  }

  if (!showBuyAgainButton) {
    return <></>
  }

  return (
    <Button
      variant={buttonVariant}
      className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px] leading-[28px]"
      data-testId="order-buy-again"
      onClick={goToBuyAgain}
      rounded
    >
      {t('PAGES.ACCOUNT.ORDER_HISTORY.BUY_AGAIN')}
    </Button>
  )
}
