import { useRef, useState } from 'react'

import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { OrderHistoryItem } from '../../types'
import {
  convertOrderLineStatusAndTrackingStatusToOrderLineStatusView,
  isAllowedToCancel
} from '../../utils/utils'
import { OrderDetailCancelModal } from '../OrderDetailCancelModal/OrderDetailCancelModal'

interface Props {
  order: OrderHistoryItem
  onCancelled: () => void
}

export const CancelAllItemsButton = ({ order, onCancelled }: Props) => {
  const { t } = useAccountTranslation()
  const [isShowingModal, setIsShowingModal] = useState(false)

  const buttonRef = useRef<HTMLButtonElement>(null)

  const isShowingCancelAllItemsButton = order.orderLines.every((orderLine) => {
    const orderLineStatusView =
      convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(orderLine, 0)

    return isAllowedToCancel(orderLineStatusView)
  })

  const closeModal = () => {
    setIsShowingModal(false)
    buttonRef?.current?.focus()
  }

  if (!isShowingCancelAllItemsButton) {
    return <></>
  }

  return (
    <>
      <Button
        variant={BUTTON_VARIANTS.infoAccessible}
        className="flex justify-center items-center w-full lg:w-auto lg:!w-[240px] font-bold text-regular font-semibold whitespace-nowrap h-[40px] mb-3"
        data-testId="cancel-all-items"
        onClick={() => setIsShowingModal(true)}
        rounded
        ref={buttonRef}
      >
        {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.CANCEL.ORDER')}
      </Button>

      {isShowingModal && (
        <OrderDetailCancelModal
          order={order}
          onCancelled={onCancelled}
          onClose={closeModal}
        />
      )}
    </>
  )
}
