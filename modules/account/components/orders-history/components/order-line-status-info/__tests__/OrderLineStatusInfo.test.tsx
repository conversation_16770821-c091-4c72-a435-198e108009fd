import {
  fireEvent,
  render,
  screen,
  waitFor,
  within
} from '@testing-library/react'

import { config } from '@core/config'
import { useIsMobile } from '@core/hooks/useIsMobile'

// eslint-disable-next-line no-restricted-imports
import {
  CancellationReasons,
  DeliveryTracking,
  OrderLine,
  OrderStateStatus,
  PaymentStatus,
  TrackingStatus
} from '../../../types'
// eslint-disable-next-line no-restricted-imports
import { CancelledBy } from '../../../types/cancelled-by.enum'
// eslint-disable-next-line no-restricted-imports
import { formatDate, formatDateRange } from '../../../utils/utils'
import { orderLine, tracking } from '../../__tests__/fixtures'
import { getFirstDayOfNextMonth } from '../../__tests__/utils'
import { OrderLineStatusInfo } from '../OrderLineStatusInfo'

jest.mock('@core/hooks', () => ({
  useMarket: jest.fn(() => 'en-GB')
}))
jest.mock('@core/hooks/useIsMobile')
const mockedUseIsMobile = useIsMobile as jest.Mock

describe('Each order line status', () => {
  function checkStatusBackgroundAndTitle(
    orderLine: OrderLine,
    tracking: DeliveryTracking | undefined,
    expected: {
      statusLabel?: string
      className?: string
      info?: string
      extraInfo?: string
      delayedMessage?: string
      isViewReasonVisibleReturn?: boolean
      isViewReasonVisibleCancellation?: boolean
      topInfo?: string
    }
  ) {
    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          trackingDetails: tracking ? [tracking] : []
        }}
        trackingIndex={0}
      />
    )

    if (expected.statusLabel) {
      expect(
        screen.getByTestId('order-line-status-info-label')
      ).toHaveTextContent(expected.statusLabel)
    } else {
      expect(
        screen.queryByTestId('order-line-status-info-label')
      ).not.toBeInTheDocument()
    }
    if (expected.className) {
      expect(screen.getByTestId('order-line-status-info-label')).toHaveClass(
        expected.className
      )
    }
    if (expected.info) {
      expect(
        screen.getByTestId('order-line-status-info-extra')
      ).toBeInTheDocument()
      expect(
        screen.getByTestId('order-line-status-info-extra')
      ).toHaveTextContent(expected.info)
    } else {
      expect(
        screen.queryByTestId('order-line-status-info-extra')
      ).not.toBeInTheDocument()
    }
    if (expected.extraInfo) {
      expect(
        screen.getByTestId('order-line-status-info-label-date')
      ).toBeInTheDocument()
      expect(
        screen.getByTestId('order-line-status-info-label-date')
      ).toHaveTextContent(expected.extraInfo)
    } else {
      expect(
        screen.queryByTestId('order-line-status-info-label-date')
      ).not.toBeInTheDocument()
    }
    if (expected.delayedMessage) {
      expect(
        screen.getByTestId('order-line-status-info-delayed')
      ).toBeInTheDocument()
      expect(
        screen.getByTestId('order-line-status-info-delayed')
      ).toHaveTextContent(expected.delayedMessage)
    } else {
      expect(
        screen.queryByTestId('order-line-status-info-delayed')
      ).not.toBeInTheDocument()
    }

    if (expected.isViewReasonVisibleReturn) {
      expect(
        screen.getByTestId('order-line-status-view-reasons-return')
      ).toBeInTheDocument()
    } else {
      expect(
        screen.queryByTestId('order-line-status-view-reasons-return')
      ).not.toBeInTheDocument()
    }

    if (expected.isViewReasonVisibleCancellation) {
      expect(
        screen.getByTestId('order-line-status-view-reasons-cancellation')
      ).toBeInTheDocument()
    } else {
      expect(
        screen.queryByTestId('order-line-status-view-reasons-cancellation')
      ).not.toBeInTheDocument()
    }
  }

  // Phase 00
  it('waiting_for_payment', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.WAITING_FOR_PAYMENT,
          title: 'Waiting processing'
        },
        payment: {
          status: PaymentStatus.Pending,
          name: ''
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_PAYMENT',
        className: 'bg-grey-tint-90'
      }
    )
  })

  it('Payment failed', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.WAITING_FOR_PAYMENT,
          title: 'Cancelled'
        },
        payment: {
          status: PaymentStatus.Error,
          name: ''
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.PAYMENT_FAILED',
        className: 'bg-red-tint-80'
      }
    )
  })

  // Phase 01
  it('waiting_for_approval', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.WAITING_FOR_APPROVAL,
          title: 'Pending approval'
        },
        payment: {
          status: PaymentStatus.Pending,
          name: ''
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_APPROVAL',
        className: 'bg-yellow-tint-80'
      }
    )
  })

  it('paid', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.PAID,
          title: 'Payment processing'
        },
        payment: {
          status: PaymentStatus.Pending,
          name: ''
        }
      },
      undefined,
      {
        statusLabel: 'Payment processing',
        className: 'bg-grey-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PAID'
      }
    )
  })

  // Phase 02
  it('should show Order rejected when status is CANCELED and reason is rejected with grey background and extra info', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CANCELED,
          title: 'Order rejected'
        },
        payment: {
          status: PaymentStatus.Pending,
          name: ''
        },
        cancellationDetails: {
          cancelledAt: null,
          cancelledBy: CancelledBy.buyer,
          cancellationReason: {
            value: CancellationReasons.REJECTED,
            title: ''
          }
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REJECTED',
        className: 'bg-red-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REJECTED'
      }
    )
  })

  it('placed and date is before expected initial date of delivery period', async () => {
    const date = getFirstDayOfNextMonth()
    const formattedDateStart = date.toISOString()
    const dateEnd = getFirstDayOfNextMonth()
    dateEnd.setDate(dateEnd.getDate() + 10)
    const formattedDateEnd = dateEnd.toISOString()

    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.PLACED,
          title: 'Order placed'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDateStart,
            end: formattedDateEnd
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED',
        className: 'bg-green-tint-80',
        info: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY ${formatDateRange(date, dateEnd, 'en-GB' as any)}`
      }
    )
  })

  it('canceled security check failed', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CANCELED,
          title: 'Order cancelled'
        },
        payment: {
          status: PaymentStatus.Failed,
          name: ''
        },
        cancellationDetails: {
          cancellationReason: {
            value: CancellationReasons.FRAUD_SUSPICIOUS,
            title:
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SECURITY'
          },
          cancelledBy: null,
          cancelledAt: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
        className: 'bg-red-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SECURITY'
      }
    )
  })

  // Phase 03
  it('canceled (by buyer)', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CANCELED,
          title: 'Order cancelled'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        cancellationDetails: {
          cancelledAt: null,
          cancelledBy: CancelledBy.buyer
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
        className: 'bg-red-tint-80'
      }
    )
  })

  it('placed (delayed) and date is after expected initial date of delivery period', async () => {
    const date = new Date()
    date.setDate(date.getDate() - 10)
    const formattedDate = date.toISOString()
    const dateEnd = new Date()
    dateEnd.setDate(dateEnd.getDate() - 5)
    const formattedDateEnd = dateEnd.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.PLACED,
          title: 'Placed'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDate,
            end: formattedDateEnd
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED',
        className: 'bg-orange-tint-80',
        delayedMessage: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED.EXPECTED ${formatDateRange(
          date,
          dateEnd,
          'en-GB' as any
        )} (PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.DELAYED)`,
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED'
      }
    )
  })

  it('confirmed ', async () => {
    const date = getFirstDayOfNextMonth()
    const formattedDateStart = date.toISOString()
    const dateEnd = getFirstDayOfNextMonth()
    dateEnd.setDate(dateEnd.getDate() + 10)
    const formattedDateEnd = dateEnd.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CONFIRMED,
          title: 'Order placed'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDateStart,
            end: formattedDateEnd
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED',
        className: 'bg-green-tint-80',
        info: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY ${formatDateRange(date, dateEnd, 'en-GB' as any)}`
      }
    )
  })

  it('confirmed (delayed) and date is after expected initial date of delivery period', async () => {
    const date = new Date()
    date.setDate(date.getDate() - 10)
    const formattedDate = date.toISOString()
    const dateEnd = new Date()
    dateEnd.setDate(dateEnd.getDate() - 5)
    const formattedDateEnd = dateEnd.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CONFIRMED,
          title: 'Placed'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDate,
            end: formattedDateEnd
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED',
        className: 'bg-orange-tint-80',
        delayedMessage: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED.EXPECTED ${formatDateRange(
          date,
          dateEnd,
          'en-GB' as any
        )} (PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.DELAYED)`,
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED'
      }
    )
  })

  it('Canceled by the seller', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.CANCELED,
          title: 'Order cancelled'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        cancellationDetails: {
          cancellationReason: {
            value: CancellationReasons.NO_INVENTORY,
            title: 'Modal info refund has been done'
          },
          cancelledBy: CancelledBy.seller,
          cancelledAt: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED',
        className: 'bg-red-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SELLER',
        isViewReasonVisibleCancellation: true
      }
    )
  })

  // Phase 05
  it('shipped with integrated carrier', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Processing'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.IN_PROGRESS
      },
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.READY_SHIP',
        className: 'bg-green-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY 28 - 31 Jul'
      }
    )
  })

  it('shipped without integrated carrier when tracking status is SUBSCRIPTION_FAILED', async () => {
    const date = new Date()
    date.setDate(date.getDate() + 10)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDate,
            end: formattedDate
          },
          type: null
        },
        trackingDetails: [
          {
            ...tracking,
            trackingStatus: TrackingStatus.SUBSCRIPTION_FAILED
          }
        ]
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED',
        className: 'bg-green-tint-80',
        info: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY ${formatDateRange(date, date, 'en-GB' as any)}`
      }
    )
  })
  it('shipped without integrated carrier', async () => {
    const date = new Date()
    date.setDate(date.getDate() + 10)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: formattedDate,
            end: formattedDate
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED',
        className: 'bg-green-tint-80',
        info: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY ${formatDateRange(date, date, 'en-GB' as any)}`
      }
    )
  })

  // TODO
  it.skip('shipped without integrated carrier without getting tracking udpates', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Ready to ship'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        trackingDetails: [
          {
            ...tracking,
            trackingStatus: TrackingStatus.TRACKING_FAILED
          }
        ]
      },
      undefined,
      {
        statusLabel: 'Ready to ship',
        className: 'bg-green-tint-80',
        info: 'Expected delivery: 28 - 31 Jul',
        topInfo:
          'We stopped getting updates from the carrier. Please track your item or reach out to the seller'
      }
    )
  })

  // Phase 0.6 TODO

  // Phase 07.1
  it('shipped with integrated carrier delivered (first 14 days)', async () => {
    const date = new Date()
    date.setDate(date.getDate() - 10)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },

        deliveryDetails: {
          expected: {
            start: formattedDate,
            end: formattedDate
          },
          type: null
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.DELIVERY_SUCCESS
      },
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED',
        className: 'bg-green-tint-80',
        extraInfo: formatDate(date, 'en-GB' as any)
      }
    )
  })

  it('shipped with integrated carrier delivered (first 14 days) should use deliveredAt', async () => {
    const date = new Date()
    date.setDate(date.getDate() - 1)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.DELIVERY_SUCCESS,
        deliveredAt: formattedDate
      },
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED',
        className: 'bg-green-tint-80',
        extraInfo: formatDate(date, 'en-GB' as any)
      }
    )
  })

  it('shipped without integrated carrier (before end day)', async () => {
    const date = new Date()
    date.setDate(date.getDate() + 10)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        deliveryDetails: {
          expected: {
            start: orderLine.deliveryDetails.expected.start,
            end: formattedDate
          },
          type: null
        }
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED',
        className: 'bg-green-tint-80',
        info: `PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY ${formatDateRange(new Date(orderLine.deliveryDetails.expected!.start!), date, 'en-GB' as any)}`
      }
    )
  })

  // Phase 07.2
  it('shipped with integrated carrier delivered (after 14 days)', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.DELIVERY_SUCCESS
      },
      {
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_ON 31 Jul, 2024'
      }
    )
  })

  it('shipped with integrated carrier delivered (after 14 days) should use delivered at', async () => {
    const date = new Date(orderLine.deliveryDetails.expected.start)
    date.setDate(date.getDate() - 1)
    const formattedDate = date.toISOString()
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.DELIVERY_SUCCESS,
        deliveredAt: formattedDate
      },
      {
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_ON 27 Jul, 2024'
      }
    )
  })

  it('shipped without integrated carrier (after 14 days)', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      undefined,
      {
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_BY 31 Jul, 2024'
      }
    )
  })

  // Phase 07.3
  it('shipped with integrated carrier delivery failed', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.SHIPPED,
          title: 'Shipped'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      {
        ...tracking,
        trackingStatus: TrackingStatus.DELIVERY_FAILED
      },
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_FAILED',
        className: 'bg-orange-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.SHIPPED_FAILED'
      }
    )
  })

  // Phase 8
  it('return requested', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.RETURN_REQUESTED,
          title: 'Return started'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      undefined,
      {
        statusLabel: 'Return started',
        className: 'bg-green-tint-80'
      }
    )
  })

  it('Return started bulky item have return instructions', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.RETURN_REQUESTED,
          title: 'Return started'
        },
        productDetails: {
          ...orderLine.productDetails,
          isBulky: true
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      undefined,
      {
        statusLabel: 'Return ',
        className: 'bg-green-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.VIEW_INSTRUCTIONS'
      }
    )
  })

  it('should show return instructions sidebar when view return instructions is clicked', async () => {
    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          status: {
            value: OrderStateStatus.RETURN_REQUESTED,
            title: 'Return accepted'
          },
          productDetails: {
            ...orderLine.productDetails,
            isBulky: true
          },
          payment: {
            status: PaymentStatus.Authorized,
            name: ''
          }
        }}
      />
    )
    screen.getByTestId('order-line-status-view-instructions').click()
    expect(await screen.findByTestId('order-sidebar')).toBeInTheDocument()
    expect(
      within(await screen.findByTestId('order-sidebar')).getByText(
        'PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.TITLE'
      )
    ).toBeInTheDocument()
  })

  // Phase 9
  it('return declined', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.RETURN_DECLINED,
          title: 'Return declined'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      undefined,
      {
        statusLabel: 'Return declined',
        className: 'bg-red-tint-80',
        isViewReasonVisibleReturn: true
      }
    )
  })

  it('return accepted', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.RETURN_ACCEPTED,
          title: 'Returned'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        }
      },
      undefined,
      {
        statusLabel: 'Returned',
        className: 'bg-green-tint-80',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.RETURNED'
      }
    )
  })

  // Phase 10
  it('Refunded', async () => {
    checkStatusBackgroundAndTitle(
      {
        ...orderLine,
        status: {
          value: OrderStateStatus.RETURN_ACCEPTED,
          title: 'Return accepted'
        },
        payment: {
          status: PaymentStatus.Authorized,
          name: ''
        },
        accountingStatus: 'refunded'
      },
      undefined,
      {
        statusLabel:
          'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REFUNDED',
        info: 'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REFUNDED'
      }
    )
  })
})

describe('view reason', () => {
  it('should show the modal with the cancellation reasons when view reason is clicked', async () => {
    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          status: {
            value: OrderStateStatus.CANCELED,
            title: 'Devoluci\u00f3n'
          },
          payment: {
            status: PaymentStatus.Authorized,
            name: ''
          },
          cancellationDetails: {
            cancellationReason: {
              value: CancellationReasons.NO_INVENTORY,
              title: 'Cancellation reason title'
            },
            cancelledBy: CancelledBy.seller,
            cancelledAt: null
          }
        }}
      />
    )
    screen.getByTestId('order-line-status-view-reasons-cancellation').click()
    expect(
      within(await screen.findByTestId('side-bar')).getByText(
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.CANCELLATION_REASONS'
      )
    ).toBeInTheDocument()
    expect(
      within(await screen.findByTestId('side-bar')).getByText(
        'Cancellation reason title'
      )
    ).toBeInTheDocument()
  })

  it('should show the modal with the return reasons when view reason is clicked', async () => {
    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          status: {
            value: OrderStateStatus.RETURN_DECLINED,
            title: 'Devoluci\u00f3n'
          },
          payment: {
            status: PaymentStatus.Authorized,
            name: ''
          },
          returnDetails: {
            returnResolution: 'Return declined',
            returnReasonTitle: '',
            returnReasonValue: null
          }
        }}
      />
    )
    screen.getByTestId('order-line-status-view-reasons-return').click()
    expect(
      within(await screen.findByTestId('side-bar')).getByText(
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN_REJECTED_REASONS'
      )
    ).toBeInTheDocument()
    expect(
      within(await screen.findByTestId('side-bar')).getByText('Return declined')
    ).toBeInTheDocument()
  })

  it.only('should focus the button when the button is closed by the enter', async () => {
    mockedUseIsMobile.mockReturnValue(false)

    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          status: {
            value: OrderStateStatus.RETURN_DECLINED,
            title: 'Devoluci\u00f3n'
          },
          payment: {
            status: PaymentStatus.Authorized,
            name: ''
          },
          returnDetails: {
            returnResolution: 'Return declined',
            returnReasonTitle: '',
            returnReasonValue: null
          }
        }}
      />
    )
    const button = screen.getByTestId('order-line-status-view-reasons-return')
    button.click()
    await waitFor(() => {
      expect(screen.getByTestId('popup-modal')).toBeInTheDocument()
    })
    screen.getByTestId('close-button').click()

    expect(button).toHaveFocus()
  })

  it('should show the modal with the cancellation reasons when view reason is activated via keyboard', async () => {
    render(
      <OrderLineStatusInfo
        orderLine={{
          ...orderLine,
          status: {
            value: OrderStateStatus.CANCELED,
            title: 'Devoluci\u00f3n'
          },
          payment: {
            status: PaymentStatus.Authorized,
            name: ''
          },
          cancellationDetails: {
            cancellationReason: {
              value: CancellationReasons.NO_INVENTORY,
              title: 'Cancellation reason title'
            },
            cancelledBy: CancelledBy.seller,
            cancelledAt: null
          }
        }}
      />
    )
    const viewReasonLink = screen.getByTestId(
      'order-line-status-view-reasons-cancellation'
    )
    fireEvent.keyDown(viewReasonLink, { key: 'Enter', code: 'Enter' })
    expect(
      within(await screen.findByTestId('side-bar')).getByText(
        'Cancellation reason title'
      )
    ).toBeInTheDocument()
  })
})

describe('Plant disclaimer', () => {
  it('should show the plant category disclaimer if isPlantCategory is true', () => {
    const orderLineInfo: OrderLine = {
      ...orderLine,
      sellerDetails: {
        ...orderLine.sellerDetails,
        id: config.METRO_ID
      },
      status: {
        ...orderLine.status,
        value: OrderStateStatus.RETURN_REQUESTED
      },
      productDetails: {
        ...orderLine.productDetails,
        isPlantCategory: true
      }
    }

    render(<OrderLineStatusInfo orderLine={orderLineInfo} />)
    expect(
      screen.getByTestId('order-line-status-return-content-plant')
    ).toBeInTheDocument()
  })

  it('should not show the plant category disclaimer if isPlantCategory is true', () => {
    const orderLineInfo: OrderLine = {
      ...orderLine,
      sellerDetails: {
        ...orderLine.sellerDetails,
        id: config.METRO_ID
      },
      status: {
        ...orderLine.status,
        value: OrderStateStatus.RETURN_REQUESTED
      },
      productDetails: {
        ...orderLine.productDetails,
        isPlantCategory: false
      }
    }

    render(<OrderLineStatusInfo orderLine={orderLineInfo} />)
    expect(
      screen.queryByTestId('order-line-status-return-content-plant')
    ).not.toBeInTheDocument()
  })

  it('should not bulky the plant category disclaimer if isPlantCategory is true and bulky is true', () => {
    const orderLineInfo: OrderLine = {
      ...orderLine,
      sellerDetails: {
        ...orderLine.sellerDetails,
        id: config.METRO_ID
      },
      status: {
        ...orderLine.status,
        value: OrderStateStatus.RETURN_REQUESTED
      },
      productDetails: {
        ...orderLine.productDetails,
        isPlantCategory: true,
        isBulky: true
      }
    }

    render(<OrderLineStatusInfo orderLine={orderLineInfo} />)
    expect(
      screen.getByTestId('order-line-status-return-content-plant')
    ).toBeInTheDocument()
    expect(
      screen.queryByTestId('order-line-status-info-extra')
    ).not.toBeInTheDocument()
  })
})
