import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useDispatch } from 'react-redux'

import {
  OrderLine,
  OrderStateStatus
} from '@modules/account/components/orders-history/types'

import { orderDocumentItem, orderLine } from '../../__tests__/fixtures'
import { ReturnLabelActions } from '../ReturnLabelActions'

jest.mock(
  '@modules/account/components/orders-history/services/orders-history.service',
  () => ({
    downloadDocuments: jest.fn()
  })
)
jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))
jest.mock('@core/services/http/http-request', () => ({
  http: jest.fn()
}))
jest.mock('@core/redux/features/market/useLocale', () => ({
  useLocale: () => 'de-DE'
}))
jest.mock('@core/hooks', () => ({
  useMarket: jest.fn(() => 'de')
}))
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}))
jest.mock(
  '@modules/account/components/orders-history/hooks/useActiveContext',
  () => ({
    useActiveContext: () => 'test-context'
  })
)

describe('ReturnLabelActions', () => {
  const dispatch = jest.fn()
  beforeEach(() => {
    useDispatch.mockReturnValue(dispatch)
  })
  const defaultOrderLine = {
    ...orderLine,
    status: { ...orderLine.status, value: OrderStateStatus.RETURN_REQUESTED },
    documents: { ...orderLine.documents, returnLabels: [orderDocumentItem] }
  }

  it('renders "Download return label" button when return label is available', () => {
    render(
      <ReturnLabelActions orderLine={defaultOrderLine} isHighlighted={false} />
    )

    const button = screen.getByTestId(
      'order-detail-order-line-actions-return-label'
    )
    expect(button).toBeInTheDocument()
  })

  it('should not render "Download return label" button when return label is available and is bulky', () => {
    render(
      <ReturnLabelActions
        orderLine={{
          ...defaultOrderLine,
          productDetails: {
            ...defaultOrderLine.productDetails,
            isBulky: true
          }
        }}
        isHighlighted={false}
      />
    )

    const button = screen.queryByTestId(
      'order-detail-order-line-actions-return-label'
    )
    expect(button).not.toBeInTheDocument()
  })

  it('renders "Return Label Pending" button when no return label is available', () => {
    render(
      <ReturnLabelActions
        orderLine={{
          ...defaultOrderLine,
          documents: {
            ...defaultOrderLine.documents,
            returnLabels: []
          }
        }}
        isHighlighted={false}
      />
    )

    const disabledButton = screen.getByTestId(
      'order-detail-order-line-actions-no-return'
    )
    expect(disabledButton).toBeInTheDocument()
    expect(disabledButton).toBeDisabled()

    const infoText = screen.getByTestId('order-line-status-info-extra')
    expect(infoText).toHaveTextContent(
      'PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.INFORMATION'
    )
  })

  it('renders the correct button variant when isHighlighted is true', () => {
    render(<ReturnLabelActions orderLine={defaultOrderLine} isHighlighted />)

    const button = screen.getByTestId(
      'order-detail-order-line-actions-return-label'
    )

    expect(button).toHaveClass('bg-secondary-main')
  })

  it('does not render anything if status is not RETURN_REQUESTED', () => {
    const orderLineWithDifferentStatus = {
      ...orderLine,
      status: { ...orderLine.status, value: OrderStateStatus.CANCELED },
      documents: { ...orderLine.documents, returnLabels: [orderDocumentItem] }
    }

    render(
      <ReturnLabelActions
        orderLine={orderLineWithDifferentStatus}
        isHighlighted={false}
      />
    )

    expect(
      screen.queryByTestId('order-detail-order-line-actions-return-label')
    ).not.toBeInTheDocument()
    expect(
      screen.queryByTestId('order-line-status-view-instructions')
    ).not.toBeInTheDocument()
    expect(
      screen.queryByTestId('order-detail-order-line-actions-no-return')
    ).not.toBeInTheDocument()
  })

  it('should not render anything if status is RETURN_REQUESTED is the product is bulky', () => {
    const orderLineWithDifferentStatus: OrderLine = {
      ...orderLine,
      productDetails: {
        ...orderLine.productDetails,
        isBulky: true
      },
      status: { ...orderLine.status, value: OrderStateStatus.RETURN_REQUESTED },
      documents: { ...orderLine.documents, returnLabels: [orderDocumentItem] }
    }

    render(
      <ReturnLabelActions
        orderLine={orderLineWithDifferentStatus}
        isHighlighted={false}
      />
    )

    expect(
      screen.queryByTestId('order-detail-order-line-actions-return-label')
    ).not.toBeInTheDocument()
    expect(
      screen.queryByTestId('order-line-status-view-instructions')
    ).not.toBeInTheDocument()
    expect(
      screen.queryByTestId('order-detail-order-line-actions-no-return')
    ).not.toBeInTheDocument()
  })

  it('Should open the return label sidebar when cliking the view instrucctions link', async () => {
    render(
      <ReturnLabelActions orderLine={defaultOrderLine} isHighlighted={false} />
    )

    const button = screen.getByTestId('order-line-status-view-instructions')

    expect(screen.queryByTestId('order-sidebar')).not.toBeInTheDocument()

    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByTestId('order-sidebar')).toBeInTheDocument()
    })
  })

  it('Should open the return label sidebar when tapping enter', async () => {
    render(
      <ReturnLabelActions orderLine={defaultOrderLine} isHighlighted={false} />
    )

    const button = screen.getByTestId('order-line-status-view-instructions')

    await userEvent.type(button, 'Enter')

    await waitFor(() => {
      expect(screen.getByTestId('order-sidebar')).toBeInTheDocument()
    })
  })

  it('Should set the focus to the button when closing by tapping enter', async () => {
    render(
      <ReturnLabelActions orderLine={defaultOrderLine} isHighlighted={false} />
    )

    const button = screen.getByTestId('order-line-status-view-instructions')

    await userEvent.type(button, 'Enter')

    await waitFor(() => {
      expect(screen.getByTestId('order-sidebar')).toBeInTheDocument()
    })
    screen.getByTestId('order-sidebar-close').click()
    expect(button).toHaveFocus()
  })
})
