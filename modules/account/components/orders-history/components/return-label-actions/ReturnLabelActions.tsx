import { useRef, useState } from 'react'
import { useDispatch } from 'react-redux'

import { ordersHistoryDownloadDocumentsAlert } from '@core/alerts/alertObjects/ordersHistoryDownloadDocumentsAlert'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { addAlert } from '@core/redux/features/alerts/AlertSlice'
import { useLocale } from '@core/redux/features/market/useLocale'

import { Button, SVGIcon } from '@shared/components'
import { BUTTON_VARIANTS } from '@shared/components/Button/types/variant'
import { SVG_NAMES } from '@shared/icons/constants'

import { ReturnInstructionsSidebar } from '@modules/account/components/orders-history/components/return-instructions/ReturnInstructionsSidebar'
import { downloadDocuments } from '@modules/account/components/orders-history/services/orders-history.service'
import {
  OrderLine,
  OrderStateStatus
} from '@modules/account/components/orders-history/types'
import { handleEnterOrSpacePress } from '@modules/shared/utils/accessibility.utils'

import { useActiveContext } from '../../hooks/useActiveContext'

interface Props {
  orderLine: OrderLine
  isHighlighted: Boolean
}

export const ReturnLabelActions = ({ orderLine, isHighlighted }: Props) => {
  const { t } = useAccountTranslation()
  const activeContext = useActiveContext()
  const market = useMarket()
  const locale = useLocale()
  const dispatch = useDispatch()
  const [showReturnInstructionsSidebar, setShowReturnInstructionsSidebar] =
    useState(false)

  const downloadRturnLabelDocumentsButtonRef = useRef<HTMLButtonElement>(null)
  const showLinkViewReturnInstrucctionsButtonRef =
    useRef<HTMLButtonElement>(null)

  const isAllowedToDownloadReturnLabel =
    orderLine.status.value === OrderStateStatus.RETURN_REQUESTED &&
    orderLine.documents?.returnLabels?.length > 0

  const showNoReturnLabelButtonDisabled =
    orderLine.status.value === OrderStateStatus.RETURN_REQUESTED &&
    orderLine.documents?.returnLabels?.length === 0

  const showLinkViewReturnInstructions =
    orderLine.status.value === OrderStateStatus.RETURN_REQUESTED

  const downloadReturnLabelDocuments = async (): Promise<void> => {
    try {
      await downloadDocuments(
        market,
        locale,
        activeContext,
        orderLine.documents.returnLabels[0].type,
        orderLine.documents.returnLabels.map((returnLabel) => returnLabel.id),
        orderLine.documents.returnLabels.map((returnLabel) => returnLabel.name)
      )
    } catch (_) {
      dispatch(addAlert(ordersHistoryDownloadDocumentsAlert()))
    }
  }

  if (orderLine.productDetails.isBulky) {
    return <></>
  }

  return isAllowedToDownloadReturnLabel || showNoReturnLabelButtonDisabled ? (
    <div className="font-lato text-center space-y-2 flex flex-col lg:max-w-full">
      {isAllowedToDownloadReturnLabel && (
        <>
          <Button
            variant={
              isHighlighted
                ? BUTTON_VARIANTS.secondary
                : BUTTON_VARIANTS.infoAccessible
            }
            className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px] leading-[28px]"
            data-testId="order-detail-order-line-actions-return-label"
            onClick={downloadReturnLabelDocuments}
            ref={downloadRturnLabelDocumentsButtonRef}
            rounded
          >
            <SVGIcon
              name={SVG_NAMES.DOWNLOAD_ICON}
              className="mr-6px mt-2px font-lato font-bold text-sm"
              width="20px"
              height="20px"
              fill={isHighlighted ? '#FFF' : '#005AE0'}
            />
            <span
              className={`${!isHighlighted ? 'text-blue-interaction' : ''} font-lato`}
            >
              {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.DOWNLOAD')}
            </span>
          </Button>
        </>
      )}

      {showNoReturnLabelButtonDisabled && (
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            disabled={true}
            className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px] leading-[28px]"
            data-testId="order-detail-order-line-actions-no-return"
            rounded
          >
            {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.PENDING')}
          </Button>
          <span
            className="text-primary-main text-base leading-[21px] font-lato font-bold justify-center"
            data-testid="order-line-status-info-extra"
          >
            {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.INFORMATION')}
          </span>
        </>
      )}

      {showLinkViewReturnInstructions && (
        <div className="flex w-full justify-center">
          <button
            data-testid="order-line-status-view-instructions"
            className="text-blue-main text-base cursor-pointer bg-none border-none p-0 m-0 appearance-none w-fit"
            onClick={(e) => {
              e.preventDefault()
              setShowReturnInstructionsSidebar(true)
            }}
            onKeyDown={handleEnterOrSpacePress(() =>
              setShowReturnInstructionsSidebar(true)
            )}
            ref={showLinkViewReturnInstrucctionsButtonRef}
          >
            {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.VIEW_INSTRUCTIONS')}
          </button>
        </div>
      )}

      {showReturnInstructionsSidebar && (
        <ReturnInstructionsSidebar
          onClose={() => {
            setShowReturnInstructionsSidebar(false)
            showLinkViewReturnInstrucctionsButtonRef?.current.focus()
          }}
        />
      )}
    </div>
  ) : (
    <></>
  )
}
