import { fireEvent, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'

import { http } from '@core/services/http/http-request'
import { renderWithProviders } from '@core/utils/testing'

// eslint-disable-next-line no-restricted-imports
import { useOrdersHistoryGAEvents } from '../../../hooks/useOrdersHistoryGAEvents'
import { OrderDetailOrderLineContactSeller } from '../OrderDetailOrderLineContactSeller'

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    buyerGateway: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    svcBuyerAccountBaseUrl: 'http://mock-buyer-url'
  }
}))

jest.mock('../../../hooks/useOrdersHistoryGAEvents')

jest.mock('@core/services/http/http-request')

describe('OrderDetailOrderLineContactSeller', () => {
  const mockProps = {
    orderId: 'order123',
    orderNumber: 'ON123',
    orderLine: {
      sellerDetails: {
        id: 'metro123',
        email: '<EMAIL>'
      }
    }
  }

  const mockPush = jest.fn()
  const mockTrackContactSupport = jest.fn()
  const mockHttp = http as jest.MockedFunction<typeof http>

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock window.location.href to prevent JSDOM navigation error
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    })

    // Mock window.open to track navigation
    window.open = jest.fn()

    useRouter.mockReturnValue({
      push: mockPush,
      query: { context: 'personal' }
    })

    useOrdersHistoryGAEvents.mockReturnValue({
      trackContactSupport: mockTrackContactSupport,
      trackContactSeller: jest.fn()
    })
  })

  it('should redirect to message center when isMessageCenterC2AFeatureEnabled is true and order is eligible', async () => {
    // Mock successful eligibility response
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] has been verified successfully.',
        data: {
          eligible: true
        }
      }
    } as any)

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />,
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    // Wait for eligibility check to complete
    await waitFor(() => {
      expect(mockHttp).toHaveBeenCalledWith(
        expect.stringContaining('/app-api/message-center/get-eligibility'),
        expect.objectContaining({
          method: 'GET',
          params: {
            organizationId: 'metro123',
            orderNumber: 'ON123'
          }
        }),
        expect.objectContaining({
          market: 'de',
          locale: 'de'
        })
      )
    })

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).toContain(
        '/message-center/order123/metro123'
      )
    })
  })

  it('should not redirect to message center when feature flag is enabled but order is not eligible', async () => {
    // Mock eligibility response with eligible: false
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] is not eligible.',
        data: {
          eligible: false
        }
      }
    } as any)

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />,
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    // Wait for eligibility check to complete
    await waitFor(() => {
      expect(mockHttp).toHaveBeenCalled()
    })

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })

  it('should not redirect to message center when isMessageCenterC2AFeatureEnabled is false', async () => {
    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />,
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: false
            }
          }
        }
      }
    )

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  it('should not redirect to message center when eligibility check fails', async () => {
    // Mock failed eligibility response
    mockHttp.mockRejectedValue(new Error('Network error'))

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />,
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    // Wait for eligibility check to complete
    await waitFor(() => {
      expect(mockHttp).toHaveBeenCalled()
    })

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })
})
