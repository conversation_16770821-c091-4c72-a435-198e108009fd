import { fireEvent, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'

import { renderWithProviders } from '@core/utils/testing'

// eslint-disable-next-line no-restricted-imports
import { useMessageCenterEligibility } from '../../../hooks/useMessageCenterEligibility'
// eslint-disable-next-line no-restricted-imports
import { useOrdersHistoryGAEvents } from '../../../hooks/useOrdersHistoryGAEvents'
import { OrderDetailOrderLineContactSeller } from '../OrderDetailOrderLineContactSeller'

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    buyerGateway: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    svcBuyerAccountBaseUrl: 'http://mock-buyer-url'
  }
}))

jest.mock('../../../hooks/useOrdersHistoryGAEvents')
jest.mock('../../../hooks/useMessageCenterEligibility')

describe('OrderDetailOrderLineContactSeller', () => {
  const mockProps = {
    orderId: 'order123',
    orderNumber: 'ON123',
    orderLine: {
      sellerDetails: {
        id: 'metro123',
        email: '<EMAIL>'
      }
    }
  }

  const mockPush = jest.fn()
  const mockTrackContactSupport = jest.fn()
  const mockUseMessageCenterEligibility =
    useMessageCenterEligibility as jest.MockedFunction<
      typeof useMessageCenterEligibility
    >
  const mockUseOrdersHistoryGAEvents =
    useOrdersHistoryGAEvents as jest.MockedFunction<
      typeof useOrdersHistoryGAEvents
    >
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock window.location.href to prevent JSDOM navigation error
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    })

    // Mock window.open to track navigation
    window.open = jest.fn()

    mockUseRouter.mockReturnValue({
      push: mockPush,
      query: { context: 'personal' }
    } as any)

    mockUseOrdersHistoryGAEvents.mockReturnValue({
      trackContactSupport: mockTrackContactSupport,
      trackContactSeller: jest.fn()
    } as any)
  })

  it('should redirect to message center when canRedirectToMessageCenter is true', async () => {
    // Mock the hook to return canRedirectToMessageCenter: true
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: true,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: true,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).toContain(
        '/message-center/order123/metro123'
      )
    })

    expect(mockUseMessageCenterEligibility).toHaveBeenCalledWith({
      organizationId: 'metro123',
      orderNumber: 'ON123'
    })
  })

  it('should not redirect to message center when canRedirectToMessageCenter is false', async () => {
    // Mock the hook to return canRedirectToMessageCenter: false
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })

  it('should not redirect to message center when feature flag is disabled', async () => {
    // Mock the hook to return feature flag disabled
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: false
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  it('should not redirect to message center when there is an error', async () => {
    // Mock the hook to return an error state
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: new Error('Network error'),
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(screen.getByTestId('dropdown-button'))

    fireEvent.click(
      // eslint-disable-next-line testing-library/no-node-access
      screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })

  it('should disable button when isCheckingEligibility is true', () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: true,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    expect(screen.getByTestId('dropdown-button')).toBeDisabled()
  })

  it('should disable METRO button when isCheckingEligibility is true', () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: true,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    const metroProps = {
      ...mockProps,
      orderLine: {
        ...mockProps.orderLine,
        sellerDetails: {
          ...mockProps.orderLine.sellerDetails,
          id: 'METRO_ID' // Assuming config.METRO_ID is 'METRO_ID' for testing
        }
      }
    }

    // Mock config for METRO_ID if it's used directly in the component
    jest.mock('@core/config', () => ({
      config: { METRO_ID: 'METRO_ID' },
      getAppBuyerUrl: jest.fn(() => 'http://mock-buyer-url')
    }))

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(metroProps as any)} />
    )
    expect(
      screen.getByTestId('order-detail-order-line-contact-seller-metro')
    ).toBeDisabled()
  })
})
