import { useState } from 'react'
import { v4 as uuidv4 } from 'uuid'

import { CloseIconButton } from '@core/components/CloseIconButton'
import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button, SVGIcon } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { Sidebar } from '@modules/shared/components/Sidebar'
import { SVG_NAMES } from '@modules/shared/icons/constants'
import { Option } from '@modules/shared/types'

import { CONTACT_SELLER_OPTIONS } from '../../constants'

interface Props {
  onClickButton: (option: Option) => void
}

export const OrderDetailOrderLineContactSellerMobile = ({
  onClickButton
}: Props) => {
  const { t } = useAccountTranslation()

  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  return (
    <>
      <Button
        variant={BUTTON_VARIANTS.infoAccessible}
        className="flex justify-center text-regular w-full font-semibold font-lato items-center min-h-[40px]"
        data-testid="order-detail-order-line-contact-seller-mobile-button"
        onClick={() => setIsSidebarOpen(true)}
        rounded
      >
        <span>
          {t(
            'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER'
          )}
        </span>
        <SVGIcon
          name={SVG_NAMES.CHEVRON_DOWN}
          stroke="none"
          fill="#0064FE"
          width="20px"
          height="20px"
          viewBox="0 0 28 28"
          className="ml-6px mb-[4px]"
        />
      </Button>
      <Sidebar
        isInDom={isSidebarOpen}
        isSidebarOpen={isSidebarOpen}
        closeOverlay={() => setIsSidebarOpen(false)}
        variant="mobile-short"
        stickyToBottom
        defaultClose={false}
        wrapperClassnames="h-min"
      >
        <div className="px-6 pt-6 font-lato mb-6">
          <div className="flex mb-6 items-center">
            <CloseIconButton onClick={() => setIsSidebarOpen(false)} />
            <span className="text-blue-main text-regular ml-2">
              {t('PAGES.ACCOUNT.ORDER_HISTORY')}
            </span>
          </div>
          <div className="flex flex-col space-y-4">
            {CONTACT_SELLER_OPTIONS.map((option) => (
              <Button
                key={uuidv4()}
                variant={BUTTON_VARIANTS.infoAccessible}
                className="w-full text-regular min-h-[40px]"
                onClick={() => {
                  onClickButton(option)
                  setIsSidebarOpen(false)
                }}
                rounded
              >
                {t(option.value)}
              </Button>
            ))}
          </div>
        </div>
      </Sidebar>
    </>
  )
}
