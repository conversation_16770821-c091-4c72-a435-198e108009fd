import { config } from '@core/config'
import { getAppBuyerUrl } from '@core/config/url'
import { APP_MESSAGE_CENTER } from '@core/constants/routesConstants'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { useLocale } from '@core/redux/features/market/useLocale'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { Dropdown } from '@modules/shared/components/Dropdown'
import { Option } from '@modules/shared/types'

import { CONTACT_SELLER_OPTIONS } from '../../constants'
import { useActiveContext } from '../../hooks/useActiveContext'
import { useOrdersHistoryGAEvents } from '../../hooks/useOrdersHistoryGAEvents'
import { getLoginToSSOUrl } from '../../services/orders-history.service'
import { OrderLine } from '../../types'
import { OrderDetailOrderLineContactSellerMobile } from './OrderDetailOrderLineContactSellerMobile'

interface Props {
  orderId: string
  orderNumber: string
  orderLine: OrderLine
}

export const OrderDetailOrderLineContactSeller = ({
  orderId,
  orderNumber,
  orderLine
}: Props) => {
  const market = useMarket()
  const locale = useLocale()
  const { t } = useAccountTranslation()
  const activeContext = useActiveContext()
  const { trackContactSupport, trackContactSeller } = useOrdersHistoryGAEvents()

  const isMetroSeller = orderLine.sellerDetails.id === config.METRO_ID
  const isMessageCenterC2AFeatureEnabled = useFeatureFlag(
    FeatureFlag.FF_CCS_MESSAGE_CENTER_ORDER_C2A
  )
  const webAppBuyerUrl = getAppBuyerUrl(market)

  const contactMessageCenter = () => {
    if (isMessageCenterC2AFeatureEnabled) {
      window.location.href = `${webAppBuyerUrl}/${APP_MESSAGE_CENTER}/${orderId}/${orderLine.sellerDetails.id}`
    }
    return isMessageCenterC2AFeatureEnabled
  }
  const handleMetroSellerButtonClick = async () => {
    trackContactSupport()
    if (!contactMessageCenter()) {
      await redirectToSSOLogin()
    }
  }

  const handleSellerButtonClick = (option: Option) => {
    trackContactSeller()
    if (!contactMessageCenter()) {
      openEmailClient(option)
    }
  }

  const redirectToSSOLogin = async () => {
    const redirectUrl = await getLoginToSSOUrl(market, locale, activeContext)
    window.open(redirectUrl, '_blank')
  }

  const openEmailClient = (option: Option) => {
    if (option.id === 1) {
      handleSendEmail(
        orderLine.sellerDetails.email,
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT',
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY'
      )
    } else if (option.id === 2) {
      handleSendEmail(
        orderLine.sellerDetails.email,
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT',
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY'
      )
    }
  }

  const handleSendEmail = (email: string, subject: string, body: string) => {
    const mailtoLink = `mailto:${email}?subject=${t(subject, {
      orderNumber: orderNumber
    })}&body=${t(body, {
      orderNumber: orderNumber
    })}`
    window.location.href = mailtoLink
  }

  return (
    <>
      {isMetroSeller ? (
        <Button
          variant={BUTTON_VARIANTS.infoAccessible}
          className="w-full text-regular font-semibold font-lato min-h-[40px]"
          onClick={handleMetroSellerButtonClick}
          data-testid="order-detail-order-line-contact-seller-metro"
          rounded
        >
          {t(
            'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER'
          )}
        </Button>
      ) : (
        <>
          <Dropdown
            buttonVariant={BUTTON_VARIANTS.infoAccessible}
            data-testid="order-detail-order-line-contact-seller"
            prefixSelected={t(
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER'
            )}
            className={{
              button: 'font-semibold font-lato min-h-[40px] leading-[21px]',
              option: 'h-[40px] leading-[24px] text-regular text-blue-main',
              content: 'w-max right-0 min-w-full',
              dropdown: 'hidden lg:block'
            }}
            options={CONTACT_SELLER_OPTIONS.map((option) => ({
              id: option.id,
              value: t(option.value)
            }))}
            onSelectOption={handleSellerButtonClick}
          />
          <div className="lg:hidden">
            <OrderDetailOrderLineContactSellerMobile
              onClickButton={handleSellerButtonClick}
            />
          </div>
        </>
      )}
    </>
  )
}
