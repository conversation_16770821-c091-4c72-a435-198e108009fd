import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'

import { config } from '@core/config'
import { useMarket } from '@core/hooks'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useFeatureFlagValue } from '@core/redux/features/featureFlags'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { Button, Loader } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { Dropdown } from '@modules/shared/components/Dropdown'
import { Option } from '@modules/shared/types'

import { blue } from '@styles/palette'

import { faqUrlByCountry } from '../../constants'
import { OrderLine } from '../../types'
import { OrderSidebarProduct } from '../order-sidebar-product/OrderSidebarProduct'
import { OrderSidebar } from '../order-sidebar/OrderSidebar'

interface Props {
  orderLine: OrderLine
  returnReasons: Option[]
  isLoading: boolean
  isReturning: boolean
  onClose: () => void
  onReturn: (returnReasonValue: number, quantity: number) => void
}

export const ReturnSidebarUI = ({
  isLoading,
  isReturning,
  returnReasons,
  orderLine,
  onClose,
  onReturn
}: Props) => {
  const { t } = useAccountTranslation()
  const marketsShowingRefundFeeInfo: string[] = useFeatureFlagValue(
    FeatureFlag.FF_TMP_ODR_1536_SHOW_REFUND_FEE_IN_OHP_REACT
  )
  const market = useMarket()
  const [quantity, setQuantity] = useState(orderLine.quantity)
  const [quantityOptions, setQuantityOptions] = useState<Option[]>([])

  const [selectedReturnReasonValue, setSelectedReturnReasonValue] = useState<
    number | undefined
  >()

  useEffect(() => {
    const quantityOptionsTmp: Option[] = Array.from(
      { length: orderLine.quantity },
      (_, i) => ({
        id: i + 1,
        value: `${i + 1}`
      })
    )

    if (quantityOptionsTmp.length > 0) {
      quantityOptionsTmp[quantityOptionsTmp.length - 1].value =
        `${quantityOptionsTmp[quantityOptionsTmp.length - 1].value} (${t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.ALL')})`
    }

    setQuantityOptions(quantityOptionsTmp)
  }, [orderLine])

  const isMetroSeller = orderLine.sellerDetails.id === config.METRO_ID
  const isShowingPaidReturns =
    isMetroSeller && marketsShowingRefundFeeInfo.includes(market)

  const returnProduct = (): void => {
    onReturn(selectedReturnReasonValue, quantity)
  }

  const isReturnButtonDisabled = !selectedReturnReasonValue || isReturning

  return (
    <OrderSidebar
      title={t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.RETURN')}
      actions={
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            rounded
            className="w-full !pt-4 !pb-4 text-lg font-[700] h-[48px] leading-[21px]"
            onClick={onClose}
            data-testid="order-line-return-sidebar-cancel"
          >
            {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CANCEL')}
          </Button>
          <Button
            variant={BUTTON_VARIANTS.secondary}
            rounded
            disabled={isReturnButtonDisabled}
            className={cn(
              'w-full pt-4 pb-4 text-lg font-[700] h-[48px] leading-[21px]',
              !selectedReturnReasonValue &&
                '!bg-blue-tint-40 cursor-not-allowed'
            )}
            onClick={() => {
              if (!isReturnButtonDisabled) {
                returnProduct()
              }
            }}
            data-testid="order-line-return-sidebar-return"
            isLoading={isReturning}
          >
            <div className="h-[24px]">
              {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CONFIRM')}
            </div>
          </Button>
        </>
      }
      onClose={onClose}
    >
      <>
        <OrderSidebarProduct
          name={orderLine.productDetails.name}
          url={orderLine.productDetails.imageUrl}
        />

        {isLoading ? (
          <div className="flex w-full min-h-[100px] items-center justify-center">
            <Loader
              className="w-8 h-8"
              strokeColor={blue.shade[60]}
              strokeWidth="6.25%"
            />
          </div>
        ) : (
          <>
            <div>
              {orderLine.quantity > 1 && (
                <>
                  <span className="text-tiny">
                    {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.QUANTITY')}
                  </span>
                  <Dropdown
                    placeholder={t(
                      'PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.QUANTITY'
                    )}
                    dataTestId="dropdown-button-quantity"
                    options={quantityOptions}
                    onSelectOption={({ id }) => {
                      setQuantity(+id)
                    }}
                    className={{
                      placeholder: 'text-grey-tint-40 font-Lato-400',
                      selectedOption:
                        'font-Lato-400 text-regular text-metro-blue-main',
                      option:
                        'h-[47px] text-regular text-metro-blue-main leading-[27px]',
                      content: 'max-h-[320px] overflow-auto'
                    }}
                  />
                </>
              )}
            </div>
            <div>
              <span className="text-tiny">
                {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.REASON')}
              </span>
              <Dropdown
                placeholder={t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.RETURN'
                )}
                dataTestId="dropdown-button-reason"
                options={returnReasons}
                onSelectOption={({ id }) => {
                  setSelectedReturnReasonValue(+id)
                }}
                className={{
                  placeholder: 'text-grey-tint-40 font-Lato-400',
                  selectedOption:
                    'font-Lato-400 text-regular text-metro-blue-main',
                  option:
                    'h-[47px] text-regular text-metro-blue-main leading-[27px]',
                  content: 'h-[320px] overflow-auto'
                }}
              />
            </div>
            <span className="text-regular mr-1">
              {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO')}
              {isShowingPaidReturns && (
                <span data-testid="order-line-return-sidebar-return-fee">
                  {' '}
                  {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO.FEE')}{' '}
                  <a
                    href={faqUrlByCountry[market]}
                    target="_blank"
                    className="text-blue-main cursor-pointer text-regular"
                  >
                    {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.LEARN_MORE')}.
                  </a>
                </span>
              )}
            </span>
          </>
        )}
      </>
    </OrderSidebar>
  )
}
