import { useRef, useState } from 'react'

import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useUser } from '@core/redux'

import { ReturnLabelActions } from '@modules/account/components/orders-history/components/return-label-actions/ReturnLabelActions'
import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { returnItemButtonVisibleStatusView } from '../../constants'
import { OrderLine, OrderStateStatus } from '../../types'
import {
  convertOrderLineStatusAndTrackingStatusToOrderLineStatusView,
  hasAnyOrderLineStatus,
  isAllowedToCancel
} from '../../utils/utils'
import { BuyAgainButton } from '../buy-again-button/BuyAgainButton'
import { OrderTrackItems } from '../order-track-items/OrderTrackItems'
import { OrderDetailOrderLineActionsCancel } from './OrderDetailOrderLineActionsCancel/OrderDetailOrderLineActionsCancel'
import { OrderDetailOrderLineActionsCancelReturnModal } from './OrderDetailOrderLineActionsCancelReturnModal'
import { OrderDetailOrderLineContactSeller } from './OrderDetailOrderLineContactSeller'
import { ReturnSidebar } from './ReturnSidebar'

interface Props {
  orderId: string
  orderNumber: string
  orderLine: OrderLine
  onUpdated: () => void
}

export const OrderDetailOrderLineActions = ({
  orderId,
  orderNumber,
  orderLine,
  onUpdated
}: Props) => {
  const { t } = useAccountTranslation()
  const { isSubsidiaryAccount } = useUser()

  const [showReturnModal, setShowReturnModal] = useState(false)
  const [showCancelModal, setShowCancelModal] = useState(false)
  const [showCancelReturningModal, setShowCancelReturningModal] =
    useState(false)

  const cancelButtonRef = useRef<HTMLButtonElement>(null)
  const returnButtonRef = useRef<HTMLButtonElement>(null)
  const cancelReturnButtonRef = useRef<HTMLButtonElement>(null)

  const isAllowedCancelReturning =
    orderLine.status.value === OrderStateStatus.RETURN_REQUESTED

  const orderLineStatusView =
    convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(orderLine)

  const isAllowedToCancelOrderLine = isAllowedToCancel(orderLineStatusView)

  const isAllowedToReturn = hasAnyOrderLineStatus(
    [orderLine],
    returnItemButtonVisibleStatusView
  )

  const isShowingContactSeller =
    !isSubsidiaryAccount && !!orderLine.sellerDetails.email

  return (
    <div className="space-y-3">
      {isShowingContactSeller && (
        <OrderDetailOrderLineContactSeller
          orderId={orderId}
          orderNumber={orderNumber}
          orderLine={orderLine}
        />
      )}
      {isAllowedToReturn && (
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px]"
            data-testId="order-detail-order-line-actions-return"
            onClick={() => setShowReturnModal(true)}
            ref={returnButtonRef}
            rounded
          >
            <span className="font-lato">
              {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.RETURN')}
            </span>
          </Button>
          {showReturnModal && (
            <ReturnSidebar
              orderId={orderId}
              onReturned={onUpdated}
              orderLine={orderLine}
              onClose={() => {
                setShowReturnModal(false)
                returnButtonRef?.current?.focus()
              }}
            />
          )}
        </>
      )}
      {isAllowedCancelReturning && (
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex justify-center text-regular w-full items-center font-semibold mb-3 min-h-[40px]"
            data-testId="order-detail-order-line-actions-cancel-return"
            onClick={() => setShowCancelReturningModal(true)}
            ref={cancelReturnButtonRef}
            rounded
          >
            <span className="font-lato">
              {t('PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL')}
            </span>
          </Button>
          {showCancelReturningModal && (
            <OrderDetailOrderLineActionsCancelReturnModal
              orderId={orderId}
              orderLine={orderLine}
              onClose={() => {
                setShowCancelReturningModal(false)
                cancelReturnButtonRef?.current?.focus()
              }}
              onCancelledReturn={onUpdated}
            />
          )}
        </>
      )}
      {isAllowedToCancelOrderLine && (
        <>
          <Button
            variant={BUTTON_VARIANTS.infoAccessible}
            className="flex justify-center text-regular w-full items-center font-semibold min-h-[40px]"
            data-testId="order-detail-order-line-actions-cancel"
            onClick={() => setShowCancelModal(true)}
            ref={cancelButtonRef}
            rounded
          >
            {t('PAGES.ACCOUNT.ORDER_HISTORY.CANCEL_PRODUCT')}
          </Button>
          {showCancelModal && (
            <OrderDetailOrderLineActionsCancel
              onClose={() => {
                setShowCancelModal(false)
                cancelButtonRef?.current?.focus()
              }}
              onCancelled={onUpdated}
              orderId={orderId}
              orderLine={orderLine}
            />
          )}
        </>
      )}

      <ReturnLabelActions orderLine={orderLine} isHighlighted={true} />

      <OrderTrackItems
        orderLine={orderLine}
        variant={BUTTON_VARIANTS.secondary}
      />
      {orderLine.isBuyAgain && <BuyAgainButton orderLine={orderLine} />}
    </div>
  )
}
