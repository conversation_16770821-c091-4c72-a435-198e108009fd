import { useAccountTranslation } from '@core/hooks/useTranslation'

import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

interface Props {
  isCancelling: boolean
  onConfirm: () => void
  onClose: () => void
}

export const OrderDetailCancelUIFooter = ({
  isCancelling,
  onConfirm,
  onClose
}: Props) => {
  const { t } = useAccountTranslation()

  return (
    <>
      <Button
        variant={BUTTON_VARIANTS.infoAccessible}
        rounded
        className="w-full !pt-[15px] !pb-[15px] font-[700] h-[48px]"
        onClick={onClose}
        data-testid="order-detail-cancel-cancel"
      >
        <span className="text-lg leading-[18px]">
          {t('PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CANCEL')}
        </span>
      </Button>
      <Button
        variant={BUTTON_VARIANTS.secondary}
        rounded
        className="w-full !pt-4 !pb-4 text-lg font-[700] h-[48px]"
        onClick={() => {
          if (!isCancelling) {
            onConfirm()
          }
        }}
        data-testid="order-detail-cancel-confirm"
        isLoading={isCancelling}
      >
        <span className="leading-[18px] text-lg">
          {t('PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CONFIRM')}
        </span>
      </Button>
    </>
  )
}
