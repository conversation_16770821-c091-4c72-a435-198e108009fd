import { waitFor } from '@testing-library/react'

import { http } from '@core/services/http/http-request'
import { renderHookWithRedux } from '@core/utils/testing'

import { useMessageCenterEligibility } from '../useMessageCenterEligibility'

jest.mock('@core/services/http/http-request')

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))

describe('useMessageCenterEligibility', () => {
  const mockHttp = http as jest.MockedFunction<typeof http>
  const defaultProps = {
    organizationId: 'metro123',
    orderNumber: 'ON123'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return initial state', () => {
    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: false
          }
        }
      }
    )

    expect(result.current.isEligible).toBe(false) // Should be false when feature flag is disabled
    expect(result.current.isCheckingEligibility).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.canRedirectToMessageCenter).toBe(false)
    expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(false)
  })

  it('should set isEligible to false when feature flag is disabled', async () => {
    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: false
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(false)
    })

    expect(mockHttp).not.toHaveBeenCalled()
  })

  it('should check eligibility when feature flag is enabled and order is eligible', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] has been verified successfully.',
        data: {
          eligible: true
        }
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isCheckingEligibility).toBe(true)
    })

    await waitFor(() => {
      expect(result.current.isEligible).toBe(true)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(true)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(null)
    })

    expect(mockHttp).toHaveBeenCalledWith(
      expect.stringContaining('message-center/get-eligibility'),
      expect.objectContaining({
        method: 'GET',
        params: {
          organizationId: 'metro123',
          orderNumber: 'ON123'
        }
      }),
      expect.objectContaining({
        market: 'de',
        locale: 'de-DE'
      })
    )
  })

  it('should check eligibility when feature flag is enabled but order is not eligible', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] is not eligible.',
        data: {
          eligible: false
        }
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(null)
    })

    expect(mockHttp).toHaveBeenCalled()
  })

  it('should handle API error and set isEligible to false', async () => {
    const mockError = new Error('Network error')
    mockHttp.mockRejectedValue(mockError)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(mockError)
    })

    expect(mockHttp).toHaveBeenCalled()
  })

  it('should handle malformed API response and set isEligible to false', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        // Missing data.eligible field
        data: {}
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.error).toBe(null)
    })
  })
})
