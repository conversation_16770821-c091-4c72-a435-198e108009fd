import { renderHook, waitFor } from '@testing-library/react'

import { http } from '@core/services/http/http-request'
import { renderHookWithRedux } from '@core/utils/testing'

import { useMessageCenterEligibility } from '../useMessageCenterEligibility'

jest.mock('@core/services/http/http-request')

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))

describe('useMessageCenterEligibility', () => {
  const mockHttp = http as jest.MockedFunction<typeof http>
  const defaultProps = {
    organizationId: 'metro123',
    orderNumber: 'ON123'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return initial state', () => {
    const { result } = renderHookWithRedux(() =>
      useMessageCenterEligibility(defaultProps)
    )

    expect(result.current.isEligible).toBe(null)
    expect(result.current.isCheckingEligibility).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.canRedirectToMessageCenter).toBe(false)
    expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(false)
  })

  it('should set isEligible to false when feature flag is disabled', async () => {
    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: false
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(false)
    })

    expect(mockHttp).not.toHaveBeenCalled()
  })

  it('should check eligibility when feature flag is enabled and order is eligible', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] has been verified successfully.',
        data: {
          eligible: true
        }
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isCheckingEligibility).toBe(true)
    })

    await waitFor(() => {
      expect(result.current.isEligible).toBe(true)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(true)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(null)
    })

    expect(mockHttp).toHaveBeenCalledWith(
      expect.stringContaining('/app-api/message-center/get-eligibility'),
      expect.objectContaining({
        method: 'GET',
        params: {
          organizationId: 'metro123',
          orderNumber: 'ON123'
        }
      }),
      expect.objectContaining({
        market: 'de',
        locale: 'de'
      })
    )
  })

  it('should check eligibility when feature flag is enabled but order is not eligible', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        message: 'Order [ON123] is not eligible.',
        data: {
          eligible: false
        }
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(null)
    })

    expect(mockHttp).toHaveBeenCalled()
  })

  it('should handle API error and set isEligible to false', async () => {
    const mockError = new Error('Network error')
    mockHttp.mockRejectedValue(mockError)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.isMessageCenterC2AFeatureEnabled).toBe(true)
      expect(result.current.error).toBe(mockError)
    })

    expect(mockHttp).toHaveBeenCalled()
  })

  it('should handle malformed API response and set isEligible to false', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        // Missing data.eligible field
        data: {}
      }
    } as any)

    const { result } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(false)
      expect(result.current.isCheckingEligibility).toBe(false)
      expect(result.current.canRedirectToMessageCenter).toBe(false)
      expect(result.current.error).toBe(null)
    })
  })

  it('should re-check eligibility when props change', async () => {
    mockHttp.mockResolvedValue({
      data: {
        status: 'success',
        data: { eligible: true }
      }
    } as any)

    const { result, rerender } = renderHookWithRedux(
      (props) => useMessageCenterEligibility(props),
      {
        initialProps: defaultProps,
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    await waitFor(() => {
      expect(result.current.isEligible).toBe(true)
    })

    expect(mockHttp).toHaveBeenCalledTimes(1)

    // Change props
    rerender({
      organizationId: 'different-org',
      orderNumber: 'different-order'
    })

    await waitFor(() => {
      expect(mockHttp).toHaveBeenCalledTimes(2)
    })

    expect(mockHttp).toHaveBeenLastCalledWith(
      expect.stringContaining('/app-api/message-center/get-eligibility'),
      expect.objectContaining({
        params: {
          organizationId: 'different-org',
          orderNumber: 'different-order'
        }
      }),
      expect.any(Object)
    )
  })

  it('should clear error when feature flag is disabled', async () => {
    const mockError = new Error('Previous error')
    mockHttp.mockRejectedValue(mockError)

    const { result, rerender } = renderHookWithRedux(
      () => useMessageCenterEligibility(defaultProps),
      {
        preloadedState: {
          featureFlags: {
            values: {
              FF_CCS_MESSAGE_CENTER_ORDER_C2A: true
            }
          }
        }
      }
    )

    // Wait for error to be set
    await waitFor(() => {
      expect(result.current.error).toBe(mockError)
    })

    // Change feature flag to disabled
    rerender(undefined, {
      preloadedState: {
        featureFlags: {
          values: {
            FF_CCS_MESSAGE_CENTER_ORDER_C2A: false
          }
        }
      }
    })

    await waitFor(() => {
      expect(result.current.error).toBe(null)
      expect(result.current.isEligible).toBe(false)
    })
  })
})
