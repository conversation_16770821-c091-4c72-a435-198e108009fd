import { useEffect, useState } from 'react'

import { getProxyEndpoint } from '@core/constants/api.constants'
import { useMarket } from '@core/hooks'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { useLocale } from '@core/redux/features/market/useLocale'
import { http } from '@core/services/http/http-request'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

interface EligibilityResponse {
  status: string
  message: string
  data: {
    eligible: boolean
  }
}

interface UseMessageCenterEligibilityProps {
  organizationId: string
  orderNumber: string
}

export const useMessageCenterEligibility = ({
  organizationId,
  orderNumber
}: UseMessageCenterEligibilityProps) => {
  const market = useMarket()
  const locale = useLocale()
  const isMessageCenterC2AFeatureEnabled = useFeatureFlag(
    FeatureFlag.FF_CCS_MESSAGE_CENTER_ORDER_C2A
  )

  const [isEligible, setIsEligible] = useState<boolean | null>(null)
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  // Check eligibility when feature flag is enabled
  useEffect(() => {
    const checkEligibility = async () => {
      if (!isMessageCenterC2AFeatureEnabled) {
        setIsEligible(false)
        setError(null)
        return
      }

      setIsCheckingEligibility(true)
      setError(null)
      
      try {
        const url = getProxyEndpoint(market, 'message-center/get-eligibility')
        const { data } = await http<EligibilityResponse>(
          url,
          {
            method: 'GET',
            params: {
              organizationId,
              orderNumber
            }
          },
          { market, locale }
        )
        setIsEligible(data?.data?.eligible || false)
      } catch (err) {
        // If eligibility check fails, fall back to false (current behavior)
        setError(err as Error)
        setIsEligible(false)
      } finally {
        setIsCheckingEligibility(false)
      }
    }

    checkEligibility()
  }, [
    isMessageCenterC2AFeatureEnabled,
    market,
    locale,
    organizationId,
    orderNumber
  ])

  const canRedirectToMessageCenter = isMessageCenterC2AFeatureEnabled && isEligible

  return {
    isEligible,
    isCheckingEligibility,
    error,
    canRedirectToMessageCenter,
    isMessageCenterC2AFeatureEnabled
  }
}
