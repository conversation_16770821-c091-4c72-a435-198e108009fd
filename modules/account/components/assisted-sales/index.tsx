import { useDispatch } from 'react-redux'

import { SOFT_LOGIN_TOKEN_KEY } from '@core/components/Auth/SoftLogin/softLogin.constant'
import { getAppBuyerUrl } from '@core/config/url'
import {
  ACCESS_TOKEN,
  ACCOUNT_TYPE_KEY,
  HAS_DIGITAL_IDENTITY_KEY,
  ID_TOKEN,
  IMPERSONATED_CUSTOMER_FULLNAME_KEY,
  SESSION_STATE,
  SUB_ACCOUNT_TYPE,
  USER_LOGIN_TYPE
} from '@core/constants/identity.constant'
import {
  APP_CATALOG_PAGE_ROUTE,
  APP_CUSTOMER_LIST_ROUTE
} from '@core/constants/routesConstants'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useUser } from '@core/redux'
import { useAddress } from '@core/redux/features/address/useAddress'
import { clearDiscountCodeFromSessionStorage } from '@core/redux/features/cart/thunk/tools/clearDiscountCodeFromSessionStorage'
import { useLocation } from '@core/redux/features/market/useMarket'
import {
  setAccountInfo,
  setAccountType,
  setEmployeeInfo,
  setIdTokenHint,
  setLogoutEmployee
} from '@core/redux/features/user'

import { REFRESH_SOFT_TOKEN } from '@modules/account/hooks/auth/constants'
import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import {
  removeItemsFromLocalStorage,
  removeItemsFromSessionStorage
} from '@modules/shared/utils'

import { SL_USER_LOGIN_TYPE_LOCAL_STORAGE_KEY } from '../login-flows/components/otp-screen/constants'

interface Props {}

export const AssistedSales: React.FC<Props> = ({}) => {
  const dispatch = useDispatch()
  const { t } = useAccountTranslation()
  const { market } = useLocation()
  const { clearAddressCheckoutLocalStorage } = useAddress()
  const {
    stopImpersonation,
    fetchAccountInfo,
    logout,
    accountInfo,
    isAuthorized
  } = useUser()
  const webAppBuyerUrl = getAppBuyerUrl(market)

  const customerListRoute = `${webAppBuyerUrl}${APP_CUSTOMER_LIST_ROUTE}`
  const catalogRoute = `${webAppBuyerUrl}/${APP_CATALOG_PAGE_ROUTE}`

  const handleStartWithNewCustomer = async () => {
    await stopImpersonation()
    fetchAccountInfo()
    clearDiscountCodeFromSessionStorage()
    localStorage.removeItem(HAS_DIGITAL_IDENTITY_KEY)
    localStorage.removeItem(IMPERSONATED_CUSTOMER_FULLNAME_KEY)
    localStorage.removeItem(ACCOUNT_TYPE_KEY)
    clearAddressCheckoutLocalStorage()
    window.location.href = customerListRoute
  }

  const handleEmployeeLogout = () => {
    if (isAuthorized) {
      dispatch(setLogoutEmployee(true))
      logout()
    }
  }

  const logoutEmployee = async () => {
    await stopImpersonation()
    clearDiscountCodeFromSessionStorage()
    clearAddressCheckoutLocalStorage()
    localStorage.removeItem(IMPERSONATED_CUSTOMER_FULLNAME_KEY)
    localStorage.removeItem(ACCOUNT_TYPE_KEY)
    const itemsToRemoveFromSessionStorage = [
      SESSION_STATE,
      ID_TOKEN,
      ACCESS_TOKEN
    ]

    const itemsToRemoveFromLocalStorage = [
      USER_LOGIN_TYPE,
      SL_USER_LOGIN_TYPE_LOCAL_STORAGE_KEY,
      SUB_ACCOUNT_TYPE,
      REFRESH_SOFT_TOKEN,
      SOFT_LOGIN_TOKEN_KEY
    ]

    removeItemsFromSessionStorage(itemsToRemoveFromSessionStorage)
    removeItemsFromLocalStorage(itemsToRemoveFromLocalStorage)
    handleEmployeeLogout()
    dispatch(setLogoutEmployee(false))
    dispatch(setIdTokenHint(null))
    dispatch(setAccountInfo(null))
    dispatch(setEmployeeInfo(null))
    dispatch(setAccountType(null))

    window.location.href = catalogRoute
  }

  return (
    <div
      className="flex justify-between bg-white-main py-5 my-5 px-8 font-lato"
      data-testid="account-assisted-sales"
    >
      <span className="text-primary-main text-lg leading-[40px] space-y-2">
        <span>{t('PAGES.ACCOUNT.ORDER_HISTORY.COMPANY')}</span>
        <span className="font-bold">{accountInfo?.organizationName}</span>
      </span>

      <div className="space-x-4">
        <Button
          variant={BUTTON_VARIANTS.infoAccessible}
          className="py-2 px-4 font-semibold text-regular"
          rounded
          onClick={handleStartWithNewCustomer}
          data-testid="account-assisted-sales-new-customer"
        >
          <span>
            {t('PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.NEW_IMPERSONATION')}
          </span>
        </Button>
        <Button
          variant={BUTTON_VARIANTS.secondary}
          className="py-2 px-4 font-semibold text-regular"
          rounded
          onClick={logoutEmployee}
        >
          {t('PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.EMPLOYEE_SIGNOUT')}
        </Button>
      </div>
    </div>
  )
}
