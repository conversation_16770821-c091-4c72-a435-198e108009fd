import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'

import { getAppBuyerUrl } from '@core/config/url'
import { NUMBER_0, NUMBER_40 } from '@core/constants/numbers'
import { useMediaQuery } from '@core/hooks'
import { useUser } from '@core/redux'

import { ProductAdvancedCarousel } from '@shared/components'
import { TextboxCarousel } from '@shared/components/Storyblok/AdvancedCarousel/TextboxCarousel'
import { classes } from '@shared/components/Storyblok/AdvancedCarousel/classes'
import { TOOLTIP_POSITION } from '@shared/components/Tolltip/constants'
import { SVG_NAMES } from '@shared/icons/constants'
import { clsx } from '@shared/utils'

import { PLPS_PAGES } from '@modules/product/constants'
import {
  hasAnyBasePriceOrMoreThanOneUnit,
  hasAnyReferencePrice
} from '@modules/product/utils/top-products-carousel'

import { LG_BREAKPOINT, SM_BREAKPOINT } from '@styles/mediaQueriesBreakpoints'

type Props = {
  label?: string
  query: string
  description: string
  market
  explanation: string
  region?
  topCarousel?: boolean
  buttonCarousel?: boolean
  carouselBg?: string
  products?: any[]
  isNormalSearch?: boolean
}
export const AICarousel = ({
  label,
  query,
  description,
  market,
  explanation,
  region,
  topCarousel,
  buttonCarousel,
  carouselBg,
  products,
  isNormalSearch
}: Props) => {
  const { t } = useTranslation([PLPS_PAGES])
  const matchesSm = useMediaQuery(`(${SM_BREAKPOINT})`)
  const matchesLg = useMediaQuery(`(${LG_BREAKPOINT})`)
  const aiCarousel = useRef(null)
  const WEB_APP_BUYER_URL = getAppBuyerUrl(market)
  const [isMobile, setIsMobile] = useState(false)
  const [isMobileOrTablet, setIsMobileOrTablet] = useState<boolean>(false)
  const [hasReferencePrice, setHasReferencePrice] = useState<boolean>()

  const [hasBasePriceOrMoreThanOneUnit, setHasBasePriceOrMoreThanOneUnit] =
    useState<boolean>()

  const preTitle = {
    text: t('CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER'),
    svgName: SVG_NAMES.AI_STARS,
    tooltipDescription: t('CATALOG.CATALOG_PAGE.AI_INFO_TOOLBAR'),
    tooltipIconName: SVG_NAMES.ICON_INFORMATION,
    tooltipPosition: TOOLTIP_POSITION.carouselTop
  }

  useEffect(() => {
    if (products?.length > NUMBER_0) {
      setHasReferencePrice(hasAnyReferencePrice(products as any))
      setHasBasePriceOrMoreThanOneUnit(
        hasAnyBasePriceOrMoreThanOneUnit(products as any)
      )
    }
  }, [products])

  useEffect(() => {
    setIsMobile(matchesSm)
  }, [matchesSm])

  useEffect(() => {
    setIsMobileOrTablet(matchesLg)
  }, [isMobileOrTablet])

  return (
    <>
      <div
        data-testid="ai-advanced-carousel"
        className={clsx(
          `p-0 rounded-xs xs-lgMinus:relative xs-lgMinus:min-w-[100vw] xs-lgMinus:left-[-16px] mt-6 ${carouselBg}`,
          'bg-white-main'
        )}
        ref={aiCarousel}
      >
        <TextboxCarousel
          button={[
            {
              text: label,
              link: `${WEB_APP_BUYER_URL}/search?q=${encodeURIComponent(query)}`,
              isExternalLink: true
            }
          ]}
          dynamicEditorStyle={''}
          title={isNormalSearch ? `${description} ${query}` : description}
          preTitle={preTitle}
          products={products as any}
          isMobile={isMobile}
          isMobileOrTablet={isMobileOrTablet}
          isPromo={false}
          webAppBuyerUrl={WEB_APP_BUYER_URL}
          currentClasses={classes.advancedCarousel['white']}
          hasReferencePrice={hasReferencePrice}
          hasBasePriceOrMoreThanOneUnit={hasBasePriceOrMoreThanOneUnit}
          onProductCardLoadingComplete={() => {}}
          description={explanation}
          customBtnWithIconClasses={
            '!rounded-md !font-bold hover:bg-blue-shade-20'
          }
        ></TextboxCarousel>
      </div>
    </>
  )
}
