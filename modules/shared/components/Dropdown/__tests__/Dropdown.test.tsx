import { fireEvent, screen, within } from '@testing-library/react'
import React from 'react'

import { renderWithProviders } from '@core/utils/testing'

import { Dropdown, Props } from '..'

const mockOptions = [
  { value: 'Option 1', id: 'option1' },
  { value: 'Option 2', id: 'option2' },
  { value: 'Option 3', id: 'option3' }
]

describe('Dropdown', () => {
  let props: Props
  let onSelectOption: jest.Mock

  beforeEach(() => {
    onSelectOption = jest.fn()
    props = {
      prefixSelected: 'Order type',
      options: mockOptions,
      onSelectOption
    }
  })

  it('displays all options when the dropdown is open', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    mockOptions.forEach((option) => {
      expect(
        within(screen.getByTestId('dropdown-options')).getByText(option.value)
      ).toBeInTheDocument()
    })
  })

  it('should not open dropdown when disabled', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
        disabled={true}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.queryByTestId('dropdown-options')).not.toBeInTheDocument()
  })

  it('should apply disabled styles when disabled', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
        disabled={true}
      />
    )

    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('cursor-not-allowed', 'opacity-50')
  })

  it('should not call onSelectOption when disabled and option is clicked', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
        disabled={true}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(onSelectOption).not.toHaveBeenCalled()
  })

  it('should disable Button variant when disabled prop is true', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
        buttonVariant="infoAccessible"
        disabled={true}
        dataTestId="dropdown-button"
      />
    )

    const button = screen.getByTestId('dropdown-button')
    expect(button).toBeDisabled()

    fireEvent.click(button)
    expect(screen.queryByTestId('dropdown-options')).not.toBeInTheDocument()
  })

  it('calls onSelectOption callback when an option is selected', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    fireEvent.click(screen.getByText(mockOptions[1].value))
    expect(onSelectOption).toHaveBeenCalledWith(mockOptions[1])
  })

  it('displays the selected option in the button', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    fireEvent.click(screen.getByText(mockOptions[2].value))
    expect(screen.getByRole('button')).toHaveTextContent(
      `Order type:${mockOptions[2].value}`
    )
  })

  it('hides the dropdown after clicking outside', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={props.options}
        onSelectOption={onSelectOption}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.getByTestId('dropdown-options')).toBeInTheDocument()
    fireEvent.mouseDown(document)
    expect(screen.queryByTestId('dropdown-options')).not.toBeInTheDocument()
  })

  it('should show the placeholder if empty array is passed as options', () => {
    renderWithProviders(
      <Dropdown
        prefixSelected={props.prefixSelected}
        options={[]}
        onSelectOption={onSelectOption}
        placeholder="placeholder"
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.getByTestId('dropdown-options')).toBeInTheDocument()
    fireEvent.mouseDown(document)
    expect(screen.getByRole('button')).toHaveTextContent('placeholder')
  })

  it('should only display the selected option value if prefix selected it no passed by parameter', () => {
    renderWithProviders(
      <Dropdown options={props.options} onSelectOption={onSelectOption} />
    )

    fireEvent.click(screen.getByRole('button'))
    fireEvent.click(screen.getByText(mockOptions[2].value))
    expect(screen.getByRole('button')).toHaveTextContent(mockOptions[2].value)
  })
})
