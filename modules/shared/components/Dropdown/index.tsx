import { useEffect, useRef, useState } from 'react'

import { handleEnterOrSpacePress } from '@shared/utils/accessibility.utils'

import { SVG_NAMES } from '@modules/shared/icons/constants'
import { Option } from '@modules/shared/types'

import { Button } from '../Button'
import { BUTTON_VARIANTS, ButtonVariant } from '../Button/types/variant'
import { SVGIcon } from '../SvgIcon'

export interface Props {
  prefixSelected?: string
  selectedOptionId?: string | number
  placeholder?: string
  options: Option[]
  className?: {
    dropdown?: string
    placeholder?: string
    selectedOption?: string
    option?: string
    content?: string
    button?: string
  }
  buttonVariant?: ButtonVariant
  dataTestId?: string
  onSelectOption: (option: Option) => void
  onClosed?: () => void
}

export const Dropdown = ({
  selectedOptionId,
  placeholder,
  prefixSelected,
  options,
  className = {
    dropdown: '',
    placeholder: '',
    selectedOption: '',
    option: '',
    content: ''
  },
  buttonVariant,
  dataTestId,
  onSelectOption,
  onClosed
}: Props) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedOption, setSelectedOption] = useState<Option | undefined>()

  const dropdownRef = useRef<HTMLDivElement>(null)
  const isOpenRef = useRef(false)

  const selectOption = (option: Option) => {
    setSelectedOption(option)
    onSelectOption(option)
    setIsOpen(false)
    isOpenRef.current = false
  }
  useEffect(() => {
    setSelectedOption(options.find((option) => option.id === selectedOptionId))
  }, [selectedOptionId, options])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        if (isOpenRef.current && onClosed) {
          onClosed()
        }
        isOpenRef.current = false
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const toggleIsOpen = () => {
    setIsOpen(!isOpen)
    isOpenRef.current = !isOpen
  }

  const colorClassName =
    buttonVariant === BUTTON_VARIANTS.info ? 'text-blue-interaction' : ''

  return (
    <div
      className={`relative text-base text-grey-main font-lato ${className.dropdown}`}
      ref={dropdownRef}
    >
      {!buttonVariant ? (
        <button
          onClick={toggleIsOpen}
          className="flex items-center justify-between whitespace-nowrap w-full px-[12px] py-[8px] bg-white border border-grey-tint-80 rounded-md hover:bg-blue-tint-95 h-[40px]"
          data-testid={dataTestId ?? 'dropdown'}
        >
          <div className="flex flex-row leading-[28px] justify-between w-full">
            <div>
              {!selectedOption ? (
                <span className={className.placeholder}>{placeholder}</span>
              ) : (
                <>
                  {prefixSelected && <>{prefixSelected}:</>}
                  {selectedOption && (
                    <span className={`ml-1 ${className.selectedOption}`}>
                      {selectedOption.value}{' '}
                    </span>
                  )}
                </>
              )}
            </div>
            <div>
              <SVGIcon
                name={SVG_NAMES.CHEVRON_DOWN}
                fill="#99A1AD"
                stroke="none"
                width="16px"
                height="16px"
                viewBox="0 0 32 32"
                className={`ml-[4px] mt-7px transform transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : 'rotate-0'}`}
              />
            </div>
          </div>
        </button>
      ) : (
        <Button
          variant={BUTTON_VARIANTS.infoAccessible}
          className={`flex w-full ${className.button} text-regular justify-center items-center space-x-[6px] !rounded-md leading-[21px]`}
          data-testid={dataTestId ?? 'dropdown-button'}
          onClick={toggleIsOpen}
          rounded
        >
          <span>{prefixSelected}</span>
          <SVGIcon
            name={SVG_NAMES.CHEVRON_DOWN}
            className={`${colorClassName} transform transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : 'rotate-0'}`}
            width="20px"
            height="20px"
          />
        </Button>
      )}
      {isOpen && (
        <div
          className={`absolute z-10 w-full bg-white-main border border-gray-200 rounded-md shadow-lg ${className.content}`}
          data-testid="dropdown-options"
        >
          {options.map((option) => (
            <div
              onKeyDown={handleEnterOrSpacePress(() => selectOption(option))}
              tabIndex={0}
              role="button"
              key={option.id}
              className={`px-4 py-2 cursor-pointer h-[36px] hover:bg-blue-tint-95 ${className.option}`}
              onClick={() => selectOption({ ...option })}
            >
              {option.value}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
