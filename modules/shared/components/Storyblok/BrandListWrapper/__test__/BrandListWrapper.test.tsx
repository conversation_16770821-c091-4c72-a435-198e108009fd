import { screen } from '@testing-library/react'
import { BrandListWrapper } from '..'
import { useBrandFetch } from '@modules/shared/hooks/useBrandFetch'
import { renderWithProviders } from '@core/utils/testing'

jest.mock('@modules/shared/hooks/useBrandFetch', () => ({
  useBrandFetch: jest.fn()
}))

jest.mock('@modules/category/components', () => ({
  BrandList: jest.fn(({ title, subTitle, displayedBrands, allHaveLogos }) => (
    <div data-testid="brand-list">
      <h1>{title}</h1>
      <h2>{subTitle}</h2>
      <span>Brands count: {displayedBrands.length}</span>
      <span>{allHaveLogos ? 'Logos present' : 'No logos'}</span>
    </div>
  ))
}))

jest.mock('../BrandListWrapperSkeleton', () => ({
  BrandListWrapperSkeleton: () => <div data-testid="brand-list-wrapper-skeleton">Loading...</div>
}))

jest.mock('@storyblok/react', () => ({
  storyblokEditable: () => ({ 'data-test': 'storyblok' })
}))

describe('BrandListWrapper', () => {

  const baseBlok = {
    title: 'Top Brands',
    subTitle: 'Our trusted partners',
    brandSelection: { id: ['1', '2', '3'] },
    _uid: '123abc',
    component: 'brand-list-wrapper'
  }

  it('renders skeleton when loading', () => {
    (useBrandFetch as jest.Mock).mockReturnValue({
      isLoading: true,
      isError: false,
      brands: [],
      hasLogos: false
    })

    renderWithProviders(<BrandListWrapper blok={baseBlok} />)

    expect(screen.getByTestId('brand-list-wrapper-skeleton')).toBeInTheDocument()
  })

  it('renders BrandList when data is valid', () => {
    (useBrandFetch as jest.Mock).mockReturnValue({
      isLoading: false,
      isError: false,
      brands: [
        { value: 'brand1', logo: 'logo1.png' },
        { value: 'brand2', logo: 'logo2.png' },
        { value: 'brand3', logo: 'logo3.png' },
        { value: 'brand4', logo: 'logo4.png' },
        { value: 'brand5', logo: 'logo5.png' }
      ],
      hasLogos: true
    })

    renderWithProviders(<BrandListWrapper blok={baseBlok} />)

    expect(screen.getByTestId('brand-list')).toBeInTheDocument()
    expect(screen.getByText('Top Brands')).toBeInTheDocument()
    expect(screen.getByText('Our trusted partners')).toBeInTheDocument()
    expect(screen.getByText('Brands count: 5')).toBeInTheDocument()
    expect(screen.getByText('Logos present')).toBeInTheDocument()
  })
})
