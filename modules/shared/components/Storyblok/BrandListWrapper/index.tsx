import { BrandList } from '@modules/category/components'
import { SbBlokData, storyblokEditable } from '@storyblok/react'
import { BrandListWrapperSkeleton } from './BrandListWrapperSkeleton'
import { useBrandFetch } from '@modules/shared/hooks/useBrandFetch'
import { useMemo } from 'react'

interface BrandListWrapperBlok extends SbBlokData {
    title?: string
    subTitle?: string
    brandSelection?: {
        id?: string[]
    }
}

export interface BrandListWrappertProps {
    blok?: BrandListWrapperBlok
}

export const BrandListWrapper = ({ blok }: BrandListWrappertProps) => {
    const { title, subTitle, brandSelection } = blok

    const brandSelectionIds = useMemo(() => brandSelection?.id ?? [], [brandSelection?.id])
    const {
        brands,
        hasLogos,
        isError,
        isLoading
    } = useBrandFetch(brandSelectionIds)

    if (isLoading) {
        return <BrandListWrapperSkeleton></BrandListWrapperSkeleton>
    }

    if (isError || !brands || brands.length < 5) {
        return <></>
    }

    return (
        <section
            {...storyblokEditable(blok)}
            className="storyblok-blok-container"
        >
            <BrandList
                title={title}
                subTitle={subTitle}
                displayedBrands={brands}
                allHaveLogos={hasLogos}
            />
        </section>
    )
}
