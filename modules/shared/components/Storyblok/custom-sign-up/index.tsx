import { SbBlokData, storyblokEditable } from '@storyblok/react'
import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'

import { useGtag, useIsInViewport } from '@core/hooks'
import { useExponea } from '@core/services/exponea/useExponea'

import {
  GA_TRACKING_ACTION,
  GA_TRACKING_EVENT
} from '@modules/ga-tracking/constants'
import { isEmail } from '@modules/shared/utils'

import { CustomSignUpUI } from './CustomSignUpUI'

export interface CustomSignUp extends SbBlokData {
  title?: string
  subTitle?: string
  imageSrc?: string
  buttonText?: string
  termsAndConditions: {
    value: string
  }
  confirmationMessage: {
    value: string
  }
  source?: string
}

export interface CustomSignUpProps {
  blok: CustomSignUp
}

export const CustomSignUpWrapper = ({ blok }: CustomSignUpProps) => {
  const [loading, setLoading] = useState(false)
  const { t } = useTranslation()
  const [inputFlag, setInputFlag] = useState<boolean>(false)
  const [exponeaResponse, setExponeaResponse] = useState(false)
  const [userEmail, setUserEmail] = useState('')
  const customSignUpRef = useRef(null)
  const { isInViewport } = useIsInViewport(customSignUpRef)
  const { sendVisitorsEventData } = useExponea()
  const { gtagEvent } = useGtag()

  useEffect(() => {
    const triggerView = (): void => {
      gtagEvent({
        event: GA_TRACKING_EVENT.CustomSignUpViewed,
        GA_eventname: GA_TRACKING_EVENT.Interaction,
        action: GA_TRACKING_ACTION.CustomSignUpViewed
      })
    }
    triggerView()
  }, [gtagEvent, isInViewport])

  const handleUserImput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserEmail(e.target.value)
    !inputFlag && setInputFlag(true)
  }

  const handleTrackingAndExponea = () => {
    gtagEvent({
      action: GA_TRACKING_ACTION.CustomSignUpSuscribed,
      event: GA_TRACKING_EVENT.CustomSignUpSuscribed,
      label: blok?.source || 'homepage',
      GA_eventname: GA_TRACKING_EVENT.Interaction
    })

    sendVisitorsEventData({
      type: 'lottery_sign_in',
      email: userEmail,
      source: blok?.source || 'homepage'
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    !inputFlag && setInputFlag(true)

    if (isEmail(userEmail)) {
      setLoading(true)
      setTimeout(() => setLoading(false), 2000)
      setTimeout(() => setExponeaResponse(true), 2000)
      handleTrackingAndExponea()
    }
  }

  return (
    <section
      data-testid={'customSignUp-wrapper'}
      className=""
      {...storyblokEditable(blok)}
      ref={customSignUpRef}
    >
      <CustomSignUpUI
        imageSrc={
          'https://a.storyblok.com/f/164324/210x280/a38b07bc3d/sub-box-bubble.png'
        }
        header={blok?.title}
        subtext={blok?.subTitle}
        buttonText={blok?.buttonText}
        termsConditionsText={blok?.termsAndConditions.value}
        confirmationText={blok?.confirmationMessage.value}
        userEmail={userEmail}
        loading={loading}
        exponeaResponse={exponeaResponse}
        handleSubmit={handleSubmit}
        handleUserInput={handleUserImput}
        inputFlag={inputFlag}
        t={t}
      />
    </section>
  )
}
