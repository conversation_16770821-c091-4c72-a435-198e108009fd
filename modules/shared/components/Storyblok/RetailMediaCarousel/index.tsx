import { Sb<PERSON>lokD<PERSON>, storyblokEditable } from '@storyblok/react'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'

import { getAppBuyerUrl } from '@core/config/url'
import { NUMBER_0 } from '@core/constants/numbers'
import { useCookieConsent } from '@core/cookie-consent/cookie-consent.context'
import { useMarket, useMediaQuery } from '@core/hooks'
import { useUser } from '@core/redux'
import { useCriteoViewItem } from '@core/redux/features/product/hooks/useCriteoViewItem'
import { setCriteoViewItem } from '@core/redux/features/product/productSlice'
import { CountryCode } from '@core/types'

import { buildItemListName } from '@shared/utils/trackingNameBuilder.utils'

import { OK } from '@modules/product/constants'
import {
  DEFAULT_POSITION,
  EVENT_TYPES,
  PAGE_IDS
} from '@modules/product/constants/criteo.constants'
import { getProductList } from '@modules/product/services/get-product-list'
import { CarouselProduct } from '@modules/product/types'
import { ViewItemAPI } from '@modules/product/types/criteo/viewItem'
import {
  generateRetailVisitorID,
  getRetailVisitorID,
  parseCriteoViewItem,
  parseRederingAttributes,
  searchQueryParams
} from '@modules/product/utils'
import { colorsType } from '@modules/shared/constants/storyblok-carousel-teaser.constants'

import { MD_BREAKPOINT, SM_BREAKPOINT } from '@styles/mediaQueriesBreakpoints'

import { ContentProductCarouselSkeleton } from '../ContentProductCarousel/ContentProductCarouselSkeleton'
import { RetailMediaCarouselUI } from './RetailMediaCarouselUI'
import { GaTrackingEventLabel } from '@modules/ga-tracking/constants'

export interface RetailMediaCarouselBlok extends SbBlokData {
  title?: string
  source: string
  color?: colorsType
  trackingName?: string
}

export interface RetailMediaCarouselProps {
  blok: RetailMediaCarouselBlok
}

export const RetailMediaCarousel = ({ blok }: RetailMediaCarouselProps) => {
  const { query } = useRouter()
  const { accountInfo } = useUser()
  const { hasAcceptedCookies } = useCookieConsent()
  const { t } = useTranslation()
  const customerId = accountInfo?.hashedPersonCdmId || null
  const market = useMarket()
  const dispatch = useDispatch()
  const { fetchCriteoViewItem } = useCriteoViewItem()
  const webAppBuyerUrl = getAppBuyerUrl(market)
  const isMediumScreen = useMediaQuery(`(${MD_BREAKPOINT})`)
  const [isMobile, setIsMobile] = useState<boolean>(true)
  const matchesSm = useMediaQuery(`(${SM_BREAKPOINT})`)
  const [products, setProducts] = useState<CarouselProduct[] | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const { route } = useRouter()

  const trackingListName: string = blok?.source !== 'homepage'
    ? GaTrackingEventLabel.CategoryPplCarouselListSponsored
    : GaTrackingEventLabel.HomapageCarouselListSponsored

  useEffect(() => {
    if (hasAcceptedCookies) {
      setLoading(true)
      initializeCriteo()
    } else {
      setLoading(false)
    }
  }, [hasAcceptedCookies])

  useEffect((): void => {
    setIsMobile(matchesSm)
  }, [matchesSm])

  const initializeCriteo = async () => {
    try {
      const payload = createCriteoPayload(market)
      const response: ViewItemAPI = await fetchCriteoViewItem(payload)

      if (response) {
        const products = await fetchProductList(response)
        setProducts(products)
        const parsedCriteoResponse = parseCriteoViewItem(response)
        dispatch(setCriteoViewItem(parsedCriteoResponse))
      }
    } catch (error) {
      return <></>
    } finally {
      setLoading(false)
    }
  }

  const createCriteoPayload = (market: CountryCode) => {
    const pageId = isMediumScreen
      ? PAGE_IDS[blok?.source].MOBILE
      : PAGE_IDS[blok?.source].DESKTOP
    const retailVisitorId = getRetailVisitorID() || generateRetailVisitorID()
    const eventType = EVENT_TYPES[blok?.source]
    const viewItemBlock = `${pageId}-${DEFAULT_POSITION}`
    const categorySlug = (query?.categorySlugOrId as string) || ''

    return {
      customerId,
      pageId,
      retailVisitorId,
      eventType,
      viewItemBlock,
      categorySlug,
      market
    }
  }

  const fetchProductList = async (response) => {
    const productIdsQuery = searchQueryParams(response?.products)
    const productListResponse = await getProductList(market, productIdsQuery)

    if (
      productListResponse?.type === OK &&
      productListResponse?.result?.items.length > NUMBER_0
    ) {
      /*  merge the criteo & search response in required
       *  format together and storing becon urls in state for later use */
      const parsedCriteoResponse = parseCriteoViewItem(response)
      /* tap products objects to add sposoring attributes manually
               this will be more dynamic when we started to use mixed up product carousels */
      return (
        parseRederingAttributes(productListResponse, parsedCriteoResponse) ||
        productListResponse.result.items
      )
    }

    return null
  }

  if (loading) {
    return <ContentProductCarouselSkeleton />
  }

  if (!products) {
    return <></>
  }

  return (
    <section className="storyblok-blok-container" {...storyblokEditable(blok)}>
      <RetailMediaCarouselUI
        title={blok.title || t('RETAIL.MEDIA.CAROUSEL.TITLE')}
        trackingName={blok?.trackingName}
        color={blok.color}
        products={products}
        isMobile={isMobile}
        webAppBuyerUrl={webAppBuyerUrl}
        isTransparent={!blok.color}
        itemListName={trackingListName}
        isCategoryPage={blok?.source !== 'homepage'}
      />
    </section>
  )
}
