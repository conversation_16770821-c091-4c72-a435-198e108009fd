import { useTranslation } from 'next-i18next'

import {
  ButtonWithIcon,
  Header,
  ProductAdvancedCarousel
} from '@shared/components'
import {
  CarouselTypes,
  GenericCarouselProps
} from '@shared/components/Storyblok/AdvancedCarousel/types'
import { StoryblokButton } from '@shared/types/Storyblok'

import { AICarouselPretitleType } from '@modules/search/types/ai-search-config.type'

interface TextboxCarouselProps extends GenericCarouselProps {
  subHeading?: string
  description?: string
  button: Array<StoryblokButton>
  customBtnWithIconClasses?: string
  preTitle?: AICarouselPretitleType
}
export function TextboxCarousel({
  dynamicEditorStyle,
  title,
  preTitle,
  trackingName,
  subHeading,
  description,
  button,
  products,
  isMobile,
  isPromo,
  webAppBuyerUrl,
  currentClasses,
  hasReferencePrice,
  hasBasePriceOrMoreThanOneUnit,
  onProductCardLoadingComplete,
  itemListName,
  mustOpenPDPInOtherTab = false,
  customBtnWithIconClasses,
  isMobileOrTablet
}: TextboxCarouselProps): JSX.Element {
  const { t } = useTranslation()

  const loadHeaderTitleStyle = (): string => {
    if (description && !!button.length) {
      return 'text-[20px] md:text-2xl'
    }

    return 'text-[20px] md:text-4xl'
  }

  return (
    <div
      data-testid="textbox-carousel-wrapper"
      className={`${currentClasses.mainBackgroundColor} ${dynamicEditorStyle} pl-4 md:pl-0`}
    >
      <div className="flex flex-col md:flex-row">
        <div
          data-testid="textbox-carousel-header-wrapper"
          className={`flex w-full md:w-[35%] lg:w-[30%] xl:w-[25%] py-4 md:py-6 md:pl-10 md:pr-6 md:gap-2 ${isPromo ? 'pr-4' : ''}`}
        >
          <Header
            type={CarouselTypes.TITLE_WITH_TEXTBOX}
            title={title}
            preTitle={preTitle}
            subHeading={subHeading}
            description={description}
            button={button}
            isMobile={isMobile}
            isPromo={isPromo}
            webAppBuyerUrl={webAppBuyerUrl}
            t={t}
            currentClasses={currentClasses}
            customStyle={`flex md:flex-col md:justify-center ${
              isPromo
                ? 'flex-row-reverse flex-grow justify-between md:justify-center gap-3 items-center md:items-start'
                : 'flex-col gap-2'
            }`}
            titleStyle={`font-metro-ca uppercase ${loadHeaderTitleStyle()}`}
            customBtnWithIconClasses={customBtnWithIconClasses}
            isMobileOrTablet={isMobileOrTablet}
          />
        </div>

        <div className="block relative md:w-[65%] lg:w-[70%] xl:w-[75%] md:pt-4">
          <ProductAdvancedCarousel
            carouselTitle={title}
            trackingName={trackingName}
            products={products}
            hasReferencePrice={hasReferencePrice}
            hasBasePriceOrMoreThanOneUnit={hasBasePriceOrMoreThanOneUnit}
            isMobile={isMobile}
            onProductCardLoadingComplete={onProductCardLoadingComplete}
            itemListName={itemListName}
            mustOpenPDPInOtherTab={mustOpenPDPInOtherTab}
          />
          {isMobile && (
            <ButtonWithIcon
              type={CarouselTypes.TITLE_WITH_TEXTBOX}
              button={button}
              isMobile={isMobile}
              webAppBuyerUrl={webAppBuyerUrl}
              t={t}
              currentClasses={currentClasses}
              customBtnWithIconClasses={customBtnWithIconClasses}
            />
          )}
        </div>
      </div>
    </div>
  )
}
