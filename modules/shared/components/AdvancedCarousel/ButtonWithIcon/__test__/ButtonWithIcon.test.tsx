import { render, screen } from '@testing-library/react'

import { CarouselTypes } from '@shared/components/Storyblok/AdvancedCarousel/types'

import { SVG_NAMES } from '@modules/shared/icons/constants'

import { ButtonWithIcon } from '..'

describe('ButtonWithIcon Component', () => {
  const mockTranslate = (key) => key
  const webAppBuyerUrl = 'https://example.com'

  const currentClasses = {
    bgButton: 'bg-custom',
    buttonColor: 'text-white',
    buttonColorMobile: 'text-black'
  }
  const mockType = CarouselTypes.TITLE_WITH_TEXTBOX

  it('should not render when button array is empty', () => {
    render(
      <ButtonWithIcon
        type={mockType}
        button={[]}
        isMobile={false}
        webAppBuyerUrl={webAppBuyerUrl}
        t={mockTranslate}
        currentClasses={currentClasses}
      />
    )

    const buttonElement = screen.queryByRole('link')
    expect(buttonElement).not.toBeInTheDocument()
  })

  it('should render with external link on desktop', () => {
    const button = [
      {
        isExternalLink: true,
        link: 'https://external.com',
        text: 'External Link'
      }
    ]

    render(
      <ButtonWithIcon
        type={mockType}
        button={button}
        isMobile={false}
        webAppBuyerUrl={webAppBuyerUrl}
        t={mockTranslate}
        currentClasses={currentClasses}
      />
    )

    const buttonElement = screen.getByRole('link', { name: 'External Link' })
    expect(buttonElement).toBeInTheDocument()
    expect(buttonElement).toHaveAttribute('href', 'https://external.com')
  })

  it('should render with internal link on desktop', () => {
    const button = [
      { isExternalLink: false, link: '/internal', text: 'Internal Link' }
    ]

    render(
      <ButtonWithIcon
        type={mockType}
        button={button}
        isMobile={false}
        webAppBuyerUrl={webAppBuyerUrl}
        t={mockTranslate}
        currentClasses={currentClasses}
      />
    )

    const buttonElement = screen.getByRole('link', { name: 'Internal Link' })
    expect(buttonElement).toBeInTheDocument()
    expect(buttonElement).toHaveAttribute(
      'href',
      'https://example.com/internal'
    )
  })

  it('should render correctly in mobile view', () => {
    const button = [
      {
        isExternalLink: true,
        link: 'https://external.com',
        text: 'Mobile Link'
      }
    ]

    render(
      <ButtonWithIcon
        type={mockType}
        button={button}
        isMobile={true}
        webAppBuyerUrl={webAppBuyerUrl}
        t={mockTranslate}
        currentClasses={currentClasses}
      />
    )

    const buttonElement = screen.getByRole('link', { name: 'Mobile Link' })
    expect(buttonElement).toBeInTheDocument()
  })

  it('should render the default text when button text is missing', () => {
    const button = [
      { isExternalLink: true, link: 'https://external.com', text: '' }
    ]

    render(
      <ButtonWithIcon
        type={mockType}
        button={button}
        isMobile={false}
        webAppBuyerUrl={webAppBuyerUrl}
        t={mockTranslate}
        currentClasses={currentClasses}
      />
    )

    const buttonElement = screen.getByRole('link', {
      name: 'CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.SEE_ALL'
    })
    expect(buttonElement).toBeInTheDocument()
    expect(buttonElement).toHaveAttribute('href', 'https://external.com')
  })
})
