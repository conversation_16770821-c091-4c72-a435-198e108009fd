import { CarouselTypes } from '@shared/components/Storyblok/AdvancedCarousel/types'
import { StoryblokButton } from '@shared/types/Storyblok'

import { LinkButton, SVGIcon } from '@modules/shared/components'
import { SVG_NAMES } from '@modules/shared/icons/constants'

type ButtonProps = {
  type: CarouselTypes
  button: StoryblokButton[]
  isMobile: boolean
  webAppBuyerUrl: string
  t: (key: string) => string
  currentClasses: any
  customBtnWithIconClasses?: string
}
export const ButtonWithIcon = ({
  type,
  button,
  isMobile,
  webAppBuyerUrl,
  t,
  currentClasses,
  customBtnWithIconClasses
}: ButtonProps) => {
  const buttonLink = button.length
    ? button[0].isExternalLink
      ? button[0].link
      : `${webAppBuyerUrl}${button[0].link}`
    : ''

  if (!button?.length || type === CarouselTypes.TITLE_WITH_CAROUSEL) return null

  return (
    <LinkButton
      url={buttonLink}
      className={`flex ${isMobile ? 'justify-end mb-3' : 'justify-start'}`}
      variant={null}
      border={null}
    >
      <a
        className={`flex gap-2 px-4 py-2 text-[16px] h-[40px] font-lato items-center text-blue-interaction rounded-none font-extrabold ${
          isMobile ? 'bg-inherit' : currentClasses.bgButton
        } ${
          isMobile
            ? currentClasses.buttonColorMobile
            : currentClasses.buttonColor
        } ${customBtnWithIconClasses ? customBtnWithIconClasses : ''}`}
      >
        {button[0]?.text || t('CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.SEE_ALL')}
        <SVGIcon
          name={SVG_NAMES.ARROW_RIGHT}
          className="ml-4px"
          svgFill={`${currentClasses.buttonColor}`}
          width="24px"
        />
      </a>
    </LinkButton>
  )
}
