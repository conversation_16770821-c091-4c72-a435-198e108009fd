import { <PERSON>er, DrawerContent, DrawerHeader } from '@/components/ui/drawer'
import React, { useState } from 'react'

import { SVGIcon, Tooltip } from '@shared/components'
import { SVG_NAMES } from '@shared/icons/constants'

import { AICarouselPretitleType } from '@modules/search/types/ai-search-config.type'

interface Props {
  preTitle?: AICarouselPretitleType
  isMobileOrTablet?: boolean
  t?: (key: string) => string
}

export const Pretitle = ({ preTitle, isMobileOrTablet, t }: Props) => {
  const [openAIToolbarDescMobile, setOpenAIToolbarDescMobile] =
    useState<boolean>(false)
  return (
    <div className={'flex gap-1 font-metro-ca-800 text-metro-blue-tint-20'}>
      <SVGIcon dataTestid="svg-icon" name={preTitle.svgName} width={'20px'} />
      <p>{preTitle.text}</p>
      {isMobileOrTablet && (
        <SVGIcon
          className="mt-1"
          width="20px"
          height="20px"
          name={SVG_NAMES.ALERT_INFO}
          fill="#33578E"
          onClick={() => setOpenAIToolbarDescMobile(true)}
        />
      )}
      {openAIToolbarDescMobile && (
        <Drawer
          open={openAIToolbarDescMobile}
          onClose={() => setOpenAIToolbarDescMobile(false)}
        >
          <DrawerContent
            className="!bg-white-main flex max-h-[85%] justify-top z-[70] p-6 pb-12 font-lato"
            test-target="ai-toolbar-desc-mobile-drawer"
          >
            <div
              data-testid="ai-toolbar-desc-mobile"
              className={'flex flex-col gap-4'}
            >
              <DrawerHeader className="p-0 gap-4">
                <div
                  className={'cursor-pointer flex justify-end'}
                  onClick={() => setOpenAIToolbarDescMobile(false)}
                >
                  <SVGIcon
                    name={SVG_NAMES.SIDEBAR_CLOSE}
                    width="24px"
                    height="24px"
                  />
                </div>
                <div className="flex gap-1 text-[20px] leading-6 font-extrabold text-primary-main font-metro-ca items-center">
                  <SVGIcon
                    className={'m-2'}
                    width="32px"
                    height="32px"
                    name={SVG_NAMES.AI_SEARCH_ICON}
                  />
                  {preTitle.text}
                </div>
              </DrawerHeader>
              <div className={'text-base font-Lato-400 text-primary-main'}>
                {preTitle.tooltipDescription}
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      )}
      {!isMobileOrTablet && (
        <Tooltip
          tooltipDescription={preTitle.tooltipDescription}
          defaultPosition={preTitle.tooltipPosition}
          className={'text-white-main bg-grey-base font-light'}
        >
          <span className="flex items-center cursor-pointer">
            <SVGIcon
              className="mt-1"
              width="20px"
              height="20px"
              name={SVG_NAMES.ALERT_INFO}
              fill="#33578E"
            />
          </span>
        </Tooltip>
      )}
    </div>
  )
}
