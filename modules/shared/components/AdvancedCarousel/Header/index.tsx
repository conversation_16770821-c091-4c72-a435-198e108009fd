import { Tooltip } from '@shared/components'
import { Pretitle } from '@shared/components/AdvancedCarousel/Header/Pretitle'
import { CarouselTypes } from '@shared/components/Storyblok/AdvancedCarousel/types'
import { StoryblokButton } from '@shared/types/Storyblok'

import { AICarouselPretitleType } from '@modules/search/types/ai-search-config.type'
import { SVG_NAMES } from '@modules/shared/icons/constants'

import { SVGIcon } from '../../SvgIcon'
import { ButtonWithIcon } from '../ButtonWithIcon'
import { Description } from '../Description'
import { SubHeading } from '../SubHeading'

type HeaderProps = {
  type?: CarouselTypes
  preTitle?: AICarouselPretitleType
  title?: string
  subHeading?: string
  description?: string
  button?: StoryblokButton[]
  isMobile?: boolean
  isPromo?: boolean
  webAppBuyerUrl?: string
  t?: (key: string) => string
  isHorizontalLayout?: boolean
  currentClasses?: any
  customStyle?: string
  titleStyle?: string
  customBtnWithIconClasses?: string
  isMobileOrTablet?: boolean
}

const renderPromoIcon = (isMobile: boolean, currentClasses: any) => (
  <SVGIcon
    dataTestid="svg-icon"
    name={SVG_NAMES.PROMO_ICON}
    className={currentClasses.icon}
    width={isMobile ? '61.47px' : '74px'}
    height={isMobile ? '40px' : '48px'}
    fill={currentClasses.discountIcon}
  />
)

export const Header = ({
  type,
  title,
  preTitle,
  subHeading,
  description,
  button,
  isPromo,
  webAppBuyerUrl,
  isMobile,
  t,
  isHorizontalLayout = false,
  currentClasses,
  customStyle,
  titleStyle,
  customBtnWithIconClasses,
  isMobileOrTablet
}: HeaderProps) => {
  return (
    <header className={customStyle}>
      {isPromo && !isHorizontalLayout ? (
        renderPromoIcon(isMobile, currentClasses)
      ) : (
        <SubHeading
          type={type}
          subHeading={subHeading}
          currentClasses={currentClasses}
        />
      )}

      <div
        className={`${
          isPromo && isMobile ? 'max-w-[80%]' : 'md:max-w-none'
        } flex flex-col gap-1 
          ${!isMobile && type !== CarouselTypes.TITLE_WITH_CAROUSEL ? 'mb-2' : 'mb-0'}`}
      >
        {preTitle && (
          <Pretitle
            preTitle={preTitle}
            isMobileOrTablet={isMobileOrTablet}
            t={t}
          />
        )}
        <h1
          data-testid="title"
          className={`font-extrabold ${currentClasses.text} ${titleStyle}`}
        >
          {title}
        </h1>
        {!isHorizontalLayout && (
          <Description
            type={type}
            description={description}
            isMobile={isMobile}
            currentClasses={currentClasses}
          />
        )}
      </div>

      {!isMobile && !isHorizontalLayout && (
        <ButtonWithIcon
          type={type}
          button={button}
          isMobile={isMobile}
          webAppBuyerUrl={webAppBuyerUrl}
          t={t}
          currentClasses={currentClasses}
          customBtnWithIconClasses={customBtnWithIconClasses}
        />
      )}

      {isHorizontalLayout &&
        isPromo &&
        renderPromoIcon(isMobile, currentClasses)}
    </header>
  )
}
