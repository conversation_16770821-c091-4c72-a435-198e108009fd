export const classes = {
  base: 'rounded-sm transition',
  size: {
    small: 'px-2 py-[2px] text-sm',
    normal: 'px-4 py-1',
    large: 'px-8 py-2 text-lg'
  },
  width: {
    small: '!px-4',
    medium: '!px-8',
    large: '!px-16'
  },
  disabled: 'bg-disabled-button !text-[#677283] cursor-not-allowed',
  isDisabledOnEmptyInput:
    'bg-blue-tint-40 pointer-events-none  cursor-not-allowed',
  pill: '!rounded-full',
  fullWidth: 'w-full',
  isLoading: 'cursor-wait flex justify-center items-center',
  border: {
    primary: 'border-2 border-solid border-white-main',
    secondary: 'border-2 border-solid',
    info: 'border-2 border-solid',
    infoAccessible: 'border-2 border-solid',
    danger: 'border-2 border-solid',
    active: 'border-2 border-solid border-blue-tint-80'
  },
  variant: {
    primary: `bg-primary-main 
      hover:bg-blue-shade-40
      focus:ring-2
      focus:ring-blue-shade-40
      focus:ring-opacity-50
      text-white-main
      transition`,
    secondary: `bg-secondary-main 
      hover:bg-blue-shade-20
      focus:ring-2
      focus:ring-blue-share-20
      focus:ring-opacity-50
      text-white-main
      transition`,
    info: `bg-blue-tint-90 
      hover:bg-blue-tint-80
      focus:ring-2
      focus:ring-blue-tint-80
      focus:ring-opacity-50
      text-primary-main
      font-bold
      transition`,
    infoAccessible: `bg-blue-base
      hover:bg-blue-tint-80
      focus:ring-2
      focus:ring-blue-tint-80
      focus:ring-opacity-50
      text-blue-interaction
      font-bold
      font-lato
      transition`,
    danger: `bg-error-main 
      hover:bg-red-shade-20
      focus:ring-2
      focus:ring-red-500
      focus:ring-opacity-50
      text-white-main
      transition`,
    outline: `bg-white-main
      !rounded-md
      border-secondary-main
      text-secondary-main
      transition`,
    active: 'bg-blue-tint-80 text-primary-main',
    ghost: ''
  },
  rounded: '!rounded-md'
}
