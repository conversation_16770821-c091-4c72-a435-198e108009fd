import { Navigation, Swiper, SwiperOptions } from 'swiper'
import 'swiper/swiper-bundle.min.css'
import 'swiper/swiper.min.css'

type Props = {
  swiperEl: string
  swiperOptions: SwiperOptions
  handleSlideChange?: ({
    isBeginning,
    isEnd
  }: {
    isBeginning: boolean
    isEnd: boolean
  }) => void
}

// eslint-disable-next-line react-hooks/rules-of-hooks
Swiper.use([Navigation])

export const createSwiper = ({
  swiperEl,
  swiperOptions,
  handleSlideChange
}: Props): Swiper => {
  const swiper = new Swiper(swiperEl, swiperOptions)

  if (handleSlideChange && typeof swiper.on === 'function') {
    swiper.on('slideChange', handleSlideChange)
  }

  return swiper
}
