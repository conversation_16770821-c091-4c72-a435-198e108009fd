export const SVG_NAMES = {
  BOX_STAIRS: 'BOX_STAIRS',
  CHEVRON_LEFT: 'CHEVRON_LEFT',
  CHEVRON_LEFT_BLUEGREY: 'CHEVRON_LEFT_BLUEGREY',
  CHE<PERSON>ON_RIGHT: 'CHEVR<PERSON>_RIGHT',
  CH<PERSON><PERSON><PERSON>_RIGHT_BLUEGREY: 'CHEVRON_RIGHT_BLUEGREY',
  CHEVRON_UP: 'CHEVRON_UP',
  CHEVRON_DOWN: 'CHEVRON_DOWN',
  CHEVRON_SMALL_RIGHT: 'CHEVRON_SMALL_RIGHT',
  CHEVRON_SMALL_LEFT: 'CHEVRON_SMALL_LEFT',
  <PERSON><PERSON><PERSON>OAD: 'DOWNLOAD',
  ICON_BELIEFERUNG: 'ICON_BELIEFERUNG',
  ICON_INFORMATION: 'ICON_INFORMATION',
  ICON_MEINMARKT: 'ICON_MEINMARKT',
  ICON_ONLINESHOP_ACTIVE: 'ICON_ONLINESHOP_ACTIVE',
  FOOTER_MAIL: 'FOOTER_MAIL',
  FOOTER_PHONE: 'FOOTER_PHONE',
  FOOTER_BROWSE: 'FOOTER_BROWSE',
  PAY_LESS_WITH_VOLUME_PRICING_ICO: 'PAY_LESS_WITH_VOLUME_PRICING_ICO',
  '100_TRUSTABLE_SELLERS': '100_TRUSTABLE_SELLERS',
  MASTER_CARD_PAYMENT_ICON: 'MASTER_CARD_PAYMENT_ICON',
  SOFORT: 'SOFORT',
  SOFORT_PAYMENT_METHOD_ICON: 'SOFORT_PAYMENT_METHOD_ICON',
  VISA_PAYMENT_ICON: 'VISA_PAYMENT_ICON',
  INVOICE_PAYMENT_ICON: 'INVOICE_PAYMENT_ICON',
  SEPA_PAYMENT_ICON: 'SEPA_PAYMENT_ICON',
  IDEAL_PAYMENT_ICON: 'IDEAL_PAYMENT_ICON',
  PAYPAL_PAYMENT_ICON: 'PAYPAL_PAYMENT_ICON',
  RATEAPAY_PAYMENT_ICON: 'RATEPAY_PAYMENT_ICON',
  PAYPAL_PAYMENT_METHOD_ICON: 'PAYPAL_PAYMENT_METHOD_ICON',
  PAYMENT_METHOD_VISA_ICON: 'PAYMENT_METHOD_VISA_ICON',
  PAYMENT_METHOD_MASTERCARD_ICON: 'PAYMENT_METHOD_MASTERCARD_ICON',
  PAYMENT_METHOD_IDEAL_ICON: 'PAYMENT_METHOD_IDEAL_ICON',
  PAYMENT_METHOD_BILLIE_ICON: 'PAYMENT_METHOD_BILLIE_ICON',
  PAYMENT_METHOD_SEPA_ICON: 'PAYMENT_METHOD_SEPA_ICON',
  PAYMENT_METHOD_INVOICE_ICON: 'PAYMENT_METHOD_INVOICE_ICON',
  PAYMENT_METHOD_CARD_BANCAIRES: 'PAYMENT_METHOD_CARD_BANCAIRES',
  ALERT_INFO_BLACK_ICON: 'ALERT_INFO_BLACK_ICON',
  SEARCH: 'SEARCH',
  CAMERA: 'CAMERA',
  IMAGE_FILE: 'IMAGE_FILE',
  IMAGE_FILE_CAMERA: 'IMAGE_FILE_CAMERA',
  CAMERA_POINTER: 'CAMERA_POINTER',
  METRO_LOGO_ICON: 'METRO_LOGO_ICON',
  MAKRO_LOGO_ICON: 'MAKRO_LOGO_ICON',
  CAMERA_SWITCH: 'CAMERA_SWITCH',
  FILTER: 'FILTER',
  QUESTION_MARK: 'QUESTION_MARK',
  HISTORY: 'HISTORY',
  BUSINESS: 'BUSINESS',
  CATEGORY: 'CATEGORY',
  CLOSE: 'CLOSE',
  CLOSE_IMAGE_SEARCH_PREVIEW: 'CLOSE_IMAGE_SEARCH_PREVIEW',
  CLOSE_1: 'CLOSE_1',
  CLOSE_V2: 'CLOSE_V2',
  SIDEBAR_CLOSE: 'SIDEBAR_CLOSE',
  SIDEBAR_BACK: 'SIDEBAR_BACK',
  CART: 'CART',
  CART_PAGE_EMPTY_CART: 'CART_PAGE_EMPTY_CART',
  BASKET: 'BASKET',
  TRASH: 'TRASH',
  EMPTY_CART: 'EMPTY_CART',
  EDIT: 'EDIT',
  ADD: 'ADD',
  ARROW_LEFT: 'ARROW_LEFT',
  ARROW_RIGHT: 'ARROW_RIGHT',
  NEWS: 'NEWS',
  MENU_ICON: 'MENU_ICON',
  AVATAR: 'AVATAR',
  CHAT: 'CHAT',
  TIMER: 'TIMER',
  PHONE: 'PHONE',
  HELP: 'HELP',
  INFO: 'INFO',
  REGISTER_APPS: 'REGISTER_APPS',
  TAG: 'TAG',
  SOLID_TAG: 'SOLID_TAG',
  LISTS: 'LISTS',
  WARNING: 'WARNING',
  CHECKMARK: 'CHECKMARK',
  LOADER: 'LOADER',
  VISIBILITY: 'VISIBILITY',
  VISIBILITY_OFF: 'VISIBILITY_OFF',
  CROSS: 'CROSS',
  CHECK_MARK_SMALL: 'CHECK_MARK_SMALL',
  CHECK_MARK_LARGE: 'CHECK_MARK_LARGE',
  ADD_TO_CART: 'ADD_TO_CART',
  ADD_TO_CART_V2: 'ADD_TO_CART_V2',
  MEDIUM_SHOP: 'MEDIUM_SHOP',
  DELIVERY: 'DELIVERY',
  DELIVERY_ERROR: 'DELIVERY_ERROR',
  COMPANY: 'COMPANY',
  CARTE_BANCAIRES: 'CARTE_BANCAIRES',
  PINCH_ZOOM: 'PINCH_ZOOM',
  PROMO_ICON: 'PROMO_ICON',
  ALERT_INFO_SMALL: 'ALERT_INFO_SMALL',
  ALERT_SUCCESS_SMALL: 'ALERT_SUCCESS_SMALL',
  EMPLOYEE_PROFILE_ICON: 'EMPLOYEE_PROFILE_ICON',
  IMPERSONATION_PROFILE_ICON: 'IMPERSONATION_PROFILE_ICON',
  ORDER_HISTORY_ICON: 'ORDER_HISTORY_ICON',
  EXIT_ICON: 'EXIT_ICON',
  WARNING_CIRCLE: 'WARNING_CIRCLE',
  ALCOHOL_WARNING: 'ALCOHOL_WARNING',
  CALENDER: 'CALENDER',
  CALENDER_ADD: 'CALENDER_ADD',
  SHARE_PAYMENT_LINK: 'SHARE_PAYMENT_LINK',
  SHARE_PAYMENT_LINK_SUCCESS: 'SHARE_PAYMENT_LINK_SUCCESS',
  SHARE_PAYMENT_LINK_ERROR: 'SHARE_PAYMENT_LINK_ERROR',
  STEPPER_CHECK_MARK: 'STEPPER_CHECK_MARK',
  INPUT_ERROR_ICON: 'INPUT_ERROR_ICON',
  LINK_TAG: 'LINK_TAG',
  RESEND: 'RESEND',
  DOCUMENTS: 'DOCUMENTS',
  DISCOUNT: 'DISCOUNT',
  BUY_AGAIN: 'BUY_AGAIN',
  BUSSINESS_TYPES: 'BUSSINESS_TYPES',
  CLEANING_AND_HYGIENE: 'CLEANING_AND_HYGIENE',
  COOLING_TECHNOLOGY: 'COOLING_TECHNOLOGY',
  FOOD: 'FOOD',
  FURNITURE: 'FURNITURE',
  KITCHEN_ACCESSORIES: 'KITCHEN_ACCESSORIES',
  KITCHEN_EQUIPMENT: 'KITCHEN_EQUIPMENT',
  LOGISTICS_AND_MAINTENANCE: 'LOGISTICS_AND_MAINTENANCE',
  TABLE_SETTING: 'TABLE_SETTING',
  TO_GO_AND_DISPOSABLES: 'TO_GO_AND_DISPOSABLES',
  OVERVIEW: 'OVERVIEW',
  BOLD_ALERT_INFO_SMALL: 'BOLD_ALERT_INFO_SMALL',
  EMPTY_TABLE: 'EMPTY_TABLE',
  SHOW_LIST: 'SHOW_LIST',
  USER_VALIDATE_EMAIL: 'USER_VALIDATE_EMAIL',
  LOGIN_LOCK: 'LOGIN_LOCK',
  REBUY_GUARANTEE: 'REBUY_GUARANTEE',
  REBUY_GUARANTEE_MOBILE: 'REBUY_GUARANTEE_MOBILE',
  THERMAL_SHOCK_RESISTANT: 'THERMAL_SHOCK_RESISTANT',
  THERMAL_SHOCK_RESISTANT_MOBILE: 'THERMAL_SHOCK_RESISTANT_MOBILE',
  STACKABLE: 'STACKABLE',
  STACKABLE_MOBILE: 'STACKABLE_MOBILE',
  OVENPROOF: 'OVENPROOF',
  OVENPROOF_MOBILE: 'OVENPROOF_MOBILE',
  MICROWAVE_SAFE: 'MICROWAVE_SAFE',
  MICROWAVE_SAFE_MOBILE: 'MICROWAVE_SAFE_MOBILE',
  DISHWASHER_SAFE: 'DISHWASHER_SAFE',
  DISHWASHER_SAFE_MOBILE: 'DISHWASHER_SAFE_MOBILE',
  PORTUGAL_FLAG: 'PORTUGAL_FLAG',
  GERMANY_FLAG: 'GERMANY_FLAG',
  ITALY_FLAG: 'ITALY_FLAG',
  SPAIN_FLAG: 'SPAIN_FLAG',
  NETHERLAND_FLAG: 'NETHERLAND_FLAG',
  FRANCE_FLAG: 'FRANCE_FLAG',
  LOOP: 'LOOP',
  CHECK_YOUR_INBOX: 'CHECK_YOUR_INBOX',
  ERROR_FILLED: 'ERROR_FILLED',
  AVATAR2: 'AVATAR2',
  AVATAR3: 'AVATAR3',
  LOCK: 'LOCK',
  INBOX: 'INBOX',
  ALERT_INFO: 'ALERT_INFO',
  Add_COMPANY: 'ADD_COMPANY',
  METRO_CARD: 'METRO_CARD',
  METRO_CARD2: 'METRO_CARD2',
  PROFILE_SWITCH: 'PROFILE_SWITCH',
  CHECK_MARK_THIN: 'CHECK_MARK_THIN',
  LOCATION: 'LOCATION',
  LOCATION2: 'LOCATION2',
  LOCATION3: 'LOCATION3',
  INFO_ICON_GENERIC: 'INFO_ICON_GENERIC',
  BOX: 'BOX',
  BOX2: 'BOX2',
  BOX3: 'BOX3',
  CANCEL: 'CANCEL',
  ORDER_RETURN: 'ORDER_RETURN',
  ORDER_RETURN_SHIP: 'ORDER_RETURN_SHIP',
  HAND_MONEY: 'HAND_MONEY',
  VAN_SHUTTLE: 'VAN_SHUTTLE',
  VAN_LOCATION: 'VAN_LOCATION',
  INFO_SKELETON: 'INFO_SKELETON',
  CLIPBOARD: 'CLIPBOARD',
  BORDERLESS_QUESTION_MARK: 'BORDERLESS_QUESTION_MARK',
  DOT: 'DOT',
  INFO_ICON_BLUE_BG: 'INFO_ICON_BLUE_BG',
  FOLDABLE: 'FOLDABLE',
  UV_PROTECTION: 'UV_PROTECTION',
  WEATHER_PROOF: 'WEATHER_PROOF',
  WATER_RESISTANT: 'WATER_RESISTANT',
  WATER_REPELLANT: 'WATER_REPELLANT',
  EXTENDABLE: 'EXTENDABLE',
  NON_STICKING: 'NON_STICKING',
  CORROSION_RESISTANT: 'CORROSION_RESISTANT',
  INDUCTION_COMPATIBLE: 'INDUCTION_COMPATIBLE',
  SANDWICH_BOTTOM: 'SANDWICH_BOTTOM',
  GN_COMPATIBLE: 'GN_COMPATIBLE',
  TEMPERED_GLASS: 'TEMPERED_GLASS',
  WITH_CALLIBRATION: 'WITH_CALLIBRATION',
  MAGNIFIER_ZERO_RESULTS: 'MAGNIFIER_ZERO_RESULTS',
  DELIVERY_CAR: 'DELIVERY_CAR',
  MEDIUM_SHOP_NEW: 'MEDIUM_SHOP_NEW',
  LIST: 'LIST',
  RE_BUY: 'RE_BUY',
  NEW_CART: 'NEW_CART',
  SHOPPING_LIST: 'SHOPPING_LIST',
  SHOPPING_LIST_ADDED: 'SHOPPING_LIST_ADDED',
  OUT_OF_SCOPE_BOX_ZERO_RESULTS: 'OUT_OF_SCOPE_BOX_ZERO_RESULTS',
  OUT_OF_SCOPE_BOX_ZERO_RESULTS_HAPPY_FACE:
    'OUT_OF_SCOPE_BOX_ZERO_RESULTS_HAPPY_FACE',
  RE_BUY_SIDE_MENU: 'RE_BUY_SIDE_MENU',
  LIST_SIDE_MENU: 'LIST_SIDE_MENU',
  PLUS_ICON: 'PLUS_ICON',
  MINUS_ICON: 'MINUS_ICON',
  TRUCK: 'TRUCK',
  CART_CHECK: 'CART_CHECK',
  ALERT: 'ALERT',
  CREATE_LIST: 'CREATE_LIST',
  UPDATE_LIST: 'UPDATE_LIST',
  TICK_STAR_ICON: 'TICK_STAR_ICON',
  DIGITAL_ACCOUNT_AVATAR: 'DIGITAL_ACCOUNT_AVATAR',
  DOWNLOAD_ICON: 'DOWNLOAD_ICON',
  LEFT_ARROW: 'LEFT_ARROW',
  APPROVALS: 'APPROVALS',
  CHECK_NEWSLETTER_GREEN: 'CHECK_NEWSLETTER_GREEN',
  CHECK_NEWSLETTER_WHITE: 'CHECK_NEWSLETTER_WHITE',
  CHIP_CLOSE: 'CHIP_CLOSE',
  PLAY_ICON: 'PLAY_ICON',
  PLAY_ICON_NO_CONTAINER: 'PLAY_ICON_NO_CONTAINER',
  ADJUSTABLE_SHELVES: 'ADJUSTABLE_SHELVES',
  ADJUSTABLE_SHELVES_V2: 'ADJUSTABLE_SHELVES_V2',
  LOCKABLE: 'LOCKABLE',
  LOCKABLE_V2: 'LOCKABLE_V2',
  FRIDGE_LIGHT: 'FRIDGE_LIGHT',
  FRIDGE_LIGHT_V2: 'FRIDGE_LIGHT_V2',
  TEMPERATURE_DISPLAY: 'TEMPERATURE_DISPLAY',
  TEMPERATURE_DISPLAY_V2: 'TEMPERATURE_DISPLAY_V2',
  STATIC_COOLING: 'STATIC_COOLING',
  VENTILATED_COOLING: 'VENTILATED_COOLING',
  CONTROL_PANEL: 'CONTROL_PANEL',
  CONTROL_PANEL_V2: 'CONTROL_PANEL_V2',
  DOOR_OPEN_ALARM: 'DOOR_OPEN_ALARM',
  DOOR_OPEN_ALARM_V2: 'DOOR_OPEN_ALARM_V2',
  AI_STARS: 'AI_STARS',
  AI_SEARCH_ICON: 'AI_SEARCH_ICON'
} as const

export type SvgNames = (typeof SVG_NAMES)[keyof typeof SVG_NAMES]

const STROKE_LINECAP = {
  inherit: 'inherit',
  butt: 'butt',
  round: 'round',
  square: 'square'
} as const

export type strokeLinecap = (typeof STROKE_LINECAP)[keyof typeof STROKE_LINECAP]

const STROKE_LINEJOIN = {
  inherit: 'inherit',
  round: 'round',
  miter: 'miter',
  bevel: 'bevel'
} as const

export type strokeLinejoin =
  (typeof STROKE_LINEJOIN)[keyof typeof STROKE_LINEJOIN]
