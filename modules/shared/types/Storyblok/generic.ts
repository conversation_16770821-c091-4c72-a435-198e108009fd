import { SbBlokData } from '@storyblok/react'

export interface SbImage {
  alt?: string
  filename: string
  id?: string
  name?: string
  source?: string
  title?: string
}

export interface StoryblokButton {
  text: string
  link: string
  isExternalLink: boolean
}

export interface Banner {
  url: string
  device: string
}
export interface BannerImageBlok extends SbBlokData {
  focal?: any
  bannerLink: string
  title: string
  isExternalLink: boolean
  banners: { banners: Array<Banner> }
  sponsor?: Array<BannerSponsoring>
  trackingName?: string
  altText?: string
}

export interface Brand {
    id?: string
    name?: string
    value?: string
    type?: number
    totalCount?: number
    isFiltered?: boolean
    filtered?: boolean
    code?: string
    logo?: string
    slug?: string
    hasLogos?: string
}

export interface BannerSponsoring extends SbBlokData {
  isSponsored: boolean
  advertiserName: string
  advertiserPayerName: string
  position: SponsoringPosition
}

export type SponsoringPosition =
  (typeof SPONSORING_POSITION)[keyof typeof SPONSORING_POSITION]

export const SPONSORING_POSITION = {
  bottomLeft: 'bottom-left',
  bottomRight: 'bottom-right'
} as const
