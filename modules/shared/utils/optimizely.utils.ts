import optimizelySdk, {
  Client,
  UserAttributes
} from '@optimizely/optimizely-sdk'
import * as Sentry from '@sentry/nextjs'
import Cookies from 'js-cookie'
import { v4 as uuidv4 } from 'uuid'

import { config } from '@core/config'
import { GA_COOKIE, USER_ID_COOKIE } from '@core/constants/cookies'
import { NUMBER_10, NUMBER_180, NUMBER_1000 } from '@core/constants/numbers'
import { COOKIE_DISCLAIMER_COOKIE_NAME } from '@core/constants/shared-constants'
import { OptimizelyAttributesUtil } from '@core/types/optimizely-user-attributes'

export const enum EXPERIMENT_VARIATIONS {
  ORIGINAL = 'original',
  VARIANT = 'variant',
  VARIANT_OFF = 'off',
  CONTROL_GROUP = 'control_group',
  VARIANT_ONE = 'variant_1',
  VARIANT_TWO = 'variant_2',
  VARIANT_THREE = 'variant_3',
  VARIANT_FOUR = 'variant_4',
  VARIANT_FIVE = 'variant_5'
}

export const enum EXPERIMENT_NAMES {
  MACO_REDESIGN_OF_THE_NAVIGATION = 'maco_redesign_of_the_navigation',
  OUTDOOR_USPS = 'outdoor_usps',
  G_RECOMMENDERS_CR = 'google_recommender_cr_2',
  AI_GENERATED_KEY_FEATURES = 'ai_generated_key_features',
  PDP_PROMO_BANNERS = 'pdp_promo_banners',
  ADD_TO_CART_FEEDBACK = 'add_to_cart_feedback',
  IMAGE_SIZE_EXPERIMENT = 'image_size_experiment',
  PDP_ESTIMATED_DELIVERY_TIME = 'pdp_estimated_delivery_time',
  PDP_ESTIMATED_DELIVERY_TIME_DE = 'pdp_est_delivery_time_de',
  PDP_ESTIMATED_DELIVERY_TIME_FR = 'pdp_est_delivery_time_fr',
  DYNAMIC_DELIVERY_PRICE = 'dynamic_delivery_price',
  PRODUCED_BY_METRO_CAROUSEL = 'produced_by_metro_carousel',
  MACO_OPEN_PDP_IN_NEW_TAB = 'opening_pdp_in_a_new_tab_v_2',
  FRIDGE_KEY_FEATURES = 'fridge_key_features',
  SRCH_PRODUCTS_OPEN_IN_NEW_TAB = 'search_products_open_pdp_in_new_tab',
  CATEGORY_SUGGESTIONS_SEARCH_RESULTS_PAGE = 'category_suggestions_search_results_page',
  NEW_PRODUCT_DATA_VIEW_ON_PDP = 'new_product_data_view_on_pdp'
}

class OptimizelyUtils {
  private client: Client | null = null
  constructor() {
    this.initialize()
  }

  public initialize(): void {
    const isCookieAccepted = Cookies.get(
      COOKIE_DISCLAIMER_COOKIE_NAME
    )?.includes?.('|performance|')

    if (!this.client && isCookieAccepted) {
      this.client = this.createClient()
    }
  }

  public async getExperimentVariant<T extends string>(
    experimentId: string,
    isNewIdFF: boolean,
    userAttributes?: UserAttributes
  ): Promise<T | null> {
    if (!this.client || !this.getGAClientId(isNewIdFF)) {
      return null
    }
    await this.client.onReady()
    const optimizelyBag = this.getOptimizelyBag(isNewIdFF)
    const [client] = optimizelyBag
    const attributes = {
      ...OptimizelyAttributesUtil.getDefaultAttributes(),
      ...userAttributes
    }
    const experimentIdLocalStorage: string = localStorage?.getItem(experimentId)

    const methodCalled: string = experimentIdLocalStorage
      ? 'getVariation'
      : 'activate'

    const variant: T | null = experimentIdLocalStorage
      ? (client?.getVariation(
          experimentId,
          this.getGAClientId(isNewIdFF),
          attributes
        ) as T)
      : (client?.activate(
          experimentId,
          this.getGAClientId(isNewIdFF),
          attributes
        ) as T)

    if (
      variant &&
      variant !== 'undefined' &&
      variant !== 'false' &&
      variant !== 'null'
    ) {
      localStorage.setItem(experimentId, variant)
      if (window) {
        window['optimizely_experiments'] = {
          ...window['optimizely_experiments'],
          [experimentId]: variant
        }
      }

      this.sendGAEvent(experimentId, variant, methodCalled)
    }
    return variant
  }

  public getOptimizelyBag: (isNewIdFF: boolean) => [Client, string] = (
    isNewIdFF: boolean
  ) => {
    const client: Client = this.getClient()

    return [client, this.getGAClientId(isNewIdFF)]
  }

  public getClient(): Client {
    this.initialize()
    return this.client
  }

  private createClient(): Client {
    const optimizelyClientInstance: Client = optimizelySdk.createInstance({
      sdkKey: config.optimizelySDKKey,
      eventBatchSize: NUMBER_10,
      eventFlushInterval: NUMBER_1000,
      errorHandler: this.customErrorHandler
    })

    return optimizelyClientInstance
  }

  public getGAClientId(isNewIdFF: boolean): string {
    if (!isNewIdFF) {
      const gaCookie = Cookies.get(GA_COOKIE)

      return gaCookie?.replace(/^GA[1-9][0-9]*\.[1-9][0-9]*\./, '')
    }

    let userId = Cookies.get(USER_ID_COOKIE)
    if (!userId) {
      userId = uuidv4()
      Cookies.set(USER_ID_COOKIE, userId, { expires: NUMBER_180 })
    }
    return userId
  }

  private customErrorHandler = {
    /**
     * handleError
     *
     * Function which gets called when an error is thrown in the SDK
     * @param {Object} error - error object
     * @param {String} error.message - message of the error
     * @param {String} error.stack - stack trace for the error
     */
    handleError: (error) => {
      Sentry.captureException(error)
    }
  }

  public sendGAEvent = (experimentId, variant, methodCalled) => {
    const dataLayer = ((window as any).dataLayer =
      (window as any).dataLayer || [])

    dataLayer.push({
      event: 'activate_experiment',
      category: 'Optimizely',
      action: experimentId,
      label: variant,
      method: methodCalled
    })
  }
}

export const optimizelyInstance = new OptimizelyUtils()
