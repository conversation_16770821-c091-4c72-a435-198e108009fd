import { useEffect, useMemo, useState } from 'react'
import useSWRImmutable from 'swr/immutable'
import { useRouter } from 'next/router'

import { useSelectedCategory } from '@core/redux/features/category/hooks/useSelectedCategory'
import { initializeFetcher } from '@core/services/http/swr-http-fetcher'
import { API_BRANDS_SB } from '@modules/homepage/constants'
import { API_SEARCH_ATTRIBUTE, API_USAGE_TAGS } from '@modules/search/constants'
import { CountryCode } from '@core/types'
import { useMarket } from '@core/hooks'
import { OptionResponse } from '@modules/search/types/app-search-2'
import { NUMBER_0, NUMBER_10, NUMBER_5, NUMBER_6 } from '@core/constants/numbers'
import { Brand } from '../types/Storyblok'

export const useBrandFetch = (brandSelectionIds?: string[]) => {
    const router = useRouter()
    const { locale } = router
    const market: CountryCode = useMarket()
    const { selectedCategory } = useSelectedCategory()
    const isCategoryPage = Object.keys(selectedCategory)?.length > 0

    const [brands, setBrands] = useState<Brand[]>([])
    const [hasLogos, setHasLogos] = useState(false)

    const endpoint = useMemo(() => {
        if (isCategoryPage) {
            return `${API_SEARCH_ATTRIBUTE}?attribute[code]=brand&filter[categories][]=${selectedCategory?.slug}`
        }

        if (!isCategoryPage) {
            const params = new URLSearchParams({
                limit: String(brandSelectionIds?.length),
                minLogos: String(NUMBER_5)
            })
            brandSelectionIds.forEach(id => params.append('brands[]', id))
            return `${API_BRANDS_SB}?${params.toString()}`
        }

        return

    }, [isCategoryPage, brandSelectionIds, selectedCategory])

    const { data, error } = useSWRImmutable(
        endpoint || null,
        endpoint ? initializeFetcher(
            market,
            locale,
            isCategoryPage && API_USAGE_TAGS.maco_brand_search
        ) : null
    )

    const getCategoryBrands = (data: OptionResponse) => {
        const brands: Brand[] = data?.[NUMBER_0]?.options || []

        const filteredBrands = brands.filter(
            (brand: Brand) => brand.value !== 'null' && brand.value !== null
        )

        const initialDisplayedBrands = filteredBrands.slice(NUMBER_0, NUMBER_10)
        const brandsWithLogo = initialDisplayedBrands.filter((b: Brand) => !!b.logo)
        const brandCount = initialDisplayedBrands.length

        const shouldRenderLogos =
            (brandCount === NUMBER_5 && brandsWithLogo.length === NUMBER_5) ||
            (brandCount >= NUMBER_6 &&
                brandCount <= NUMBER_10 &&
                brandsWithLogo.length >= NUMBER_5)

        const finalDisplayedBrands =
            shouldRenderLogos && brandCount >= NUMBER_6
                ? brandsWithLogo.slice(0, brandsWithLogo.length)
                : initialDisplayedBrands

        return {
            brands: finalDisplayedBrands,
            hasLogos: shouldRenderLogos
        }
    }

    useEffect(() => {
        if (!data) return

        if (!isCategoryPage) {
            const fetchedBrands: Brand[] = data?.brands || []
            const brandsWithLogo = fetchedBrands.filter(b => !!b.logo)

            if (data?.hasLogos) {
                setBrands(brandsWithLogo)
                setHasLogos(true)
            } else {
                setBrands(fetchedBrands)
                setHasLogos(false)
            }
        } else {
            const result = getCategoryBrands(data as OptionResponse)
            setBrands(result.brands)
            setHasLogos(result.hasLogos)
        }
    }, [data, isCategoryPage])

    return {
        brands,
        hasLogos,
        isLoading: !data && !error,
        isError: !!error
    }
}
