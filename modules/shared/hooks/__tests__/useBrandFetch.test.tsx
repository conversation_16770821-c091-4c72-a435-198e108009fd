import { renderHook } from '@testing-library/react'
import useSWRImmutable from 'swr/immutable'
import { useBrandFetch } from '../useBrandFetch'
import { useRouter } from 'next/router'
import { useMarket } from '@core/hooks'
import { useSelectedCategory } from '@core/redux/features/category/hooks/useSelectedCategory'
import { initializeFetcher } from '@core/services/http/swr-http-fetcher'

jest.mock('swr/immutable')
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))
jest.mock('@core/hooks', () => ({
  useMarket: jest.fn()
}))
jest.mock('@core/redux/features/category/hooks/useSelectedCategory', () => ({
  useSelectedCategory: jest.fn()
}))
jest.mock('@core/services/http/swr-http-fetcher', () => ({
  initializeFetcher: jest.fn(() => jest.fn())
}))

describe('useBrandFetch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue({ locale: 'en' })
    ;(useMarket as jest.Mock).mockReturnValue('DE')
  })

  it('should fetch brands for homepage with brandSelectionIds', () => {
    const mockBrandData = {
      brands: [
        { value: 'brand1', logo: 'logo1.png' },
        { value: 'brand2', logo: 'logo2.png' }
      ],
      hasLogos: true
    }

    ;(useSelectedCategory as jest.Mock).mockReturnValue({
      selectedCategory: {}
    })

    ;(useSWRImmutable as jest.Mock).mockReturnValue({
      data: mockBrandData,
      error: undefined
    })

    const { result } = renderHook(() => useBrandFetch(['brand1', 'brand2']))

    expect(result.current.brands).toEqual(mockBrandData.brands)
    expect(result.current.hasLogos).toBe(true)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it('should fetch category brands and apply filtering', () => {
    const mockOptionResponse = [
      {
        options: [
          { value: 'brand1', logo: 'logo1.png' },
          { value: 'brand2', logo: null },
          { value: 'null', logo: 'logo3.png' }
        ]
      }
    ]

    ;(useSelectedCategory as jest.Mock).mockReturnValue({
      selectedCategory: { slug: 'category-slug' }
    })

    ;(useSWRImmutable as jest.Mock).mockReturnValue({
      data: mockOptionResponse,
      error: undefined
    })

    const { result } = renderHook(() => useBrandFetch())

    expect(result.current.brands.length).toBeGreaterThan(0)
    expect(result.current.hasLogos).toBe(false)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it('should return isLoading true if data and error are undefined', () => {
    ;(useSelectedCategory as jest.Mock).mockReturnValue({
      selectedCategory: {}
    })

    ;(useSWRImmutable as jest.Mock).mockReturnValue({
      data: undefined,
      error: undefined
    })

    const { result } = renderHook(() => useBrandFetch(['brand1']))

    expect(result.current.isLoading).toBe(true)
    expect(result.current.isError).toBe(false)
  })

  it('should return isError true if there is an error', () => {
    ;(useSelectedCategory as jest.Mock).mockReturnValue({
      selectedCategory: {}
    })

    ;(useSWRImmutable as jest.Mock).mockReturnValue({
      data: undefined,
      error: new Error('Failed to fetch')
    })

    const { result } = renderHook(() => useBrandFetch(['brand1']))

    expect(result.current.isError).toBe(true)
  })
})
