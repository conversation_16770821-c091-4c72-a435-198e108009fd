import { clsx } from 'clsx'
import { useTranslation } from 'next-i18next'
import { useEffect, useRef, useState } from 'react'

import { getAppBuyerUrl } from '@core/config/url'
import {
  NUMBER_0,
  NUMBER_6,
} from '@core/constants/numbers'
import { APP_BRAND_PAGE_ROUTE } from '@core/constants/routesConstants'
import { useGtag, useIsInViewport, useMarket, useMediaQuery } from '@core/hooks'

import { BrandLogos } from '@modules/category/components/BrandList/BrandLogos'
import { BrandNamesUI } from '@modules/category/components/BrandList/BrandNamesUI'
import {
  GA_TRACKING_ACTION,
  GA_TRACKING_EVENT
} from '@modules/ga-tracking/constants'

import { SM_BREAKPOINT } from '@styles/mediaQueriesBreakpoints'
import { Brand } from '@modules/shared/types/Storyblok'

export interface BrandListProps {
  title?: string
  subTitle?: string
  displayedBrands?: Brand[]
  allHaveLogos?: boolean
}

export function BrandList({ title, subTitle, displayedBrands, allHaveLogos }: BrandListProps): JSX.Element {
  const brandsList = useRef(null)
  const { isInViewport } = useIsInViewport(brandsList, {
    threshold: 0.2,
    isConstantlyObserved: true
  })
  const { gtagEvent } = useGtag()
  const { t } = useTranslation()
  const market = useMarket()
  const webAppBuyerUrl = getAppBuyerUrl(market)
  const [isMobile, setIsMobile] = useState(false)
  const matchesSm = useMediaQuery(`(${SM_BREAKPOINT})`)

  useEffect(() => {
    setIsMobile(matchesSm)
  }, [matchesSm])

  useEffect(() => {
    gtagEvent({
      event: GA_TRACKING_EVENT.BestSellingBrandsViewed,
      GA_eventname: GA_TRACKING_EVENT.Navigation,
      action: GA_TRACKING_ACTION.BestSellingBrandsViewed
    })
  }, [isInViewport])

  const onBrandClick = (brandSlug: string): void => {
    gtagEvent({
      event: GA_TRACKING_EVENT.BestSellingBrandsClicked,
      GA_eventname: GA_TRACKING_EVENT.Navigation,
      action: GA_TRACKING_ACTION.BestSellingBrandsClicked,
      label: brandSlug
    })

    window.location.href = `${webAppBuyerUrl}/${APP_BRAND_PAGE_ROUTE}/${brandSlug}`
  }

  return (
    <div
      data-testid="brand-list"
      ref={brandsList}
      className={clsx(
        'flex flex-col justify-center items-center px-4 xl:px-0',
        !allHaveLogos && 'bg-white-main py-4'
      )}
    >
      <div className="flex flex-col items-center text-center mb-4 text-metro-blue-main">
        <h2 className="font-extrabold text-[20px] leading-[30px] font-metro-ca uppercase md:leading-[36px] md:text-[24px]">
          {title || t('SHARED.BRAND_LIST.HEADLINE')}
        </h2>
        {!isMobile && (
          <p className="max-w-[640px] leading-[24px] text-[14px] font-lato">
            {subTitle || t('SHARED.BRAND_LIST.SUBLINE')}
          </p>
        )}
      </div>
      {allHaveLogos ? (
        <BrandLogos
          brands={displayedBrands}
          onBrandClick={onBrandClick}
          isMobile={isMobile}
        />
      ) : (
        <BrandNamesUI
          brands={
            isMobile
              ? displayedBrands.slice(NUMBER_0, NUMBER_6)
              : displayedBrands
          }
          onBrandClick={onBrandClick}
        />
      )}
    </div>
  )
}
