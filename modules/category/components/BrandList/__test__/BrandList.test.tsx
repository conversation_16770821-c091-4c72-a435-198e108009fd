import { screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { BrandList } from '..'
import { renderWithProviders } from '@core/utils/testing'

jest.mock('next-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => `trad:${key}`
  })
}))

jest.mock('@core/hooks', () => ({
  useMediaQuery: jest.fn(),
  useGtag: () => ({ gtagEvent: jest.fn() }),
  useIsInViewport: () => ({ isInViewport: true }),
  useMarket: () => 'es'
}))

jest.mock('@modules/category/components/BrandList/BrandLogos', () => ({
  BrandLogos: ({ brands }: any) => (
    <div data-testid="brand-logos">{brands.map((b: any) => b.value).join(', ')}</div>
  )
}))

jest.mock('@modules/category/components/BrandList/BrandNamesUI', () => ({
  BrandNamesUI: ({ brands }: any) => (
    <div data-testid="brand-names">
      {brands.map((brand: any, i: number) => (
        <div key={i} data-testid="brand-name-item" onClick={() => brand.onBrandClick?.(brand.value)}>
          {brand.value}
        </div>
      ))}
    </div>
  )
}))

const mockUseMediaQuery = require('@core/hooks').useMediaQuery
const mockGtagEvent = require('@core/hooks').useGtag().gtagEvent

describe('BrandList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    delete window.location
    window.location = { href: '' } as Location
  })

  it('renders BrandLogos when allHaveLogos is true', () => {
    mockUseMediaQuery.mockReturnValue(false)

    renderWithProviders(
      <BrandList
        displayedBrands={[{ value: 'nike' }]}
        allHaveLogos={true}
        title="Top brands"
      />
    )

    expect(screen.getByText('Top brands')).toBeInTheDocument()
    expect(screen.getByTestId('brand-logos')).toBeInTheDocument()
    expect(screen.queryByTestId('brand-names')).not.toBeInTheDocument()
  })

  it('renders BrandNamesUI when allHaveLogos is false', () => {
    mockUseMediaQuery.mockReturnValue(false)

    renderWithProviders(
      <BrandList
        displayedBrands={[{ value: 'adidas' }, { value: 'puma' }]}
        allHaveLogos={false}
        title="Our Brands"
      />
    )

    expect(screen.getByText('Our Brands')).toBeInTheDocument()
    expect(screen.getByTestId('brand-names')).toBeInTheDocument()
    expect(screen.queryByTestId('brand-logos')).not.toBeInTheDocument()
  })

  it('slices brand list to 6 when isMobile and no logos', () => {
    mockUseMediaQuery.mockReturnValue(true)

    const brands = Array.from({ length: 10 }, (_, i) => ({ value: `brand${i}` }))

    renderWithProviders(
      <BrandList
        displayedBrands={brands}
        allHaveLogos={false}
      />
    )

    expect(screen.getByTestId('brand-names')).toBeInTheDocument()
    expect(screen.getAllByTestId('brand-name-item')).toHaveLength(6)
  })

  it('does not render subtitle when isMobile is true', () => {
    mockUseMediaQuery.mockReturnValue(true)

    renderWithProviders(
      <BrandList
        displayedBrands={[{ value: 'nike' }]}
        allHaveLogos={false}
        subTitle="Subtitle"
      />
    )

    expect(screen.queryByText(/Subtitle/i)).not.toBeInTheDocument()
  })

  it('uses translation fallback for title and subtitle', () => {
    mockUseMediaQuery.mockReturnValue(false)

    renderWithProviders(
      <BrandList
        displayedBrands={[{ value: 'adidas' }]}
        allHaveLogos={false}
      />
    )

    expect(screen.getByText('trad:SHARED.BRAND_LIST.HEADLINE')).toBeInTheDocument()
    expect(screen.getByText('trad:SHARED.BRAND_LIST.SUBLINE')).toBeInTheDocument()
  })
})

