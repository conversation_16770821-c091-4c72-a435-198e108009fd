import { clsx } from 'clsx'

import { useDynamicIsEditorStyle } from '@core/redux/features/storyblok/hooks'
import { Brand } from '@modules/shared/types/Storyblok'

interface BrandNamesUIProps {
  brands: Array<Brand>
  onBrandClick: (brandSlug: string) => void
}

export function BrandNamesUI({
  brands,
  onBrandClick
}: BrandNamesUIProps): JSX.Element {
  const { dynamicEditorStyle } = useDynamicIsEditorStyle()
  return (
    <div
      data-testid="brand-names"
      className={clsx(
        dynamicEditorStyle,
        'flex flex-row flex-wrap gap-2 items-center justify-center max-w-[900px]'
      )}
    >
      {brands.map((brand, index) => (
        <button
          data-testid={`brand-button-${index}`}
          className="px-4 py-1 bg-white-main hover:bg-secondary-main focus:bg-secondary-main text-secondary-main hover:text-white-main focus:text-white-main border border-secondary-main rounded-30px text-regular font-Lato-400 transition-colors duration-300"
          key={index}
          aria-label={`Redirects to brand ${brand.name}`}
          onClick={() => {
            onBrandClick(brand.value || brand.slug)
          }}
          onKeyDown={() => {
            onBrandClick(brand.value || brand.slug)
          }}
        >
          {brand.name}
        </button>
      ))}
    </div>
  )
}
