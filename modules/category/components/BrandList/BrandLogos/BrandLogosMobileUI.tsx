import { clsx } from 'clsx'
import { useTranslation } from 'next-i18next'
import Image from 'next/image'
import { useMemo, useState } from 'react'

import {
  NUMBER_0,
  NUMBER_3,
  NUMBER_6,
  NUMBER_100
} from '@core/constants/numbers'
import { imageLoader } from '@core/utils/imageLoader'

import { LoadMoreAccordion } from '@shared/components'
import { Brand } from '@modules/shared/types/Storyblok'

interface BrandLogosMobileUIProps {
  brands: Array<Brand>
  dynamicEditorStyle: string
  onBrandClick: (brandSlug: string) => void
}

export function BrandLogosMobileUI({
  brands,
  dynamicEditorStyle,
  onBrandClick
}: BrandLogosMobileUIProps): JSX.Element {
  const { t } = useTranslation()
  const [isOpened, setIsOpened] = useState(false)
  const accordionText = useMemo(() => {
    return isOpened ? t('SHARED.SHOW_LESS.LABEL') : t('SHARED.SHOW_MORE.LABEL')
  }, [isOpened])

  const logoElement = (brand: Brand, index: number): JSX.Element => {
    return (
      <button
        key={index}
        className={clsx(
          'basis-[calc(33%-8px)] w-[calc(33%-8px)] h-100px box-border p-3 rounded-lg bg-white-main',
          brands.length % NUMBER_3 === NUMBER_0 && 'flex-grow'
        )}
        aria-label={`Redirects to brand ${brand.name}`}
        onClick={() => {
          onBrandClick(brand.value || brand.slug)
        }}
        onKeyDown={() => {
          onBrandClick(brand.value || brand.slug)
        }}
      >
        <Image
          loader={imageLoader}
          width={NUMBER_100}
          height={NUMBER_100}
          className="w-full h-full object-contain"
          src={brand.logo}
          alt={`Image about brand ${brand.name}`}
        />
      </button>
    )
  }

  return (
    <div
      data-testid="brand-logos-mobile"
      className={clsx(
        dynamicEditorStyle,
        'w-full flex items-start flex-row flex-wrap content-center gap-2 justify-center'
      )}
    >
      {brands
        .slice(NUMBER_0, NUMBER_6)
        .map((brand, index) => logoElement(brand, index))}
      {brands.length > NUMBER_6 && (
        <LoadMoreAccordion
          containerDataTestId={'load-more-accordion'}
          title={accordionText}
          containerClassName={clsx(
            dynamicEditorStyle,
            'w-full flex items-start flex-row flex-wrap content-center gap-2 justify-center'
          )}
          titleClassName="font-lato text-base font-normal leading-[1.5rem] text-secondary-main"
          headerClassName="pt-2"
          isOpen={isOpened}
          onToggle={() => setIsOpened(!isOpened)}
        >
          {brands
            .slice(NUMBER_6, brands.length)
            .map((brand, index) => logoElement(brand, index))}
        </LoadMoreAccordion>
      )}
    </div>
  )
}
