import { useDynamicIsEditorStyle } from '@core/redux/features/storyblok/hooks'

import { BrandLogosDesktopUI } from '@modules/category/components/BrandList/BrandLogos/BrandLogosDesktopUI'
import { BrandLogosMobileUI } from '@modules/category/components/BrandList/BrandLogos/BrandLogosMobileUI'
import { Brand } from '@modules/shared/types/Storyblok'

interface BrandLogosProps {
  brands: Array<Brand>
  onBrandClick: (brandSlug: string) => void
  isMobile: boolean
}

export function BrandLogos({
  brands,
  onBrandClick,
  isMobile
}: BrandLogosProps): JSX.Element {
  const { dynamicEditorStyle } = useDynamicIsEditorStyle()

  return isMobile ? (
    <BrandLogosMobileUI
      brands={brands}
      dynamicEditorStyle={dynamicEditorStyle}
      onBrandClick={onBrandClick}
    />
  ) : (
    <BrandLogosDesktopUI
      brands={brands}
      dynamicEditorStyle={dynamicEditorStyle}
      onBrandClick={onBrandClick}
    />
  )
}
