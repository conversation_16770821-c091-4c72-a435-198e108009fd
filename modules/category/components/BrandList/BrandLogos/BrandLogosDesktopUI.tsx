import { clsx } from 'clsx'
import Image from 'next/image'

import {
  NUMBER_0,
  NUMBER_3,
  NUMBER_5,
  NUMBER_6
} from '@core/constants/numbers'
import { imageLoader } from '@core/utils/imageLoader'
import { Brand } from '@modules/shared/types/Storyblok'

interface BrandLogosDesktopUIProps {
  brands: Array<Brand>
  dynamicEditorStyle: string
  onBrandClick: (brandSlug: string) => void
}

export function BrandLogosDesktopUI({
  brands,
  dynamicEditorStyle,
  onBrandClick
}: BrandLogosDesktopUIProps): JSX.Element {
  const brandItemStyle = (): string => {
    if (brands.length % NUMBER_6 === NUMBER_0) {
      return 'basis-[calc(16.66%-16px)] w-[calc(16.66%-16px)]'
    }

    return 'basis-[calc(20%-16px)] w-[calc(20%-16px)]'
  }

  return (
    <div
      data-testid="brand-logos-desktop"
      className={clsx(
        dynamicEditorStyle,
        'w-full flex items-start flex-row flex-wrap content-center gap-4',
        (brands.length > 6 || brands.length % NUMBER_3 !== NUMBER_0) &&
        'justify-center'
      )}
    >
      {brands.map((brand, index) => (
        <button
          key={index}
          className={clsx(
            brandItemStyle(),
            'h-[120px] box-border p-3 lg:px-8 lg:py-6 rounded-lg bg-white-main',
            (brands.length % NUMBER_6 === NUMBER_0 ||
              brands.length % NUMBER_5 === NUMBER_0) &&
            'flex-grow'
          )}
          aria-label={`Redirects to brand ${brand.name}`}
          onClick={() => {
            onBrandClick(brand.value || brand.slug)
          }}
          onKeyDown={() => {
            onBrandClick(brand.value || brand.slug)
          }}
        >
          {brand.logo && (
            <Image
              loader={imageLoader}
              width={100}
              height={100}
              className="w-full h-full object-contain"
              src={brand.logo}
              alt={`Image about brand ${brand.name}`}
            />
          )}
        </button>
      ))}
    </div>
  )
}
