export const GA_TRACKING_ACTION = {
  AutoCorrectedSearch: 'Auto-Corrected Search',
  GlobalHeaderClick: 'Global Header Click',
  globalheaderclick: 'global header click',
  PromoClick: 'Promo Click',
  PromoView: 'Promo View',
  GridTopCategories: 'Category Image Click',
  ExternalLinks: 'Global Header Click',
  MPSellerRegistration: 'HP register as a seller interaction',
  GlobalFooter: 'Global Footer Click',
  FooterNavigationClick: 'Footer Navigation Click',
  NewsLetterSubscription: 'Newsletter subscription',
  NavigationBarClick: 'Navigation Bar Click',
  PriceFilterInteraction: 'Price Filter interaction',
  SearchInput: 'Using Search Bar',
  ContentFaq: 'Content FAQ',
  SearchSuggestion: 'Search Suggestion',
  ProductCardClick: 'Promotion Click',
  ProductCarouselClick: 'Carousel Click',
  ProductCarouselSwipe: 'HP Carousel Swipe',
  ProductCarouselButton: 'Carousel Button',
  MiniCartDeleteItem: 'Delete icon click',
  EmptyCartPromotionLink: 'Go to promotions clicked',
  MiniCartProductClick: 'Product name clicked',
  MiniCartIconHover: 'mini cart view',
  GoToCart: 'Cart link click',
  HomePageView: 'Homepage View',
  HomePageSrolling: 'Home Page Scrolling',
  PageView: 'Page View',
  PriceToggler: 'Price Toggler',
  CookieConsent: 'Cookie Consent',
  CookieBanner: 'Cookie Banner Shown',
  PromoImageLinkClick: 'Sales Navigation Banner',
  SeoDynamicWidgetClick: 'SEO Dynamic Widget Click',
  HeroBannerClick: 'Hero Banner Interaction',
  EditorialLinkList: 'Editorial Links Navigation',
  BuyAgainHeaderLinkClick: 'buy again header link clicked',
  ShoppingListHeaderLinkClick: 'shopping list header link clicked',
  YourAssortmentHeaderLinkClick: 'your assortment header link clicked',
  ApprovalsHeaderLinkClick: 'approvals header link clicked',
  PDPChangeQuantity: 'Change product quantity',
  PDPScroll: 'PDP Scrolling',
  PDPBrandInteraction: 'PDP brand interaction',
  PDPSellerInteraction: 'PDP seller interaction',
  PDPFurtherInformation: 'Further information about the product clicked',
  PDPDescriptionInformation: 'PDP description interaction',
  BreadcrumbNavigation: 'Breadcrumb Click',
  AddToCart: 'Add to Cart',
  ProductClick: 'Product Click',
  SimilarProductsView: 'Similar Products View',
  SimilarProductsClick: 'Similar Product Click',
  CategoryGridCardClick: 'Category Card Click',
  CallbackPhoneClick: 'Callback Phone Click',
  CallbackCalendlyClick: 'Callback Calendly Click',
  CallbackShown: 'telesales_callback_shown',
  Login: 'Login',
  EMP_LOGIN: 'emp - Login',
  EMP_ORDER_HISTORY: 'emp - Order History',
  IMPERSONATION_LOGIN: 'emp - Impersonation Login',
  ExploreSimilarProducts: 'explore_similar_products click',
  VariantsSelected: 'click_variant_selector',
  Checkout: 'Checkout',
  CheckoutExtended: 'Extended Checkout',
  BeginCheckout: 'begin_checkout',
  RemoveFromCart: 'remove_from_cart',
  RemoveFromCart2: 'Remove from Cart',
  ProceedToCheckout: 'Proceed To Checkout Clicked',
  CartUpdate: 'Cart Update',
  ClickOnRegionalProductUnavailableLink:
    'Click on regional product unavailable link',
  CheckoutNotifications: 'Checkout Notifications',
  CartNotifications: 'Cart Notifications',
  UndoButtonWasClicked: 'Undo button was clicked',
  ViewCart: 'view_cart',
  ProductCarouselTeaserClick: 'Product Carousel Teaser Click',
  PDPBlackWeekBannerClick: 'PDP Black week banner clicked',
  PDPBlackWeekBannerView: 'PDP Black week banner view',
  BuyAgainOnCartNotification: 'BA carousel response received',
  InstallationServices: 'Service Installation Checkbox Interaction',
  InstallationServicesLink: 'Service Installation Information Link Click',
  InstallationServicesDetails: 'Service Installation Details click',
  WeeeServicesDetails: 'Service WEEE Details click',
  CompareAllOffers: 'compare_all_offers_click',
  AddressBookLinkClick: 'Address book link click',
  CheckoutErrors: 'Checkout Errors',
  AddressNotCreated: 'Address not created',
  CheckoutPagination: 'Checkout Pagination Click',
  PopupOnCart: 'Popup on cart with out-of-stock offer',
  EmailCheckSubmitted: 'Email Check Submitted',
  CodeLoginTriggered: 'OTP Triggered',
  MagicLinkFlow: 'Magic Link Flow',
  CartTooltipVASInteraction: 'Cart tooltip VAS interaction',
  CartSidebarDeliveryOption: 'Cart delivery option sidebar clicked',
  CartSidebarWEEEInfo: 'Cart WEEE Info sidebar clicked',
  CartSidebarDeliveryOptionsLinkClicked:
    'cart delivery option sidebar link clicked',
  OTPCodeLoginResend: 'OTP Resent',
  OTPCodeLoginError: 'OTP Error',
  OTPCodeLoginSuccess: 'OTP Success',
  IdamRedirect: 'IDAM Redirect',
  RemoveButtonClicked: 'Remove button clicked',
  PLPScroll: 'PLP scrolling',
  SponsoringHomePageBannerHovered: 'hp_banner_hovered',
  SponsoringHomePageBannerClicked: 'hp_banner_clicked',
  SearchFilterRemoved: 'Filtered Search removed',
  FilteredSearch: 'Filtered Search',
  FilteredCategoryPage: 'Filtered Category Page',
  SearchBannerClick: 'search_banner_click',
  SearchFilterInteraction: 'Filter Area Interaction',
  PDPOfferMismatch: 'pdp_marketing_feed_offer_mismatch',
  AlternativeLogin: 'alternative login',
  PLPSortingDropdown: 'PLP sorting dropdown',
  PLPCategoryFilter: 'Category Filter on PLP',
  OrdersHistoryHeaderLinkClick: 'order_history_header_link_clicked',
  OrdersHistoryViewOrderDetails: 'view_order_history_details_clicked',
  OrdersHistoryViewMoreOrders: 'view_more_orders_clicked',
  OrdersHistoryTrackOrders: 'track_order_clicked',
  OrdersHistoryDownloadDocument: 'download_document_clicked',
  OrdersHistoryContactSupport: 'contact_customer_support_clicked',
  OrdersHistoryContactSeller: 'contact_seller_clicked',
  OrdersHistoryReturnItem: 'return_item_clicked',
  OrdersHistoryBuyAgain: 'buy_again_button_clicked',
  SearchZeroResult: 'Search Zero Result',
  ShoppingListLoadMore: 'SL show more products',
  ShoppingListPDPLinkClick: 'shopping list pdp link clicked',
  ShoppingListPDPLinkClickNoAuth: 'shopping list pdp button clicked',
  ShoppingListPDPToastLinkClick: 'shopping list toast on pdp link clicked',
  ShoppingListPDPRemoveClick: 'shopping list pdp remove button clicked',
  ShoppingListButtonClick: 'shopping list button on pdp clicked',
  ShoppingListManageButtonClick: 'manage shopping list on pdp clicked',
  ShoppingListRemove: 'remove item',
  ShoppingListCreateButtonClick: 'create new list on pdp clicked',
  ZRPCategoryViewed: 'zero results page|popular category is viewed',
  ShoppingListChangeQuantity: 'change_product_quantity',
  ShoppingListItemRemovedCross: 'cross clicked interaction',
  ShoppingListItemRemovedBin: 'bin clicked interaction',
  ShoppingListCreateNewClick: 'create_shopping_list_button_clicked',
  ShoppingListCreated: 'shopping_list_created',
  ShoppingListTabClicked: 'shopping_list_navigation_clicked',
  ShoppingListDeleteClicked: 'delete_shopping_list_button_clicked',
  ShoppingListDeleted: 'shopping_list_deleted',
  ShoppingListRenameClicked: 'change_shopping_list_name_button_clicked',
  ShoppingListRenamed: 'shopping_list_renamed',
  ShoppingListBuyAgainClicked: 'other_link_on_sl_navigation_clicked',
  Search: 'Search',
  OutOfScopeCategoryGridVisible: 'Out of scope Category Grid on ZRP is visible',
  FlixMediaContentView: 'Flix Media Content View',
  FlixMediaFailed: 'Flixmedia failed',
  PDPDeliveryOption: 'delivery_options_PDP_interaction',
  PopularLinksShowMore: 'popular_categories_show_more',
  PopularLinksShowLess: 'popular_categories_show_less',
  ContentCardGridShowMore: 'content_card_grid_show_more',
  PopularLinksClick: 'popular_links_clicked',
  PopularLinksView: 'popular_links_viewed',
  ContentCardGridClick: 'content_card_grid_clicked',
  ContentCardGridView: 'content_card_grid_viewed',
  ContinueShopping: 'continue_shopping',
  GoToCartPage: 'go_to_cart',
  HorizontalScroll: 'horizontal_scroll',
  RegionSelectorLocationChange: 'location_toggler',
  RecommendationProductNameClicked: 'Recommendation product name clicked',
  NewsletterViewed: 'newsletter_viewed',
  UspBenefitsViewed: 'benefits_viewed',
  AddPaymentInfo: ' Add Payment Info',
  SeeDetailsOtherOffers: 'see details offer clicked',
  ImageSearchHover: 'hover_image_search',
  ImageSearchLogoClicked: 'image_search_logo_clicked',
  SearchAddressFieldClicked: 'address_search_field_clicked',
  PlpCategoryNavigation: 'plp_category_navigation',
  PlpCategoryNavigationViewed: 'plp_category_navigation_viewed',
  BALinkSimilarProduct: 'link to similar products clicked',
  HeadlineBannerViewed: 'headline_banner_viewed',
  PasswordlessFlowSignupButtonClick: 'Passwordless Flow Signup Button Click',
  AccountCreated: 'Account Created',
  BAShowMoreProducts: 'Buy Again Show More Products',
  ClickPDPVideo: 'product_video_opened',
  ViewPDPVideo: 'product_video_viewed',
  BestSellingBrandsViewed: 'se_best_selling_brands_viewed',
  BestSellingBrandsClicked: 'se_best_selling_brands_clicked',
  RelatedCategoriesViewed: 'related_categories_viewed',
  RelatedCategoriesClicked: 'related_categories_clicked',
  CustomSignUpViewed: 'se_custom_sign_up_viewed',
  CustomSignUpSuscribed: 'se_custom_sign_up_suscribed',
  UndoButtonClicked: 'undo_button_was_clicked'
} as const

export type GaTrackingAction =
  (typeof GA_TRACKING_ACTION)[keyof typeof GA_TRACKING_ACTION]
