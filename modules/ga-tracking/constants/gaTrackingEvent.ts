export const GA_TRACKING_EVENT = {
  Logo: 'logo',
  AccessButtonClick: 'AccessButtonClick',
  TopBannerCategoryPromoClick: 'promoClick',
  TopBannerCategoryPromoView: 'promoView',
  PromoView: 'PromoView',
  Navigation: 'Navigation',
  Interaction: 'interaction',
  MPSellerRegistration: 'HP register as a seller interaction',
  NewsLetterSubscription: 'newsletter_subscription',
  SearchInput: 'search_search bar interaction',
  SearchSuggestion: 'search_suggestion',
  ProductClick: 'productClick',
  ProductCardPromoClick: 'PromoClick',
  ProductCarouselClick: 'carouselClick',
  ProductCarouselSwipe: 'carouselSwipe',
  ProductCarouselButton: 'carouselButton',
  MiniCart: 'mini_cart',
  GoToCart: 'goToCart',
  HPview: 'HPview',
  Scrolling: 'scrolling',
  PriceToggler: 'price_toggler',
  PriceFilter: 'PriceFilter',
  Consent: 'Consent',
  PageView: 'Page View',
  PromoImageLinkClick: 'click_salesNavigationBanner',
  SeoDynamicWidgetClick: 'seoDynamicWidgetClick',
  EditorialLinkList: 'click_editorialLinkNavigation',
  HeroBannerClick: 'clickHeroBanner',
  GA4ViewPromotion: 'view_promotion',
  GA4SelectPromotion: 'select_promotion',
  BuyAgainHeaderInteraction: 'header buy again link interaction',
  ShoppingListHeaderInteraction: 'header shopping list link interaction',
  YourAssortmentHeaderInteraction: 'header your assortment link interaction',
  ViewItem: 'view_item',
  PDPChangeQuantity: 'change_quantity',
  PDPBrandInteraction: 'PDP brand interaction',
  PDPSellerInteraction: 'PDP seller interaction',
  PDPFurtherInformation: 'further_information_click',
  PDPDescriptionInteraction: 'PDP description interaction',
  AddToCartGA4: 'add_to_cart',
  AddToWishlist: 'add_to_wishlist',
  ViewRecommendation: 'view_recommendation', //ga4
  SelectRecommendation: 'select_recommendation', //ga4
  CategoryGridCardClick: 'clickCategoryCard', //ga360
  CallbackPhoneClick: 'Callback Phone Click',
  CallbackCalendlyClick: 'Callback Calendly Click',
  CallbackShown: 'se_telesales_callback_shown',
  Authorisation: 'Authorisation',
  ASSISTED_SALES: 'Assisted Sales',
  exploreProducts: 'explore_similar_products click',
  Checkout: 'checkout',
  CheckoutStepper: 'checkout_stepper',
  BeginCheckout: 'begin_checkout',
  RemoveFromCart: 'remove_from_cart',
  RemoveFromCart2: 'removeFromCart',
  ProceedToCheckout: 'proceedToCheckout',
  CartUpdate: 'cart_update',
  Notifications: 'Notifications',
  ViewCart: 'view_cart',
  DiscountCode: 'discountCode',
  ProductCarouselTeaserClick: 'clickProductCarouselTeaser',
  PromoClick: 'promoClick',
  PromoViewCamelCase: 'promoView',
  InstallationServices: 'Service Installation Checkbox',
  InstallationServiceLink: 'Service Installation Link Click',
  InstallationServicesDetails: 'Service Installation Details',
  WeeeServicesDetails: 'Service WEEE Details',
  CompareAllOffers: 'compare_all_offers_click',
  AddressBookLinkClick: 'Address book link click',
  Errors: 'Errors',
  AddShippingInfo: 'add_shipping_info',
  Purchase: 'purchase',
  PurchaseTest: 'purchase_test',
  NoShippingAddress: 'no shipping',
  ViewItemList: 'view_item_list',
  SelectItem: 'select_item',
  SponsoringHomePageBannerHover: 'se_hp_banner_hovered',
  SponsoringHomePageBannerClick: 'se_hp_banner_clicked',
  SearchRemoveFilters: 'remove_filters',
  SearchFiltered: 'search_filtered',
  CategoryFiltered: 'category_page_filtered',
  ContentFaq: 'contentFAQ',
  SESearchFiltered: 'se_search_filtered',
  SECategoryFiltered: 'se_category_page_filtered',
  SearchBannerClick: 'search_banner_click',
  SearchFilterArea: 'FilterArea',
  PDPOfferMismatch: 'se_pdp_marketing_feed_offer_mismatch',
  GenesysEnabled: 'genesys_enabled',
  SortDropdown: 'sorting_dropdown',
  PLPCategoryFilter: 'category_filter_on_PLP',
  SearchZeroResults: 'search_0_results',
  ShoppingListLoadMore: 'se_sl_show_more_products',
  ShoppingListPDPRemove: 'se_sl_pdp_remove_button_clicked',
  ShoppingListPDPLinkClick: 'se_sl_pdp_link_clicked',
  CategoryViewed: 'category_viewed',
  OrdersHistoryHeaderLinkClick: 'se_order_history_header_link_clicked',
  OrdersHistoryViewOrderDetails: 'se_view_order_history_details_clicked',
  OrdersHistoryViewMoreOrders: 'se_view_more_orders_clicked',
  OrdersHistoryTrackOrders: 'se_track_order_clicked',
  OrdersHistoryDownloadDocument: 'se_download_document_clicked',
  OrdersHistoryContactSupport: 'se_contact_customer_support_clicked',
  OrdersHistoryContactSeller: 'se_contact_seller_clicked',
  OrdersHistoryReturnItem: 'se_return_item_clicked',
  OrdersHistoryBuyAgain: 'se_buy_again_button_clicked',
  ShoppingListChangeQuantity: 'se_change_product_quantity',
  ShoppingListItemRemoved: 'se_remove_from_shopping_list',
  ShoppingListCreateNewClick: 'se_create_shopping_list_button_clicked',
  ShoppingListCreated: 'se_shopping_list_created',
  ShoppingListTabClicked: 'se_shopping_list_navigation_clicked',
  ShoppingListDeleteClicked: 'se_delete_shopping_list_button_clicked',
  ShoppingListDeleted: 'se_shopping_list_deleted',
  ShoppingListRenameClicked: 'se_rename_shopping_list_button_clicked',
  ShoppingListRenamed: 'se_shopping_list_renamed',
  ShoppingListBuyAgainClicked: 'se_shopping_list_navigation_clicked',
  ShoppingListHeaderLinkClick: 'se_sl_header_link_clicked', // user shopping list
  ShoppingListUndoButtonClick: 'se_undo_shopping_list_removal_clicked',
  ApprovalsHeaderLinkClick: 'se_approvals_header_link_clicked',
  SearchCorrected: 'search_corrected',
  Search: 'search',
  FlixMediaView: 'FlixmediaView',
  FlixMediaError: 'flixmedia_error',
  PDPDeliveryOptionInteraction: 'se_delivery_options_PDP_interaction',
  PopularLinksShowMore: 'se_popular_categories_show_more',
  PopularLinksShowLess: 'se_popular_categories_show_less',
  ContentCardGridShowMore: 'se_content_card_grid_show_more',
  PopularLinksClick: 'se_popular_links_clicked',
  PopularLinksView: 'se_popular_links_viewed',
  ContentCardGridClick: 'se_content_card_grid_clicked',
  ContentCardGridView: 'se_content_card_grid_viewed',
  ContinueShopping: 'se_continue_shopping',
  GoToCartPage: 'se_go_to_cart',
  ShoppingListPDPLinkClickNoAuth: 'se_sl_pdp_button_interaction',
  RegionSelectorChange: 'se_location_toggler',
  HorizontalScroll: 'se_horizontal_scroll',
  NewsletterViewed: 'se_newsletter_viewed',
  ShoppingListButtonClick: 'se_sl_button_on_pdp_clicked',
  ShoppingListManageButtonClick: 'se_manage_shopping_list_on_pdp_clicked',
  ShoppingListUpdateButtonClick: 'se_sl_pdp_update_button_clicked',
  ShoppingListCreateButtonClick: 'se_create_new_list_clicked',
  BenefitsViewed: 'se_benefits_viewed',
  AddPaymentInfo: 'add_payment_info',
  SeeDetailsOtherOffers: 'se_see_details_offer_clicked',
  ImageSearchLogoHover: 'se_hover_image_search',
  ImageSearchLogoClicked: 'se_image_search_logo_clicked',
  SearchAddressFieldClicked: 'se_address_search_field_clicked',
  PlpCategoryNavigation: 'se_plp_category_navigation',
  PlpCategoryNavigationViewed: 'se_plp_category_navigation_viewed',
  HeadlineBannerViewed: 'se_headline_banner_viewed',
  InteractionBA: 'interaction_buy_again',
  ClickPDPVideo: 'se_product_video_opened',
  ViewPDPVideo: 'se_product_video_viewed',
  BestSellingBrandsViewed: 'se_best_selling_brands_viewed',
  BestSellingBrandsClicked: 'se_best_selling_brands_clicked',
  RelatedCategoriesViewed: 'se_related_categories_viewed',
  RelatedCategoriesClicked: 'se_related_categories_clicked',
  CustomSignUpViewed: 'se_custom_sign_up_viewed',
  CustomSignUpSuscribed: 'se_custom_sign_up_suscribed'
} as const

export type GaTrackingEvent =
  (typeof GA_TRACKING_EVENT)[keyof typeof GA_TRACKING_EVENT]
