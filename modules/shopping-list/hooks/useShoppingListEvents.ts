import { useCallback } from 'react'

import { NUMBER_0, NUMBER_1 } from '@core/constants/numbers'
import { useGtag } from '@core/hooks'
import { usePriceType } from '@core/hooks/usePriceType'
import { Currency, PriceType } from '@core/types'

import {
  GA_TRACKING_ACTION,
  GA_TRACKING_CATEGORY,
  GA_TRACKING_EVENT,
  GaTrackingEvent,
  GaTrackingEventLabel
} from '@modules/ga-tracking/constants'
import { ProductIndexOffer } from '@modules/product/types'
import { ChangeQuantityTarget } from '@modules/product/types/quantity-change-target.enum'
import {
  getEecClassAttr,
  getItemShoppingListObject
} from '@modules/product/utils'

import { ShoppingListNotAvailableReasons } from '../constants'
import { ShoppingListItem, ShoppingListRemoveItemSourceType } from '../types'
import {
  getOfferShoppingList,
  isOfferAvailable,
  isOfferAvailableOtherThan,
  isOtherOrganizationOfferAvailable
} from '../utils/shopping-list-offer-utils'

export const useShoppingListEvents = () => {
  const { gtagEvent } = useGtag()
  const { price } = usePriceType()

  const shoppingListViewItemsListingEvent = useCallback(
    (products, currentOffset) => {
      if (products?.length > 0) {
        const currency = products[0]?.bestOffer?.price?.currency || Currency.EUR
        // GA4 event
        gtagEvent({
          event: GA_TRACKING_EVENT.ViewItemList,
          price_type: price,
          ecommerce: {
            currency,
            item_list_name: GaTrackingEventLabel.ShoppingListName,
            items: getItemsOfViewItemsList(products, price, currentOffset)
          }
        })
      }
    },
    [gtagEvent, price]
  )

  const shoppingListEcommerceSelectAndAddToCartEvents = (
    product: ShoppingListItem,
    bestOffer: ProductIndexOffer,
    priceType: PriceType,
    index: number,
    quantity,
    eventType: GaTrackingEvent
  ) => {
    const eecClassAttribute = getEecClassAttr(product?.item?.attributes)
    const currency =
      bestOffer?.destinationRegionInfo?.price?.currency || Currency.EUR

    // GA4 event
    gtagEvent({
      event: eventType,
      price_type: priceType,
      ecommerce: {
        currency,
        items: [
          {
            ...getItemShoppingListObject(
              product,
              bestOffer,
              priceType,
              eecClassAttribute,
              quantity,
              index + NUMBER_1,
              null
            ),
            item_list_name: GaTrackingEventLabel.ShoppingListName
          }
        ]
      }
    })
  }

  const getItemsOfViewItemsList = (
    products: ShoppingListItem[],
    priceType,
    currentOffset
  ) => {
    const items = []
    products?.forEach((product, index) => {
      let bestOffer = getOfferShoppingList(product)
      let notAvailableReason: number | null = null

      if (!product.pdpAvailable) {
        notAvailableReason = ShoppingListNotAvailableReasons.PRODUCT_NOT_IN_SPI
      }

      const sameOffer = isOfferAvailable(
        product.selectedOffer.id,
        product.offers
      )

      const isSameSellerDifferentOfferAvailable = isOfferAvailableOtherThan(
        product.selectedOffer.id,
        product.selectedOffer.organizationId,
        product.offers
      )

      const isDifferentSellerOfferAvailable = isOtherOrganizationOfferAvailable(
        product.selectedOffer.organizationId,
        product.offers
      )

      if (
        !sameOffer &&
        !isSameSellerDifferentOfferAvailable &&
        isDifferentSellerOfferAvailable
      ) {
        notAvailableReason =
          ShoppingListNotAvailableReasons.ALTERNATE_SELLER_OFFER
      }

      if (product.pdpAvailable && !product.offers.length) {
        notAvailableReason =
          ShoppingListNotAvailableReasons.PRODUCT_IN_SPI_WITHOUT_OFFER
      }

      const eecClassAttribute = getEecClassAttr(product?.item?.attributes)
      items.push({
        ...getItemShoppingListObject(
          product,
          bestOffer,
          priceType,
          eecClassAttribute,
          bestOffer ? NUMBER_1 : NUMBER_0,
          index + NUMBER_1 + currentOffset,
          notAvailableReason
        )
      })
    })
    return items
  }

  const quantityChangeEvent = (target: string) => {
    const label =
      target === ChangeQuantityTarget.Button
        ? GaTrackingEventLabel.ShoppingListQuantityPickerInteractionButton
        : GaTrackingEventLabel.ShoppingListQuantityPickerInteractionInline

    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListChangeQuantity,
      action: GA_TRACKING_ACTION.ShoppingListChangeQuantity,
      category: GA_TRACKING_CATEGORY.shoppingList,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: label
    })
  }

  const onUndoRemovalClickEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListUndoButtonClick,
      action: GA_TRACKING_ACTION.UndoButtonClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction
    })
  }

  const loadMoreEvent = () => {
    gtagEvent({
      action: GA_TRACKING_ACTION.ShoppingListLoadMore,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListLoadMore,
      category: GA_TRACKING_CATEGORY.shoppingList
    })
  }

  const removeItemEvent = (
    itemId: string,
    source: ShoppingListRemoveItemSourceType
  ) => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListItemRemoved,
      action: GA_TRACKING_ACTION[source],
      category: GA_TRACKING_CATEGORY.shoppingList,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: itemId
    })
  }

  const createNewListEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListCreateNewClick,
      action: GA_TRACKING_ACTION.ShoppingListCreateNewClick,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const listCreatedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListCreated,
      action: GA_TRACKING_ACTION.ShoppingListCreated,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const tabClickedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListTabClicked,
      action: GA_TRACKING_ACTION.ShoppingListTabClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const deleteShoppingListEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListDeleteClicked,
      action: GA_TRACKING_ACTION.ShoppingListDeleteClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const shoppingListDeletedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListDeleted,
      action: GA_TRACKING_ACTION.ShoppingListDeleted,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const renameButtonClickedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListRenameClicked,
      action: GA_TRACKING_ACTION.ShoppingListRenameClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const renamedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListRenamed,
      action: GA_TRACKING_ACTION.ShoppingListRenamed,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: null
    })
  }

  const buyAgainClickedEvent = () => {
    gtagEvent({
      event: GA_TRACKING_EVENT.ShoppingListBuyAgainClicked,
      action: GA_TRACKING_ACTION.ShoppingListBuyAgainClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      label: 'buy again'
    })
  }

  return {
    shoppingListViewItemsListingEvent,
    shoppingListEcommerceSelectAndAddToCartEvents,
    quantityChangeEvent,
    loadMoreEvent,
    removeItemEvent,
    createNewListEvent,
    listCreatedEvent,
    tabClickedEvent,
    deleteShoppingListEvent,
    shoppingListDeletedEvent,
    renameButtonClickedEvent,
    renamedEvent,
    buyAgainClickedEvent,
    onUndoRemovalClickEvent
  }
}
