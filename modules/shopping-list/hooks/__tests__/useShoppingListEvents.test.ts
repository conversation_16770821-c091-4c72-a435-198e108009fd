import { renderHook } from '@testing-library/react-hooks'

import {
  GA_TRACKING_ACTION,
  GA_TRACKING_CATEGORY,
  GA_TRACKING_EVENT
} from '@modules/ga-tracking/constants'
import { ShoppingListRemoveItemSource } from '@modules/shopping-list/types'

import { useShoppingListEvents } from '../useShoppingListEvents'

const gtagEventMock = jest.fn()

jest.mock('@core/hooks/useGtag', () => ({
  useGtag: () => ({
    gtagEvent: gtagEventMock
  })
}))

jest.mock('@core/hooks/usePriceType', () => ({
  usePriceType: jest.fn().mockImplementation(() => ({ price: 123 }))
}))

describe('useShoppingListEvents', () => {
  beforeEach(() => {
    gtagEventMock.mockReset()
  })

  it('should call the gtagEvent when item is removed from shopping list by clicking bin icon ', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.removeItemEvent('123', ShoppingListRemoveItemSource.BIN)
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: 'interaction',
      action: GA_TRACKING_ACTION[ShoppingListRemoveItemSource.BIN],
      category: 'shopping list',
      event: 'se_remove_from_shopping_list',
      label: '123'
    })
  })

  it('should call the gtagEvent when item is removed from shopping list by clicking cross icon ', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.removeItemEvent('123', ShoppingListRemoveItemSource.CROSS)
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: 'interaction',
      action: GA_TRACKING_ACTION[ShoppingListRemoveItemSource.CROSS],
      category: 'shopping list',
      event: 'se_remove_from_shopping_list',
      label: '123'
    })
  })

  it('should call the gtagEvent with correct params when createNewListEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.createNewListEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListCreateNewClick,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListCreateNewClick,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when listCreatedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.listCreatedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListCreated,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListCreated,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when tabClickedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.tabClickedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListTabClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListTabClicked,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when deleteShoppingListEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.deleteShoppingListEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListDeleteClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListDeleteClicked,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when shoppingListDeletedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.shoppingListDeletedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListDeleted,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListDeleted,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when renameButtonClickedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.renameButtonClickedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListRenameClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListRenameClicked,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when renamedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.renamedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListRenamed,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListRenamed,
      label: null
    })
  })

  it('should call the gtagEvent with correct params when buyAgainClickedEvent is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.buyAgainClickedEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      action: GA_TRACKING_ACTION.ShoppingListBuyAgainClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListBuyAgainClicked,
      label: 'buy again'
    })
  })

  it('should call the gtagEvent with correct params when undo removal button is called', () => {
    const { result } = renderHook(() => useShoppingListEvents())
    result.current.onUndoRemovalClickEvent()
    expect(gtagEventMock).toHaveBeenCalled()
    expect(gtagEventMock).toHaveBeenCalledWith({
      event: GA_TRACKING_EVENT.ShoppingListUndoButtonClick,
      action: GA_TRACKING_ACTION.UndoButtonClicked,
      category: GA_TRACKING_CATEGORY.interaction,
      GA_eventname: GA_TRACKING_CATEGORY.interaction
    })
  })
})
