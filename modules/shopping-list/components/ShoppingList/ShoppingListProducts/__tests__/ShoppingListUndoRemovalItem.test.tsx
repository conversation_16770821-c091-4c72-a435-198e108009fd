import { fireEvent, screen, waitFor } from '@testing-library/react'
import axios from 'axios'
import Mock<PERSON>dapter from 'axios-mock-adapter'
import React from 'react'

import { renderWithProviders } from '@core/utils/testing'

import { SHORT_TIMEOUT } from '@modules/shared/constants/constants'

import { ShoppingListUndoRemovalItem } from '../ShoppingListUndoRemovalItem/ShoppingListUndoRemovalItem'

const undoRemovalFunctionMock = jest.fn()
describe('ShoppingListUndoRemovalItem', () => {
  let mock: MockAdapter

  beforeEach(() => {
    mock = new MockAdapter(axios)
  })

  afterEach(() => {
    undoRemovalFunctionMock.mockClear()
    mock.reset()
  })
  it('should render ShoppingListUndoRemovalItem component with data', async () => {
    renderWithProviders(
      <ShoppingListUndoRemovalItem
        productName="Testing Product"
        productViewUrl="testing-url"
        undoRemovalItem={undoRemovalFunctionMock}
      />
    )
    expect(screen.getByTestId('item-removed-pending')).toBeInTheDocument()
    expect(screen.getByTestId('item-product-name')).toBeInTheDocument()
    expect(
      screen.getByTestId('item-product-removed-message')
    ).toBeInTheDocument()
    await waitFor(
      () => {
        expect(screen.getByTestId('item-removed-success')).toBeInTheDocument()
      },
      { timeout: SHORT_TIMEOUT }
    )
  })

  it('should redo the ShoppingListUndoRemovalItem when clicking on the undo button', async () => {
    renderWithProviders(
      <ShoppingListUndoRemovalItem
        productName="Testing Product"
        productViewUrl="testing-url"
        undoRemovalItem={undoRemovalFunctionMock}
      />
    )
    expect(screen.getByTestId('item-removed-pending')).toBeInTheDocument()

    expect(screen.getByTestId('item-product-name')).toBeInTheDocument()
    expect(
      screen.getByTestId('item-product-removed-message')
    ).toBeInTheDocument()
    fireEvent.click(screen.getByTestId('item-removed-pending'))
    await waitFor(
      () => {
        expect(
          screen.queryByTestId('item-removed-success')
        ).not.toBeInTheDocument()
      },
      { timeout: SHORT_TIMEOUT }
    )
  })
})
