import { fireEvent, screen, waitFor } from '@testing-library/react'
import React from 'react'

import '@core/hooks/__mocks__/intersectionObserver'
import { PopUPModalProvider } from '@core/hooks/usePopUpModal'
import { renderWithProviders } from '@core/utils/testing'

import {
  GA_TRACKING_ACTION,
  GA_TRACKING_CATEGORY,
  GA_TRACKING_EVENT,
  GaTrackingEventLabel
} from '@modules/ga-tracking/constants'
import { shoppingListResponse } from '@modules/shopping-list/mocks/shopping-list-response-mock'

import { ShoppingListProducts } from '../ShoppingListProducts'

const gtagEventMock = jest.fn()
const onRemoveItemMock = jest.fn()

jest.mock('@core/hooks/useGtag', () => ({
  useGtag: () => ({
    gtagEvent: gtagEventMock
  })
}))
const addOfferMock = jest
  .fn()
  .mockImplementation(() =>
    Promise.resolve({ meta: { requestStatus: 'fulfilled' } })
  )
jest.mock('@core/redux/features/cart', () => ({
  useCart: () => ({
    addOffer: addOfferMock,
    fetchQuantity: jest.fn()
  })
}))

jest.mock('@core/alerts', () => ({
  addedToCartAlert: jest.fn()
}))
describe('ShoppingListProducts', () => {
  const trimEur = (x: string) => x.replace('€', '').trim()
  const mockProduct = shoppingListResponse.items[0]

  beforeEach(() => {
    onRemoveItemMock.mockClear()
  })

  it('should render shopping List Product with data', async () => {
    const { container } = renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={shoppingListResponse.items[0]}
          index={0}
          selectedShoppingListName={null}
          onRemoveItem={jest.fn()}
        />
      </PopUPModalProvider>
    )
    expect(container).toMatchSnapshot()
  })

  it('should call the add to cart function on add to cart button and check the success label', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={mockProduct}
          onRemoveItem={jest.fn()}
          index={0}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )
    const addToCartButton = screen.getByTestId('add_to_cart_button')
    fireEvent.click(addToCartButton)
    await waitFor(() => {
      expect(screen.getByTestId('add_to_cart_success')).toBeInTheDocument()
    })
    expect(addOfferMock).toHaveBeenCalledTimes(1)
  })

  it('should call the add to cart event on add to cart button', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={mockProduct}
          onRemoveItem={jest.fn()}
          index={0}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )
    const addToCartButton = screen.getByTestId('add_to_cart_button')
    fireEvent.click(addToCartButton)
    await waitFor(() => {
      expect(screen.getByTestId('add_to_cart_success')).toBeInTheDocument()
    })
    expect(gtagEventMock).toHaveBeenCalledWith({
      ecommerce: {
        currency: 'EUR',
        items: [
          {
            category_tree: [
              {
                id: '6b9a85d7-c287-4d35-9186-cc7ceb8691b4',
                name: 'Gastro Kühlschränke'
              },
              {
                id: 'e45413de-3229-40b6-be52-2f77f97bcdc2',
                name: 'Kühltechnik'
              },
              {
                id: '7394ea0a-c7b7-4a1a-86bf-2585772dbba0',
                name: 'Küchenausstattung'
              }
            ],
            index: 1,
            item_black_deal_label: 'no',
            item_brand: 'METRO Professional',
            item_category: 'Küchenausstattung',
            item_category2: 'Kühltechnik',
            item_category3: 'Gastro Kühlschränke',
            item_discount_rate: 0,
            item_energy_label: 'B',
            item_id: 'd92e1353-dcb1-4899-99b6-ddf8e609b0ed',
            item_name:
              'METRO Professional Kühlschrank GRE1400, Edelstahl, 131.4 x 80.5 x 206.5 cm, 940 L, Luftkühlung, 350 W, GN 1/1, mit Schloß, silber',
            item_offer_id: '8997d893-6f4b-4e04-8c1f-6ee7fe078f9e',
            item_offer_type: 'cheapest',
            item_other_offers: 'no',
            item_promo: 'no',
            item_seller: 'SMS test',
            item_shipping_cost: 0,
            item_volume_pricing: true,
            item_custom: 'sl_nar=null',
            price: '2199.78',
            item_strikethrough: 'no',
            quantity: 1,
            item_list_name: 'Shopping List'
          }
        ]
      },
      event: 'add_to_cart',
      price_type: 'entrepreneur'
    })
  })

  it('should adjust the pricing according to volume price', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={mockProduct}
          index={0}
          onRemoveItem={jest.fn()}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )
    const quantityPickerInput = screen.getAllByTestId('quantity')[0]
    fireEvent.change(quantityPickerInput, { target: { value: '6' } })
    await waitFor(() => {
      expect(trimEur(screen.getByTestId('product_price').textContent)).toEqual(
        'CART.OFFER.A11Y.PRICE:2.100,50'
      )
    })
    expect(gtagEventMock).toHaveBeenCalledWith({
      action: GA_TRACKING_ACTION.ShoppingListChangeQuantity,
      GA_eventname: GA_TRACKING_CATEGORY.interaction,
      event: GA_TRACKING_EVENT.ShoppingListChangeQuantity,
      category: GA_TRACKING_CATEGORY.shoppingList,
      label: GaTrackingEventLabel.ShoppingListQuantityPickerInteractionInline
    })
  })

  it('should call onRemoveItem when delete button is clicked', async () => {
    const onRemoveItem = jest.fn()
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          index={0}
          product={mockProduct}
          onRemoveItem={onRemoveItem}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )
    const removeButton = screen.getAllByTestId('decrement')[0]

    fireEvent.click(removeButton)
    expect(onRemoveItem).toHaveBeenCalledTimes(1)
  })

  it('should call the select item event on product link click', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={mockProduct}
          onRemoveItem={jest.fn()}
          index={0}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )
    const productLink = screen.getByTestId('product_name')
    fireEvent.click(productLink)
    expect(gtagEventMock).toHaveBeenCalledWith({
      ecommerce: {
        currency: 'EUR',
        items: [
          {
            category_tree: [
              {
                id: '6b9a85d7-c287-4d35-9186-cc7ceb8691b4',
                name: 'Gastro Kühlschränke'
              },
              {
                id: 'e45413de-3229-40b6-be52-2f77f97bcdc2',
                name: 'Kühltechnik'
              },
              {
                id: '7394ea0a-c7b7-4a1a-86bf-2585772dbba0',
                name: 'Küchenausstattung'
              }
            ],
            index: 1,
            item_black_deal_label: 'no',
            item_brand: 'METRO Professional',
            item_category: 'Küchenausstattung',
            item_category2: 'Kühltechnik',
            item_category3: 'Gastro Kühlschränke',
            item_discount_rate: 0,
            item_energy_label: 'B',
            item_id: 'd92e1353-dcb1-4899-99b6-ddf8e609b0ed',
            item_name:
              'METRO Professional Kühlschrank GRE1400, Edelstahl, 131.4 x 80.5 x 206.5 cm, 940 L, Luftkühlung, 350 W, GN 1/1, mit Schloß, silber',
            item_offer_id: '8997d893-6f4b-4e04-8c1f-6ee7fe078f9e',
            item_offer_type: 'cheapest',
            item_other_offers: 'no',
            item_promo: 'no',
            item_seller: 'SMS test',
            item_shipping_cost: 0,
            item_volume_pricing: true,
            item_custom: 'sl_nar=null',
            price: '2199.78',
            item_strikethrough: 'no',
            quantity: 1,
            item_list_name: 'Shopping List'
          }
        ]
      },
      event: 'select_item',
      price_type: 'entrepreneur'
    })
  })

  it('Should set href as empty string for image link when pdpAvailable is false', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={shoppingListResponse.items[5]}
          index={0}
          onRemoveItem={jest.fn()}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )

    const href = screen.getByTestId('product_image_link').getAttribute('href')
    expect(href).toBe('')
  })

  it('Should set href as empty string for title link when pdpAvailable is false', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={shoppingListResponse.items[5]}
          index={0}
          onRemoveItem={jest.fn()}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>
    )

    const href = screen.getByTestId('product_name').getAttribute('href')
    expect(href).toBe('')
  })

  it('Should have a placeholder image when image not available', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={{
            ...mockProduct,
            item: { ...mockProduct.item, images: [] }
          }}
          index={0}
          selectedShoppingListName={null}
          onRemoveItem={jest.fn()}
        />
      </PopUPModalProvider>
    )

    const imageSrc = screen.getByAltText('offer_line_image').getAttribute('src')
    expect(imageSrc).toBe(
      'https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/product-catalog-placeholder.png'
    )
  })

  it('Should not show the undo removal functionality if the feature flag is false', () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={shoppingListResponse.items[5]}
          index={0}
          onRemoveItem={onRemoveItemMock}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>,
      {
        preloadedState: {
          featureFlags: {
            values: {
              TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST: false
            }
          }
        }
      }
    )

    fireEvent.click(screen.getByTestId('remove_button'))
    expect(
      screen.queryByTestId('undo-removal-shopping-list-item')
    ).not.toBeInTheDocument()
    expect(onRemoveItemMock).toHaveBeenCalledTimes(1)
  })

  it('Should show the undo removal functionality if the feature flag is true', () => {
    renderWithProviders(
      <PopUPModalProvider>
        <ShoppingListProducts
          product={shoppingListResponse.items[5]}
          index={0}
          onRemoveItem={onRemoveItemMock}
          selectedShoppingListName={null}
        />
      </PopUPModalProvider>,
      {
        preloadedState: {
          featureFlags: {
            values: {
              TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST: true
            }
          }
        }
      }
    )

    fireEvent.click(screen.getByTestId('remove_button'))
    expect(
      screen.getByTestId('undo-removal-shopping-list-item')
    ).toBeInTheDocument()
    fireEvent.click(screen.getByTestId('item-removed-pending'))
    expect(
      screen.queryByTestId('undo-removal-shopping-list-item')
    ).not.toBeInTheDocument()
    expect(onRemoveItemMock).not.toHaveBeenCalledTimes(1)
  })
})
