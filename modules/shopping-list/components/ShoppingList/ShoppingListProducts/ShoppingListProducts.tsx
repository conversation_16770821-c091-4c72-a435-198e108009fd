import Image from 'next/image'
import Link from 'next/link'
import { useRef, useState } from 'react'

import { useSLTranslation } from '@core/hooks/useTranslation'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { DataSheetLink } from '@modules/product/components/data-sheet-link'
import { ProductAttributeLabelsV2 } from '@modules/product/components/product-attributes-labels/V2'
import { ATTRIBUTE_CODE_TYPES } from '@modules/product/constants'
import { Item } from '@modules/product/types/spi'
import { getEecDataSheets, isBlackDealProduct } from '@modules/product/utils'
import { Button, PopUpModal, SVGIcon } from '@modules/shared/components'
import { SHORT_TIMEOUT } from '@modules/shared/constants/constants'
import { SVG_NAMES } from '@modules/shared/icons/constants'
import { mapAttributesToObject } from '@modules/shared/utils/EnergyEfficiencyLabel.utils'
import {
  useItemsPricing,
  useProductViewUrl,
  useShoppingListEvents,
  useShoppingListOffer
} from '@modules/shopping-list/hooks'
import {
  ShoppingListItem,
  ShoppingListRemoveItemFunc,
  ShoppingListRemoveItemSource
} from '@modules/shopping-list/types'
import {
  getOptimizedProductImage,
  hasPromo,
  productHasRebuyGuarantee
} from '@modules/shopping-list/utils/product-utils'

import { AddToCartButton } from '../../add-to-cart-cart/addToCartButton'
import { UnavailableOfferButton } from '../../unavailable-offer-button/UnavailableOfferButton'
import { ShoppingListProductInfo } from './ShoppingListProductInfo/ShoppingListProductInfo'
import { ShoppingListProductOfferFlow } from './ShoppingListProductOfferFlow/ShoppingListProductOfferFlow'
import { ShoppingListProductPriceATC } from './ShoppingListProductPriceATC/ShoppingListProductPriceATC'
import { ShoppingListUndoRemovalItem } from './ShoppingListUndoRemovalItem/ShoppingListUndoRemovalItem'

export type ShoppingListProductsProps = {
  product: ShoppingListItem
  onRemoveItem: ShoppingListRemoveItemFunc
  index: number
  selectedShoppingListName: string
}

export const ShoppingListProducts = ({
  product,
  onRemoveItem,
  index,
  selectedShoppingListName
}: ShoppingListProductsProps): JSX.Element => {
  const { t } = useSLTranslation()
  const {
    offer,
    addedToCart,
    vatRegionInfo,
    activeVolumeRange,
    nextVolumeRange,
    onAddToCart,
    handleQuantityChange,
    quantity,
    onProductClick,
    minQuantity,
    isMinimumPurchaseQuantityActve
  } = useItemsPricing(product, index)
  const { onUndoRemovalClickEvent } = useShoppingListEvents()
  const { productViewUrl } = useProductViewUrl(product.item.idItem, offer?.id)
  const { isSameSellerOfferAvailable, isDifferentSellerOfferAvailable } =
    useShoppingListOffer(product)
  const timer = useRef(null)
  const [offerWasRemoved, setOfferWasRemoved] = useState(false)
  const isBlackDealsEnabled = useFeatureFlag(
    FeatureFlag.FF_BLACK_FRIDAY_COUNTDOWN
  )

  const isUndoRemovalFunctionalityEnabled = useFeatureFlag(
    FeatureFlag.TEMP_RPS_OBE_1137_UNDO_REMOVAL_SHOPPING_LIST
  )
  const belongsToBlackDeals = isBlackDealProduct(product?.tags)

  const dataSheet = getEecDataSheets(product?.item?.attributes)
  const doesSheetExist =
    !!dataSheet.length &&
    dataSheet.find(
      (sheet) => sheet.code === ATTRIBUTE_CODE_TYPES.PRODUCT_DATA_SHEET
    )
  const initiateRemovalCountdown = (id, source) => {
    if (isUndoRemovalFunctionalityEnabled) {
      setOfferWasRemoved(true)

      timer.current = setTimeout(async () => {
        onRemoveItem(id, source)
      }, SHORT_TIMEOUT)
    } else {
      onRemoveItem(id, source)
    }
  }

  const handleUndoOfferRemoval = () => {
    clearTimeout(timer.current)
    setOfferWasRemoved(false)
    onUndoRemovalClickEvent()
  }

  return (
    <>
      {!offerWasRemoved && (
        <li className="flex flex-col p-4 bg-white-main mb-[1px] w-full pt-12 md:p-4 gap-y-1">
          <div className="flex flex-row relative">
            <div className="relative h-fit">
              <Link
                href={product.pdpAvailable ? productViewUrl : ''}
                onClick={onProductClick}
                data-testid="product_image_link"
                className={`${product.pdpAvailable ? 'cursor-pointer' : 'cursor-default'}`}
                tabIndex={-1}
                aria-label={product.item?.name}
              >
                <Image
                  loader={({ src }) => src}
                  className="object-contain h-[100px] w-[100px] min-w-[100px] mr-6"
                  src={getOptimizedProductImage(product?.item?.images[0]?.url)}
                  alt="offer_line_image"
                  width={100}
                  height={100}
                  unoptimized={true}
                />
              </Link>
              <PopUpModal />
            </div>

            <div className="flex flex-col lg:flex-row w-full md:gap-2 lg:gap-6">
              <ShoppingListProductInfo
                productIndexItem={product.item}
                sellerInfo={offer?.organization}
                discountedPercentage={
                  activeVolumeRange?.savePrice?.percentage ||
                  vatRegionInfo?.referencePrice?.discountPercentage
                }
                hasPromotion={hasPromo(offer)?.active}
                productViewUrl={productViewUrl}
                hasRebuyGuarantee={productHasRebuyGuarantee(product)}
                onProductClick={onProductClick}
                isPdpAvailable={product.pdpAvailable}
                nextVolumeRange={nextVolumeRange}
                isBlackDealsEnabled={isBlackDealsEnabled && belongsToBlackDeals}
                offerId={offer?.id}
              />
              <div className="absolute bottom-0 left-0 [&_img]:!mt-1">
                <ProductAttributeLabelsV2
                  productItem={
                    {
                      ...product.item,
                      attributes: mapAttributesToObject(
                        product?.item?.attributes
                      )
                    } as Item
                  }
                  isProductCard={true}
                  variant="small"
                />
              </div>
              <div className="absolute -bottom-8 left-0">
                <DataSheetLink attributes={product?.item?.attributes} />
              </div>
              {isSameSellerOfferAvailable && (
                <ShoppingListProductPriceATC
                  shoppingListItemId={product.shoppingListItem.id}
                  productName={product.item.name}
                  productOffer={offer}
                  addedToCart={addedToCart}
                  onAddToCart={onAddToCart}
                  quantity={quantity}
                  vatRegionInfo={vatRegionInfo}
                  shoppingListName={selectedShoppingListName}
                  handleQuantityChange={handleQuantityChange}
                  activeVolumeRange={activeVolumeRange}
                  onRemoveItem={initiateRemovalCountdown}
                  minQuantity={minQuantity}
                  isMinQuantityPurchaseActive={isMinimumPurchaseQuantityActve}
                  itemId={product.item.idItem}
                />
              )}
              {(!offer || isDifferentSellerOfferAvailable) && (
                <ShoppingListProductOfferFlow
                  isDifferentSellerOfferAvailable={
                    isDifferentSellerOfferAvailable
                  }
                  categorySlug={product?.category?.slug}
                  productViewUrl={productViewUrl}
                />
              )}
            </div>
            <div>
              <Button
                variant="ghost"
                className="relative -top-[30px] md:top-0 lg:top-2 text-metro-blue-tint-40 !px-0 !py-0"
                aria-label={t('SL_PAGE_REMOVE_TEXT', {
                  itemName: product?.item?.name,
                  shoppingListName: selectedShoppingListName,
                  interpolation: { escapeValue: false }
                })}
                data-testid="remove_button"
                onClick={() =>
                  initiateRemovalCountdown(
                    product?.shoppingListItem?.id,
                    ShoppingListRemoveItemSource.CROSS
                  )
                }
              >
                <SVGIcon
                  name={SVG_NAMES.CLOSE}
                  width="16"
                  height="16"
                  viewBox="0 0 32 32"
                />
              </Button>
            </div>
          </div>
          {doesSheetExist && <div className="h-6"></div>}
          {/* <DataSheetLink attributes={product?.item?.attributes} /> */}
          {(!offer || isDifferentSellerOfferAvailable) && (
            <div className="md:hidden">
              <UnavailableOfferButton
                isDifferentSellerOfferAvailable={
                  isDifferentSellerOfferAvailable
                }
                productViewUrl={productViewUrl}
                slug={product?.category?.slug}
              />
            </div>
          )}
          {isSameSellerOfferAvailable && (
            <div className="flex flex-col gap-x-4 gap-y-1 mt-3 md:hidden">
              <AddToCartButton
                testId="add_to_cart_button_mobile"
                successTestId="add_to_cart_success_mobile"
                quantity={quantity}
                productOffer={offer}
                handleQuantityChange={handleQuantityChange}
                onAddToCart={onAddToCart}
                onRemoveItem={initiateRemovalCountdown}
                shoppingListItemId={product?.shoppingListItem?.id}
                shoppingListName={selectedShoppingListName}
                addedToCart={addedToCart}
                minQuantity={minQuantity}
                isMinQuantityPurchaseActive={isMinimumPurchaseQuantityActve}
                productName={product.item?.name}
                isMobile
              />
            </div>
          )}
        </li>
      )}
      {offerWasRemoved && isUndoRemovalFunctionalityEnabled && (
        <li
          className="flex flex-col p-4 bg-white-main mb-[1px] w-full  md:p-4 gap-y-1"
          data-testid="undo-removal-shopping-list-item"
        >
          <ShoppingListUndoRemovalItem
            productName={product?.item?.name}
            productViewUrl={productViewUrl}
            undoRemovalItem={handleUndoOfferRemoval}
          />
        </li>
      )}
    </>
  )
}
