import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { FC } from 'react'
import { useTimeout } from 'react-use'

import { SVGIcon } from '@shared/components'
import { SHORT_TIMEOUT } from '@shared/constants/constants'
import { SVG_NAMES } from '@shared/icons/constants'
import { UndoIcon } from '@shared/icons/svg/UndoIcon'

import { SL } from '@modules/shopping-list/constants'

import { PROJECT_PALLET_COLORS } from '@styles/project_palette'

interface Props {
  productName: string
  undoRemovalItem: () => void
  productViewUrl: string
}

export const ShoppingListUndoRemovalItem: FC<Props> = ({
  productName,
  undoRemovalItem,
  productViewUrl
}) => {
  const { t } = useTranslation()

  const [isReady] = useTimeout(SHORT_TIMEOUT)

  const invalidItem = () => {
    return (
      <p
        className="flex items-center justify-center py-2 text-success-main w-[100px] min-w-[100px] mr-6  text-base leading-5 "
        aria-label={t('CART.TEXT.REMOVED')}
        data-testid="item-removed-success"
      >
        <SVGIcon
          className="mr-1"
          name={SVG_NAMES.CHECKMARK}
          width="20px"
          height="20px"
          fill={PROJECT_PALLET_COLORS.success}
          aria-hidden="true"
        />
        {t('CART.TEXT.REMOVED')}
      </p>
    )
  }

  const validOfferButton = () => {
    return (
      <button
        className="flex items-center flex-col justify-center py-2 text-blue-main w-[100px] min-w-[100px] mr-6 text-center"
        aria-label={t('CART.EMPTY_CART.UNDO.TITLE')}
        data-testid="item-removed-pending"
        onClick={undoRemovalItem}
      >
        <UndoIcon width="20px" height="20px" aria-hidden="true" />
        <p className=" max-w-[80px] text-base leading-5" aria-hidden="true">
          {t('CART.EMPTY_CART.UNDO.TITLE')}
        </p>
      </button>
    )
  }

  return (
    <div>
      <div className="flex items-center">
        {isReady() ? invalidItem() : validOfferButton()}
        <div>
          <Link
            href={productViewUrl}
            aria-label={productName}
            className="text-blue-main break-all line-clamp-1 text-base leading-5"
            data-testid="item-product-name"
          >
            {productName}
          </Link>
          <p
            className="text-metro-blue-main text-base leading-5"
            data-testid="item-product-removed-message"
          >
            {t('SL_PAGE_LISTING_UNDO_REMOVAL_ITEM', {
              ns: SL
            })}
          </p>
        </div>
      </div>
    </div>
  )
}
