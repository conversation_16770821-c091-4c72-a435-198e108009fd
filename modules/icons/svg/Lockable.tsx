import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const Lockable = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#e5efff" cx="24" cy="24" r="24" />
        <g>
          <g>
            <path
              fill="#fff"
              d="M24.02,37.57c-.65,0-1.31-.15-1.92-.44l-6.46-3.11c-.7-.34-1.15-1.05-1.15-1.83v-12.65h19.02v12.65c0,.78-.45,1.49-1.15,1.83l-6.42,3.11c-.61.3-1.26.44-1.92.44Z"
            />
            <path
              fill="#002d72"
              d="M24.02,38.17c-.75,0-1.5-.17-2.18-.5l-6.46-3.11c-.91-.44-1.49-1.37-1.49-2.38v-12.65c0-.33.27-.6.6-.6h19.02c.33,0,.6.27.6.6v12.65c0,1-.58,1.93-1.49,2.37l-6.42,3.11c-.69.33-1.42.5-2.18.5ZM15.09,20.13v12.05c0,.55.32,1.06.81,1.29l6.46,3.11c1.05.51,2.26.51,3.31,0l6.42-3.11c.49-.24.81-.75.81-1.29v-12.05H15.09Z"
            />
          </g>
          <path
            fill="#002d72"
            d="M24,31.02c-.33,0-.6-.27-.6-.6v-2.55c0-.33.27-.6.6-.6s.6.27.6.6v2.55c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M24,28.46c-1.13,0-2.05-.92-2.05-2.05s.92-2.05,2.05-2.05,2.05.92,2.05,2.05-.92,2.05-2.05,2.05ZM24,25.56c-.47,0-.85.38-.85.85s.38.85.85.85.85-.38.85-.85-.38-.85-.85-.85Z"
          />
          <path
            fill="#002d72"
            d="M29.37,20.13c-.33,0-.6-.27-.6-.6v-3.73c0-2.63-2.14-4.77-4.77-4.77s-4.77,2.14-4.77,4.77v3.73c0,.33-.27.6-.6.6s-.6-.27-.6-.6v-3.73c0-3.29,2.68-5.97,5.97-5.97s5.97,2.68,5.97,5.97v3.73c0,.33-.27.6-.6.6Z"
          />
        </g>
      </svg>
    )
  }
}
