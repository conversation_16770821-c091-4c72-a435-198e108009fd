import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const VentilatedCooling = {
  viewBox: '0 0 20 20',
  path(props: PathProps) {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.4 10C19.4 15.1911 15.1911 19.4 10 19.4C4.80887 19.4 0.6 15.1911 0.6 10C0.6 4.80887 4.80887 0.6 10 0.6C15.1911 0.6 19.4 4.80887 19.4 10Z"
          stroke="#002D72"
          stroke-width="1.2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11.9 10C11.9 11.0499 11.0499 11.9 10 11.9C8.95012 11.9 8.1 11.0499 8.1 10C8.1 8.95012 8.95012 8.1 10 8.1C11.0499 8.1 11.9 8.95012 11.9 10Z"
          stroke="#002D72"
          stroke-width="1.2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <mask id="path-3-inside-1_4461_356919" fill="white">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8.125 8.125C8.125 8.125 7.5 5.70375 7.5 4.375C7.5 3.0475 8.8275 2.5 10.625 2.5C12.4225 2.5 15 3.555 15 4.375C15 5.195 11.25 5.89875 11.25 7.5"
          />
        </mask>
        <path
          d="M6.96309 8.42493C7.12873 9.06663 7.78322 9.45256 8.42493 9.28691C9.06663 9.12127 9.45256 8.46678 9.28691 7.82507L6.96309 8.42493ZM10.05 7.5C10.05 8.16274 10.5873 8.7 11.25 8.7C11.9127 8.7 12.45 8.16274 12.45 7.5H10.05ZM8.125 8.125C9.28691 7.82507 9.28693 7.82514 9.28695 7.8252C9.28695 7.8252 9.28696 7.82525 9.28696 7.82527C9.28697 7.82529 9.28697 7.82528 9.28696 7.82524C9.28693 7.82515 9.28687 7.82491 9.28678 7.82453C9.28658 7.82378 9.28624 7.82245 9.28576 7.82056C9.28479 7.81678 9.28325 7.81076 9.28119 7.80262C9.27706 7.78633 9.27081 7.76153 9.26275 7.72908C9.24663 7.66416 9.22328 7.56878 9.19513 7.44974C9.1387 7.21115 9.06355 6.88002 8.98862 6.51006C8.83261 5.73987 8.7 4.9072 8.7 4.375H6.3C6.3 5.17155 6.47989 6.21388 6.63638 6.98651C6.7177 7.38795 6.7988 7.74518 6.85956 8.0021C6.89 8.13081 6.91548 8.23497 6.93353 8.30765C6.94257 8.344 6.94975 8.37253 6.95477 8.39235C6.95729 8.40226 6.95926 8.41001 6.96066 8.41547C6.96136 8.4182 6.96191 8.42037 6.96232 8.42195C6.96252 8.42274 6.96269 8.42338 6.96282 8.42388C6.96288 8.42413 6.96293 8.42434 6.96298 8.42451C6.963 8.4246 6.96303 8.4247 6.96304 8.42475C6.96306 8.42484 6.96309 8.42493 8.125 8.125ZM8.7 4.375C8.7 4.25281 8.72743 4.1982 8.74582 4.1695C8.76844 4.1342 8.82378 4.06894 8.96194 3.99377C9.26541 3.82866 9.81518 3.7 10.625 3.7V1.3C9.63732 1.3 8.62459 1.44509 7.81493 1.8856C6.92887 2.36769 6.3 3.21073 6.3 4.375H8.7ZM10.625 3.7C11.3286 3.7 12.2627 3.91645 13.0205 4.23581C13.3973 4.39461 13.6686 4.55327 13.8229 4.67579C14.0531 4.85862 13.8 4.76475 13.8 4.375H16.2C16.2 3.57525 15.6247 3.042 15.3155 2.7964C14.9303 2.49048 14.4449 2.23164 13.9526 2.02419C12.9723 1.61105 11.7189 1.3 10.625 1.3V3.7ZM13.8 4.375C13.8 4.1894 13.8546 4.04472 13.9033 3.95508C13.9487 3.87143 13.9923 3.82855 13.9974 3.82347C14.0006 3.82035 13.9547 3.86224 13.8067 3.94909C13.4933 4.13301 13.1208 4.29936 12.5794 4.57575C12.102 4.81943 11.5206 5.13784 11.0511 5.54375C10.5887 5.94352 10.05 6.59019 10.05 7.5H12.45C12.45 7.60919 12.38 7.56742 12.6208 7.35925C12.8544 7.15724 13.2105 6.94822 13.6706 6.71332C14.0667 6.51111 14.6317 6.24769 15.0214 6.019C15.225 5.89954 15.4721 5.73941 15.6803 5.53459C15.8732 5.34489 16.2 4.95243 16.2 4.375H13.8Z"
          fill="#002D72"
          mask="url(#path-3-inside-1_4461_356919)"
        />
        <mask id="path-5-inside-2_4461_356919" fill="white">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M9.31395 12.5601C9.31395 12.5601 7.52895 14.3126 6.37895 14.9764C5.22895 15.6401 4.09145 14.7639 3.1927 13.2076C2.29395 11.6514 1.91895 8.8914 2.62895 8.4814C3.33895 8.0714 5.8227 10.9676 7.2102 10.1664"
          />
        </mask>
        <path
          d="M10.1546 13.4164C10.6276 12.9521 10.6345 12.1924 10.1702 11.7195C9.70594 11.2465 8.94617 11.2396 8.47326 11.7039L10.1546 13.4164ZM7.8103 11.2056C8.38422 10.8741 8.5808 10.1402 8.24937 9.5663C7.91795 8.99238 7.18402 8.7958 6.6101 9.12722L7.8103 11.2056ZM9.31395 12.5601C8.47326 11.7039 8.4733 11.7038 8.47335 11.7038C8.47335 11.7038 8.47339 11.7037 8.4734 11.7037C8.47342 11.7037 8.47341 11.7037 8.47338 11.7037C8.47331 11.7038 8.47314 11.704 8.47286 11.7043C8.4723 11.7048 8.47131 11.7058 8.46992 11.7071C8.46713 11.7099 8.46268 11.7142 8.45666 11.7201C8.44461 11.7318 8.42625 11.7496 8.40218 11.7728C8.354 11.8193 8.28304 11.8872 8.194 11.9711C8.01554 12.1393 7.76629 12.37 7.48332 12.6199C6.89419 13.1403 6.2395 13.6714 5.77909 13.9371L6.97881 16.0157C7.6684 15.6177 8.48121 14.9407 9.07208 14.4188C9.37912 14.1476 9.64799 13.8987 9.84016 13.7176C9.93642 13.6268 10.0139 13.5527 10.0678 13.5007C10.0948 13.4747 10.1159 13.4542 10.1306 13.4399C10.1379 13.4328 10.1436 13.4272 10.1477 13.4233C10.1497 13.4213 10.1513 13.4197 10.1525 13.4186C10.153 13.418 10.1535 13.4176 10.1539 13.4172C10.1541 13.417 10.1542 13.4169 10.1543 13.4167C10.1544 13.4167 10.1545 13.4166 10.1545 13.4166C10.1546 13.4165 10.1546 13.4164 9.31395 12.5601ZM5.77909 13.9371C5.67316 13.9982 5.61209 14.0018 5.57804 14.0002C5.53616 13.9982 5.45198 13.9829 5.31782 13.9009C5.02309 13.7206 4.63683 13.3088 4.23186 12.6075L2.15354 13.8078C2.64732 14.6628 3.27919 15.4672 4.0654 15.9482C4.92588 16.4746 5.97037 16.5978 6.97881 16.0157L5.77909 13.9371ZM4.23186 12.6075C3.88017 11.9985 3.60055 11.0814 3.49822 10.2654C3.44734 9.85965 3.44911 9.54534 3.47808 9.35042C3.52132 9.05951 3.5666 9.32565 3.22904 9.52058L2.02886 7.44222C1.3363 7.84215 1.16221 8.60704 1.10416 8.99757C1.03184 9.4841 1.0504 10.0339 1.11687 10.564C1.24923 11.6195 1.60649 12.8605 2.15354 13.8078L4.23186 12.6075ZM3.22904 9.52058C3.06826 9.61342 2.91558 9.63848 2.81354 9.64115C2.71833 9.64364 2.65936 9.62733 2.65236 9.62539C2.64801 9.62419 2.70719 9.64297 2.85638 9.72774C3.17238 9.90728 3.50251 10.1467 4.01263 10.4775C4.46234 10.7691 5.02886 11.1135 5.61528 11.3171C6.19296 11.5177 7.02236 11.6606 7.8103 11.2056L6.6101 9.12722C6.70429 9.07283 6.70291 9.15418 6.40246 9.04987C6.11077 8.94859 5.75182 8.74482 5.3184 8.46377C4.9454 8.2219 4.43474 7.86419 4.04199 7.64104C3.83681 7.52445 3.57462 7.3905 3.29313 7.31251C3.03241 7.24028 2.52902 7.15339 2.02886 7.44222L3.22904 9.52058Z"
          fill="#002D72"
          mask="url(#path-5-inside-2_4461_356919)"
        />
        <mask id="path-7-inside-3_4461_356919" fill="white">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12.5642 9.3125C12.5642 9.3125 14.9742 9.9825 16.1242 10.6462C17.2742 11.31 17.0842 12.7337 16.1855 14.29C15.2867 15.8462 13.0842 17.5513 12.3742 17.1413C11.6642 16.7313 12.9305 13.1325 11.543 12.3312"
          />
        </mask>
        <path
          d="M12.8856 8.15635C12.2471 7.97883 11.5856 8.35255 11.4081 8.99108C11.2305 9.62961 11.6043 10.2911 12.2428 10.4687L12.8856 8.15635ZM12.1431 11.2921C11.5691 10.9607 10.8352 11.1572 10.5038 11.7312C10.1724 12.3051 10.369 13.039 10.9429 13.3704L12.1431 11.2921ZM12.5642 9.3125C12.2428 10.4687 12.2427 10.4686 12.2427 10.4686C12.2427 10.4686 12.2426 10.4686 12.2426 10.4686C12.2426 10.4686 12.2426 10.4686 12.2426 10.4686C12.2427 10.4686 12.243 10.4687 12.2433 10.4688C12.2441 10.469 12.2454 10.4694 12.2473 10.4699C12.2511 10.471 12.257 10.4726 12.2651 10.4749C12.2813 10.4795 12.3059 10.4865 12.3381 10.4958C12.4024 10.5143 12.4967 10.5418 12.6139 10.5769C12.8488 10.6474 13.1732 10.748 13.5311 10.8681C14.2763 11.1182 15.0637 11.4197 15.5244 11.6856L16.7241 9.60694C16.0347 9.20904 15.0422 8.84368 14.2948 8.59284C13.9065 8.4625 13.5565 8.35407 13.3036 8.27818C13.1769 8.24017 13.074 8.21014 13.002 8.18942C12.966 8.17905 12.9377 8.171 12.918 8.16544C12.9082 8.16266 12.9005 8.1605 12.895 8.15897C12.8923 8.15821 12.8902 8.15761 12.8886 8.15717C12.8878 8.15695 12.8872 8.15677 12.8867 8.15664C12.8864 8.15657 12.8862 8.15651 12.886 8.15646C12.886 8.15644 12.8859 8.15641 12.8858 8.1564C12.8857 8.15637 12.8856 8.15635 12.5642 9.3125ZM15.5244 11.6856C15.6302 11.7467 15.6638 11.7977 15.6794 11.828C15.6987 11.8652 15.7275 11.9457 15.7235 12.103C15.7147 12.4483 15.5512 12.9888 15.1463 13.6899L17.2246 14.8901C17.7185 14.035 18.0993 13.0854 18.1227 12.1639C18.1484 11.1555 17.7327 10.1891 16.7241 9.60694L15.5244 11.6856ZM15.1463 13.6899C14.7945 14.299 14.1399 14.9998 13.4844 15.4965C13.1584 15.7434 12.8854 15.8991 12.7021 15.9714C12.4286 16.0794 12.6366 15.9071 12.9743 16.1021L11.7741 18.1804C12.4668 18.5804 13.2164 18.3487 13.5836 18.2037C14.0411 18.023 14.508 17.732 14.9337 17.4095C15.7816 16.767 16.6777 15.8372 17.2246 14.8901L15.1463 13.6899ZM12.9743 16.1021C13.1351 16.1949 13.2331 16.3146 13.2864 16.4016C13.3361 16.4828 13.3515 16.542 13.3533 16.549C13.3544 16.5533 13.3411 16.4927 13.3399 16.3211C13.3374 15.9577 13.3796 15.5521 13.4111 14.945C13.4387 14.4097 13.4538 13.7469 13.3369 13.1373C13.2218 12.5367 12.9309 11.747 12.1431 11.2921L10.9429 13.3704C10.8488 13.3161 10.9199 13.2767 10.9798 13.5891C11.038 13.8924 11.0409 14.3051 11.0143 14.821C10.9913 15.2649 10.9368 15.886 10.94 16.3377C10.9416 16.5737 10.9567 16.8677 11.0299 17.1504C11.0976 17.4123 11.274 17.8916 11.7741 18.1804L12.9743 16.1021Z"
          fill="#002D72"
          mask="url(#path-7-inside-3_4461_356919)"
        />
      </svg>
    )
  }
}
