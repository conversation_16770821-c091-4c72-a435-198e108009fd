import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const LockableV2 = {
  viewBox: '0 0 40 40',
  path(props: PathProps) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#fff" cx="20" cy="20" r="20" />
        <g>
          <path
            fill="002D72"
            d="M20.02,31.91c-.65,0-1.27-.14-1.86-.43l-5.38-2.59c-.79-.38-1.3-1.19-1.3-2.07v-10.54c0-.33.27-.6.6-.6h15.85c.33,0,.6.27.6.6v10.54c0,.88-.51,1.69-1.29,2.07l-5.35,2.59c-.59.29-1.21.43-1.86.43ZM12.68,16.88v9.94c0,.42.24.81.62.99l5.38,2.59c.85.41,1.83.41,2.67,0l5.35-2.59c.38-.18.62-.57.62-.99v-9.94h-14.65Z"
          />
          <path
            fill="002D72"
            d="M20,25.95c-.33,0-.6-.27-.6-.6v-2.13c0-.33.27-.6.6-.6s.6.27.6.6v2.13c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="002D72"
            d="M20,23.82c-1,0-1.81-.81-1.81-1.81s.81-1.81,1.81-1.81,1.81.81,1.81,1.81-.81,1.81-1.81,1.81ZM20,21.4c-.34,0-.61.27-.61.61s.27.61.61.61.61-.27.61-.61-.27-.61-.61-.61Z"
          />
          <path
            fill="002D72"
            d="M24.48,16.88c-.33,0-.6-.27-.6-.6v-3.11c0-2.14-1.74-3.88-3.88-3.88s-3.88,1.74-3.88,3.88v3.11c0,.33-.27.6-.6.6s-.6-.27-.6-.6v-3.11c0-2.8,2.28-5.08,5.08-5.08s5.08,2.28,5.08,5.08v3.11c0,.33-.27.6-.6.6Z"
          />
        </g>
      </svg>
    )
  }
}
