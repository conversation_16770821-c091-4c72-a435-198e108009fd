import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const TemperatureDisplayV2 = {
  viewBox: '0 0 40 40',
  path(props: PathProps) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#fff" cx="20" cy="20" r="20" />
        <g>
          <g>
            <path
              fill="#002d72"
              d="M31.31,20.56h-.01c-.16,0-.32-.08-.43-.2-2.79-3.07-6.75-4.84-10.88-4.84s-8.06,1.75-10.85,4.8c-.11.12-.27.19-.43.2-.15,0-.32-.06-.44-.18l-3.95-3.95c-.23-.23-.24-.6-.01-.83,4.12-4.4,9.69-6.82,15.68-6.82s11.59,2.44,15.71,6.86c.22.24.21.6-.02.83l-3.95,3.95c-.11.11-.26.18-.42.18ZM19.98,14.33c4.25,0,8.35,1.73,11.33,4.77l3.09-3.1c-3.85-3.91-8.95-6.06-14.43-6.06s-10.55,2.13-14.4,6.02l3.1,3.1c2.98-3.02,7.07-4.74,11.3-4.74Z"
            />
            <g>
              <path
                fill="#002d72"
                d="M28.17,17.92c-.1,0-.21-.03-.3-.08-.29-.17-.38-.54-.21-.82l2.73-4.66c.17-.29.54-.38.82-.21.29.17.38.54.21.82l-2.73,4.66c-.11.19-.31.3-.52.3Z"
              />
              <path
                fill="#002d72"
                d="M24.96,12.82s-.1,0-.15-.02c-.32-.08-.51-.41-.43-.73l.47-1.82c.08-.32.41-.51.73-.43.32.08.51.41.43.73l-.47,1.82c-.07.27-.31.45-.58.45Z"
              />
            </g>
            <g>
              <path
                fill="#002d72"
                d="M15.04,12.82c-.27,0-.51-.18-.58-.45l-.47-1.81c-.08-.32.11-.65.43-.73.32-.09.65.11.73.43l.47,1.81c.08.32-.11.65-.43.73-.05.01-.1.02-.15.02Z"
              />
              <path
                fill="#002d72"
                d="M10.06,14.9c-.21,0-.41-.11-.52-.3l-.96-1.62c-.17-.29-.07-.65.21-.82.29-.17.65-.07.82.21l.96,1.62c.17.29.07.65-.21.82-.09.06-.2.08-.3.08Z"
              />
            </g>
            <path
              fill="#002d72"
              d="M19.99,12.29c-.33,0-.6-.27-.6-.6v-1.98c0-.33.26-.6.6-.6h0c.33,0,.6.27.6.6v1.98c0,.33-.26.6-.6.6h0Z"
            />
            <path
              fill="#002d72"
              d="M20.02,30.44c-.33,0-.6-.27-.6-.6l-.03-15.88c0-.33.27-.6.6-.6h0c.33,0,.6.27.6.6l.03,15.88c0,.33-.27.6-.6.6h0Z"
            />
          </g>
          <path
            fill="#002d72"
            d="M20.02,33.25c-1.11,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2ZM20.02,30.44c-.44,0-.81.36-.81.8s.36.8.81.8.8-.36.8-.8-.36-.8-.8-.8Z"
          />
        </g>
      </svg>
    )
  }
}
