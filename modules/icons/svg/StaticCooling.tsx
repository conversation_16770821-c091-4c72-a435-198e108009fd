import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const StaticCooling = {
  viewBox: '0 0 20 20',
  path(props: PathProps) {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="0.6"
          y="0.6"
          width="18.8"
          height="18.8"
          stroke="#002D72"
          stroke-width="1.2"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M10.6702 2.41012C10.6278 2.17688 10.4237 2 10.1782 2C9.90209 2 9.67824 2.22386 9.67824 2.5V4.293L8.53179 3.14645L8.46254 3.08859C8.26767 2.9536 7.99825 2.97288 7.82468 3.14645C7.62942 3.34171 7.62942 3.65829 7.82468 3.85355L9.67824 5.707L9.6777 7.55012L9.53908 7.58245C9.02905 7.71693 8.5828 8.00854 8.25038 8.40824L6.62159 7.54262L5.85617 5.03635L5.82219 4.95276C5.71351 4.74208 5.46657 4.63262 5.23183 4.70439L5.14824 4.73837C4.93756 4.84705 4.8281 5.09399 4.89987 5.32873L5.37399 6.87925L3.79086 6.03749L3.70773 6.00241C3.4819 5.93028 3.22989 6.0275 3.11465 6.24423C2.98501 6.48805 3.07757 6.7908 3.32139 6.92044L4.90452 7.7622L3.35395 8.23618L3.27035 8.27016C3.05967 8.37884 2.95021 8.62578 3.02198 8.86052C3.10272 9.12459 3.38224 9.27322 3.64632 9.19248L6.15301 8.42603L7.7801 9.29134L7.73878 9.45066C7.69915 9.62743 7.67824 9.81127 7.67824 10C7.67824 10.2458 7.7137 10.4833 7.7798 10.7077L6.15301 11.574L3.64632 10.8075L3.55801 10.7889L3.47033 10.7864C3.29675 10.7968 3.13747 10.8979 3.05596 11.0559L3.02198 11.1395L3.00341 11.2278L3.00089 11.3155C3.01127 11.489 3.11234 11.6483 3.27035 11.7298L3.35395 11.7638L4.90452 12.2378L3.32139 13.0796L3.24582 13.1289C3.05975 13.2758 2.99942 13.539 3.11465 13.7558C3.22989 13.9725 3.4819 14.0697 3.70773 13.9976L3.79086 13.9625L5.37399 13.1207L4.89987 14.6713L4.88129 14.7596L4.87877 14.8473C4.89089 15.0498 5.02644 15.2328 5.23183 15.2956C5.46657 15.3674 5.71351 15.2579 5.82219 15.0472L5.85617 14.9636L6.62336 12.4564L8.25038 11.5918L8.35289 11.7083C8.66913 12.046 9.07805 12.296 9.53908 12.4176L9.6777 12.4499L9.67824 14.292L7.82468 16.1464L7.76683 16.2157C7.63183 16.4106 7.65112 16.68 7.82468 16.8536C7.99825 17.0271 8.26767 17.0464 8.46254 16.9114L8.53179 16.8536L9.67824 15.707V17.5L9.68629 17.5899C9.72863 17.8231 9.93278 18 10.1782 18C10.4544 18 10.6782 17.7761 10.6782 17.5V15.707L11.8247 16.8536L11.8939 16.9114C12.0644 17.0295 12.292 17.0295 12.4625 16.9114L12.5318 16.8536L12.5896 16.7843C12.7078 16.6138 12.7078 16.3862 12.5896 16.2157L12.5318 16.1464L10.6782 14.293L10.6777 12.4501L10.8638 12.4048C11.357 12.2645 11.788 11.9765 12.1061 11.5918L13.7331 12.4564L14.5003 14.9636L14.5343 15.0472C14.643 15.2579 14.8899 15.3674 15.1246 15.2956C15.3594 15.2238 15.5029 14.995 15.4752 14.7596L15.4566 14.6713L14.9825 13.1207L16.5656 13.9625L16.6487 13.9976C16.8746 14.0697 17.1266 13.9725 17.2418 13.7558C17.3715 13.512 17.2789 13.2092 17.0351 13.0796L15.452 12.2378L17.0025 11.7638L17.0861 11.7298C17.2705 11.6347 17.3773 11.4338 17.3531 11.2278L17.3345 11.1395L17.3005 11.0559C17.2054 10.8715 17.0045 10.7647 16.7985 10.7889L16.7102 10.8075L14.2035 11.574L12.5764 10.7087L12.614 10.5655C12.656 10.3838 12.6782 10.1945 12.6782 10C12.6782 9.75421 12.6428 9.51671 12.5767 9.29234L14.2035 8.42603L16.7102 9.19248L16.7985 9.21106C17.0339 9.23877 17.2627 9.09525 17.3345 8.86052C17.4063 8.62578 17.2968 8.37884 17.0861 8.27016L17.0025 8.23618L15.452 7.7622L17.0351 6.92044L17.1107 6.87113C17.2967 6.72425 17.3571 6.46096 17.2418 6.24423C17.1266 6.0275 16.8746 5.93028 16.6487 6.00241L16.5656 6.03749L14.9825 6.87925L15.4566 5.32873L15.4752 5.24042C15.4994 5.03442 15.3926 4.83346 15.2082 4.73837L15.1246 4.70439L15.0363 4.68581C14.8303 4.66157 14.6294 4.76842 14.5343 4.95276L14.5003 5.03635L13.7349 7.54262L12.1061 8.40824L11.9765 8.26328C11.6305 7.90502 11.179 7.65002 10.6777 7.54989L10.6782 5.706L12.5318 3.85355L12.5896 3.78431C12.7246 3.58944 12.7054 3.32001 12.5318 3.14645L12.4625 3.08859C12.2677 2.9536 11.9982 2.97288 11.8247 3.14645L10.6782 4.293V2.5L10.6702 2.41012ZM8.67824 10C8.67824 9.17157 9.34981 8.5 10.1782 8.5C11.0067 8.5 11.6782 9.17157 11.6782 10C11.6782 10.8284 11.0067 11.5 10.1782 11.5C9.34981 11.5 8.67824 10.8284 8.67824 10Z"
          fill="#002D72"
        />
        <mask
          id="mask0_4902_187698"
          mask-type="luminance"
          maskUnits="userSpaceOnUse"
          x="3"
          y="2"
          width="15"
          height="16"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.6702 2.41012C10.6278 2.17688 10.4237 2 10.1782 2C9.90209 2 9.67824 2.22386 9.67824 2.5V4.293L8.53179 3.14645L8.46254 3.08859C8.26767 2.9536 7.99825 2.97288 7.82468 3.14645C7.62942 3.34171 7.62942 3.65829 7.82468 3.85355L9.67824 5.707L9.6777 7.55012L9.53908 7.58245C9.02905 7.71693 8.5828 8.00854 8.25038 8.40824L6.62159 7.54262L5.85617 5.03635L5.82219 4.95276C5.71351 4.74208 5.46657 4.63262 5.23183 4.70439L5.14824 4.73837C4.93756 4.84705 4.8281 5.09399 4.89987 5.32873L5.37399 6.87925L3.79086 6.03749L3.70773 6.00241C3.4819 5.93028 3.22989 6.0275 3.11465 6.24423C2.98501 6.48805 3.07757 6.7908 3.32139 6.92044L4.90452 7.7622L3.35395 8.23618L3.27035 8.27016C3.05967 8.37884 2.95021 8.62578 3.02198 8.86052C3.10272 9.12459 3.38224 9.27322 3.64632 9.19248L6.15301 8.42603L7.7801 9.29134L7.73878 9.45066C7.69915 9.62743 7.67824 9.81127 7.67824 10C7.67824 10.2458 7.7137 10.4833 7.7798 10.7077L6.15301 11.574L3.64632 10.8075L3.55801 10.7889L3.47033 10.7864C3.29675 10.7968 3.13747 10.8979 3.05596 11.0559L3.02198 11.1395L3.00341 11.2278L3.00089 11.3155C3.01127 11.489 3.11234 11.6483 3.27035 11.7298L3.35395 11.7638L4.90452 12.2378L3.32139 13.0796L3.24582 13.1289C3.05975 13.2758 2.99942 13.539 3.11465 13.7558C3.22989 13.9725 3.4819 14.0697 3.70773 13.9976L3.79086 13.9625L5.37399 13.1207L4.89987 14.6713L4.88129 14.7596L4.87877 14.8473C4.89089 15.0498 5.02644 15.2328 5.23183 15.2956C5.46657 15.3674 5.71351 15.2579 5.82219 15.0472L5.85617 14.9636L6.62336 12.4564L8.25038 11.5918L8.35289 11.7083C8.66913 12.046 9.07805 12.296 9.53908 12.4176L9.6777 12.4499L9.67824 14.292L7.82468 16.1464L7.76683 16.2157C7.63183 16.4106 7.65112 16.68 7.82468 16.8536C7.99825 17.0271 8.26767 17.0464 8.46254 16.9114L8.53179 16.8536L9.67824 15.707V17.5L9.68629 17.5899C9.72863 17.8231 9.93278 18 10.1782 18C10.4544 18 10.6782 17.7761 10.6782 17.5V15.707L11.8247 16.8536L11.8939 16.9114C12.0644 17.0295 12.292 17.0295 12.4625 16.9114L12.5318 16.8536L12.5896 16.7843C12.7078 16.6138 12.7078 16.3862 12.5896 16.2157L12.5318 16.1464L10.6782 14.293L10.6777 12.4501L10.8638 12.4048C11.357 12.2645 11.788 11.9765 12.1061 11.5918L13.7331 12.4564L14.5003 14.9636L14.5343 15.0472C14.643 15.2579 14.8899 15.3674 15.1246 15.2956C15.3594 15.2238 15.5029 14.995 15.4752 14.7596L15.4566 14.6713L14.9825 13.1207L16.5656 13.9625L16.6487 13.9976C16.8746 14.0697 17.1266 13.9725 17.2418 13.7558C17.3715 13.512 17.2789 13.2092 17.0351 13.0796L15.452 12.2378L17.0025 11.7638L17.0861 11.7298C17.2705 11.6347 17.3773 11.4338 17.3531 11.2278L17.3345 11.1395L17.3005 11.0559C17.2054 10.8715 17.0045 10.7647 16.7985 10.7889L16.7102 10.8075L14.2035 11.574L12.5764 10.7087L12.614 10.5655C12.656 10.3838 12.6782 10.1945 12.6782 10C12.6782 9.75421 12.6428 9.51671 12.5767 9.29234L14.2035 8.42603L16.7102 9.19248L16.7985 9.21106C17.0339 9.23877 17.2627 9.09525 17.3345 8.86052C17.4063 8.62578 17.2968 8.37884 17.0861 8.27016L17.0025 8.23618L15.452 7.7622L17.0351 6.92044L17.1107 6.87113C17.2967 6.72425 17.3571 6.46096 17.2418 6.24423C17.1266 6.0275 16.8746 5.93028 16.6487 6.00241L16.5656 6.03749L14.9825 6.87925L15.4566 5.32873L15.4752 5.24042C15.4994 5.03442 15.3926 4.83346 15.2082 4.73837L15.1246 4.70439L15.0363 4.68581C14.8303 4.66157 14.6294 4.76842 14.5343 4.95276L14.5003 5.03635L13.7349 7.54262L12.1061 8.40824L11.9765 8.26328C11.6305 7.90502 11.179 7.65002 10.6777 7.54989L10.6782 5.706L12.5318 3.85355L12.5896 3.78431C12.7246 3.58944 12.7054 3.32001 12.5318 3.14645L12.4625 3.08859C12.2677 2.9536 11.9982 2.97288 11.8247 3.14645L10.6782 4.293V2.5L10.6702 2.41012ZM8.67824 10C8.67824 9.17157 9.34981 8.5 10.1782 8.5C11.0067 8.5 11.6782 9.17157 11.6782 10C11.6782 10.8284 11.0067 11.5 10.1782 11.5C9.34981 11.5 8.67824 10.8284 8.67824 10Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_4902_187698)">
          <rect x="2" y="2" width="16" height="16" fill="#002D72" />
        </g>
      </svg>
    )
  }
}
