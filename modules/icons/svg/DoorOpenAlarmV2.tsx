import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const DoorOpenAlarmV2 = {
  viewBox: '0 0 40 40',
  path(props: PathProps) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#fff" cx="20" cy="20" r="20" />
        <g>
          <path
            fill="#002D72"
            d="M27.57,29.49h-15.14c-1.21,0-2.19-.98-2.19-2.19,0-.8.44-1.54,1.14-1.92l.41.46-.29-.52c.25-.14.51-.28.71-.41.16-.1.6-.37.9-.85.25-.4.33-.79.37-1.06.03-.22.04-.47.06-.67l.39-7.01c.18-3.22,2.85-5.75,6.08-5.75s5.9,2.52,6.08,5.75l.39,6.93c.01.25.03.52.06.75.04.26.12.66.37,1.05.3.47.74.75.89.84.22.14.49.29.75.43l-.19.58.29-.53c.7.39,1.14,1.12,1.14,1.92,0,1.21-.98,2.19-2.19,2.19ZM20,10.76c-2.59,0-4.74,2.03-4.88,4.62l-.39,6.93c-.02.31-.04.59-.07.85-.03.22-.12.87-.54,1.53-.44.71-1.07,1.09-1.27,1.22-.23.15-.51.3-.78.45l-.12.07c-.32.17-.52.51-.52.87,0,.55.45,1,.99,1h15.14c.55,0,1-.45,1-1,0-.36-.2-.7-.52-.87l-.1-.06c-.28-.15-.56-.31-.79-.46-.21-.13-.83-.52-1.27-1.22-.41-.66-.51-1.31-.54-1.53-.04-.27-.05-.58-.07-.81l-.39-6.98c-.15-2.59-2.29-4.62-4.88-4.62Z"
          />
          <g>
            <path
              fill="#002D72"
              d="M12.03,19.22s-.02,0-.03,0c-.33-.02-.58-.3-.57-.63l.2-3.57c.16-2.79,1.69-5.32,4.1-6.75.29-.17.65-.07.82.21s.07.65-.21.82c-2.07,1.22-3.38,3.38-3.51,5.78l-.2,3.57c-.02.32-.28.57-.6.57Z"
            />
            <path
              fill="#002D72"
              d="M27.97,19.18c-.32,0-.58-.25-.6-.57l-.2-3.53c-.13-2.39-1.45-4.55-3.51-5.78-.29-.17-.38-.54-.21-.82.17-.28.54-.38.82-.21,2.41,1.43,3.94,3.95,4.1,6.74l.2,3.53c.02.33-.23.61-.57.63-.01,0-.02,0-.03,0Z"
            />
          </g>
          <path
            fill="#002D72"
            d="M20,32.27c-1.39,0-2.52-1.13-2.52-2.52v-.86h1.2v.86c0,.73.59,1.32,1.32,1.32s1.32-.59,1.32-1.32v-.86h1.2v.86c0,1.39-1.13,2.52-2.52,2.52Z"
          />
          <path
            fill="#002D72"
            d="M20,10.76c-.33,0-.6-.27-.6-.6v-1.83c0-.33.27-.6.6-.6s.6.27.6.6v1.83c0,.33-.27.6-.6.6Z"
          />
        </g>
      </svg>
    )
  }
}
