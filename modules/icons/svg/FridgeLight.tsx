import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const FridgeLight = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <g clipPath='url(#clippath)'>
          <path
            fill="#e5efff"
            d="M24,48c13.25,0,24-10.75,24-24S37.25,0,24,0,0,10.75,0,24s10.75,24,24,24Z"
          />
          <g>
            <path
              fill="#fff"
              d="M24,30.84c3.78,0,6.84-3.06,6.84-6.84s-3.06-6.84-6.84-6.84-6.84,3.06-6.84,6.84,3.06,6.84,6.84,6.84Z"
            />
            <path
              fill="#002d72"
              d="M24,31.44c-4.1,0-7.44-3.34-7.44-7.44s3.34-7.44,7.44-7.44,7.44,3.34,7.44,7.44-3.34,7.44-7.44,7.44<PERSON><PERSON>24,17.76c-3.44,0-6.24,2.8-6.24,6.24s2.8,6.24,6.24,6.24,6.24-2.8,6.24-6.24-2.8-6.24-6.24-6.24Z"
            />
          </g>
          <path
            fill="#002d72"
            d="M24,14.33c-.33,0-.6-.27-.6-.6v-3.53c0-.33.27-.6.6-.6s.6.27.6.6v3.53c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M31.26,17.34c-.15,0-.31-.06-.42-.18-.23-.23-.23-.61,0-.85l2.49-2.49c.23-.23.61-.23.85,0,.***********,0,.85l-2.49,2.49c-.12.12-.27.18-.42.18Z"
          />
          <path
            fill="#002d72"
            d="M37.8,24.6h-3.53c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h3.53c.33,0,.6.27.6.6s-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M33.75,34.35c-.15,0-.31-.06-.42-.17l-2.49-2.49c-.23-.23-.23-.61,0-.85.23-.23.61-.23.85,0l2.49,2.49c.***********,0,.85-.12.12-.27.18-.42.18Z"
          />
          <path
            fill="#002d72"
            d="M24,38.4c-.33,0-.6-.27-.6-.6v-3.53c0-.33.27-.6.6-.6s.6.27.6.6v3.53c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M14.25,34.35c-.15,0-.31-.06-.42-.18-.23-.23-.23-.61,0-.85l2.49-2.49c.23-.23.61-.23.85,0,.***********,0,.85l-2.49,2.49c-.12.12-.27.17-.42.17Z"
          />
          <path
            fill="#002d72"
            d="M13.73,24.6h-3.53c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h3.53c.33,0,.6.27.6.6s-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M16.74,17.34c-.15,0-.31-.06-.42-.18l-2.49-2.49c-.23-.23-.23-.61,0-.85.23-.23.61-.23.85,0l2.49,2.49c.***********,0,.85-.12.12-.27.18-.42.18Z"
          />
        </g>
        <defs>
          <clipPath id="clippath">
            <rect fill="none" width="48" height="48" />
          </clipPath>
        </defs>
      </svg>
    )
  }
}
