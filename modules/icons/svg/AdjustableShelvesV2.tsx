import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const AdjustableShelvesV2 = {
  viewBox: '0 0 41 41',
  path(props: PathProps) {
    return (
      <svg
        width="41"
        height="41"
        viewBox="0 0 41 41"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#fff" cx="20" cy="20" r="20" />
        <g>
          <path
            fill="#002d72"
            d="M32.41,28.64H7.59c-.33,0-.6-.27-.6-.6v-2.56c0-.33.27-.6.6-.6h24.82c.33,0,.6.27.6.6v2.56c0,.33-.27.6-.6.6ZM8.19,27.44h23.62v-1.36H8.19v1.36Z"
          />
          <path
            fill="#002d72"
            d="M32.41,21.92H7.59c-.33,0-.6-.27-.6-.6v-2.56c0-.33.27-.6.6-.6h24.82c.33,0,.6.27.6.6v2.56c0,.33-.27.6-.6.6ZM8.19,20.72h23.62v-1.36H8.19v1.36Z"
          />
          <path
            fill="#002d72"
            d="M32.41,15.2H7.59c-.33,0-.6-.27-.6-.6v-2.56c0-.33.27-.6.6-.6h24.82c.33,0,.6.27.6.6v2.56c0,.33-.27.6-.6.6ZM8.19,14h23.62v-1.36H8.19v1.36Z"
          />
          <rect fill="#002d72" x="9.79" y="14.6" width="1.2" height="4.17" />
          <rect fill="#002d72" x="9.79" y="21.62" width="1.2" height="3.87" />
          <rect fill="#002d72" x="29.31" y="14.6" width="1.2" height="4.17" />
          <rect fill="#002d72" x="29.31" y="21.62" width="1.2" height="3.87" />
          <path
            fill="#002d72"
            d="M10.39,31.81c-.33,0-.6-.27-.6-.6v-2.68c0-.33.27-.6.6-.6s.6.27.6.6v2.68c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M29.91,31.81c-.33,0-.6-.27-.6-.6v-2.68c0-.33.27-.6.6-.6s.6.27.6.6v2.68c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M10.39,12.07c-.33,0-.6-.27-.6-.6v-2.68c0-.33.27-.6.6-.6s.6.27.6.6v2.68c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M29.91,12.07c-.33,0-.6-.27-.6-.6v-2.68c0-.33.27-.6.6-.6s.6.27.6.6v2.68c0,.33-.27.6-.6.6Z"
          />
        </g>
      </svg>
    )
  }
}
