import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const FridgeLightV2 = {
  viewBox: '0 0 40 40',
  path(props: PathProps) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          fill="#fff"
          d="M20,40c11.05,0,20-8.95,20-20S31.05,0,20,0,0,8.95,0,20s8.95,20,20,20Z"
        />
        <path
          fill="#002d72"
          d="M20,26.3c-3.47,0-6.3-2.83-6.3-6.3s2.83-6.3,6.3-6.3,6.3,2.83,6.3,6.3-2.83,6.3-6.3,6.3ZM20,14.9c-2.81,0-5.1,2.29-5.1,5.1s2.29,5.1,5.1,5.1,5.1-2.29,5.1-5.1-2.29-5.1-5.1-5.1Z"
        />
        <path
          fill="#002d72"
          d="M20,12.04c-.33,0-.6-.27-.6-.6v-2.94c0-.33.27-.6.6-.6s.6.27.6.6v2.94c0,.33-.27.6-.6.6Z"
        />
        <path
          fill="#002d72"
          d="M26.05,14.55c-.15,0-.31-.06-.42-.17-.23-.23-.23-.61,0-.85l2.07-2.08c.23-.23.61-.23.85,0,.23.23.23.61,0,.85l-2.07,2.08c-.12.12-.27.18-.42.18Z"
        />
        <path
          fill="#002d72"
          d="M31.5,20.6h-2.94c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.94c.33,0,.6.27.6.6s-.27.6-.6.6Z"
        />
        <path
          fill="#002d72"
          d="M28.13,28.73c-.15,0-.31-.06-.42-.18l-2.07-2.08c-.23-.23-.23-.61,0-.85.23-.23.61-.23.85,0l2.07,2.08c.23.23.23.61,0,.85-.12.12-.27.17-.42.17Z"
        />
        <path
          fill="#002d72"
          d="M20,32.1c-.33,0-.6-.27-.6-.6v-2.94c0-.33.27-.6.6-.6s.6.27.6.6v2.94c0,.33-.27.6-.6.6Z"
        />
        <path
          fill="#002d72"
          d="M11.88,28.73c-.15,0-.31-.06-.42-.17-.23-.23-.23-.61,0-.85l2.07-2.08c.23-.23.61-.23.85,0,.23.23.23.61,0,.85l-2.07,2.08c-.12.12-.27.18-.42.18Z"
        />
        <path
          fill="#002d72"
          d="M11.44,20.6h-2.94c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.94c.33,0,.6.27.6.6s-.27.6-.6.6Z"
        />
        <path
          fill="#002d72"
          d="M13.95,14.55c-.15,0-.31-.06-.42-.18l-2.07-2.08c-.23-.23-.23-.61,0-.85.23-.23.61-.23.85,0l2.07,2.08c.23.23.23.61,0,.85-.12.12-.27.17-.42.17Z"
        />
      </svg>
    )
  }
}
