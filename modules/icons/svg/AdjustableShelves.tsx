import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const AdjustableShelves = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#e5efff" cx="24" cy="24" r="24" />
        <g>
          <g>
            <path fill="#fff" d="M38.89,30.58H9.11v3.07h29.78v-3.07Z" />
            <path
              fill="#002d72"
              d="M38.89,34.25H9.11c-.33,0-.6-.27-.6-.6v-3.07c0-.33.27-.6.6-.6h29.78c.33,0,.6.27.6.6v3.07c0,.33-.27.6-.6.6ZM9.71,33.05h28.58v-1.87H9.71v1.87Z"
            />
          </g>
          <g>
            <path fill="#fff" d="M38.89,22.52H9.11v3.07h29.78v-3.07Z" />
            <path
              fill="#002d72"
              d="M38.89,26.18H9.11c-.33,0-.6-.27-.6-.6v-3.07c0-.33.27-.6.6-.6h29.78c.33,0,.6.27.6.6v3.07c0,.33-.27.6-.6.6ZM9.71,24.98h28.58v-1.87H9.71v1.87Z"
            />
          </g>
          <g>
            <path fill="#fff" d="M38.89,14.45H9.11v3.07h29.78v-3.07Z" />
            <path
              fill="#002d72"
              d="M38.89,18.12H9.11c-.33,0-.6-.27-.6-.6v-3.07c0-.33.27-.6.6-.6h29.78c.33,0,.6.27.6.6v3.07c0,.33-.27.6-.6.6ZM9.71,16.92h28.58v-1.87H9.71v1.87Z"
            />
          </g>
          <rect fill="#002d72" x="11.86" y="17.52" width="1.2" height="5" />
          <rect fill="#002d72" x="11.86" y="25.94" width="1.2" height="4.64" />
          <rect fill="#002d72" x="35.3" y="17.52" width="1.2" height="5" />
          <rect fill="#002d72" x="35.3" y="25.94" width="1.2" height="4.64" />
          <path
            fill="#002d72"
            d="M12.46,38.05c-.33,0-.6-.27-.6-.6v-3.22c0-.33.27-.6.6-.6s.6.27.6.6v3.22c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M35.89,38.05c-.33,0-.6-.27-.6-.6v-3.22c0-.33.27-.6.6-.6s.6.27.6.6v3.22c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M12.46,14.37c-.33,0-.6-.27-.6-.6v-3.22c0-.33.27-.6.6-.6s.6.27.6.6v3.22c0,.33-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M35.89,14.37c-.33,0-.6-.27-.6-.6v-3.22c0-.33.27-.6.6-.6s.6.27.6.6v3.22c0,.33-.27.6-.6.6Z"
          />
        </g>
      </svg>
    )
  }
}
