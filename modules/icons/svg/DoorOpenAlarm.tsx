import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const DoorOpenAlarm = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#e5efff" cx="24" cy="24" r="24" />
        <g>
          <g>
            <path
              fill="#002d72"
              d="M14.44,22.95s-.02,0-.03,0c-.33-.02-.58-.3-.57-.63l.24-4.28c.19-3.31,2-6.3,4.86-8,.29-.17.65-.08.82.21.17.28.08.65-.21.82-2.51,1.49-4.11,4.12-4.27,7.03l-.24,4.28c-.02.32-.28.57-.6.57Z"
            />
            <path
              fill="#002d72"
              d="M33.56,22.89c-.32,0-.58-.25-.6-.57l-.24-4.23c-.16-2.91-1.76-5.54-4.27-7.03-.29-.17-.38-.54-.21-.82.17-.29.54-.38.82-.21,2.86,1.69,4.67,4.68,4.86,8l.24,4.23c.02.33-.23.61-.57.63-.01,0-.02,0-.03,0Z"
            />
          </g>
          <g>
            <path
              fill="#fff"
              fillRule="evenodd"
              d="M21.7,34.67v1.03c0,1.27,1.03,2.3,2.3,2.3s2.3-1.03,2.3-2.3v-1.03"
            />
            <path
              fill="#002d72"
              d="M24,38.6c-1.6,0-2.9-1.3-2.9-2.9v-1.03h1.2v1.03c0,.94.76,1.7,1.7,1.7s1.7-.76,1.7-1.7v-1.03h1.2v1.03c0,1.6-1.3,2.9-2.9,2.9Z"
            />
          </g>
          <path
            fill="#002d72"
            d="M24,12.79c-.33,0-.6-.27-.6-.6v-2.19c0-.33.27-.6.6-.6s.6.27.6.6v2.19c0,.33-.27.6-.6.6Z"
          />
          <g>
            <path
              fill="#fff"
              fillRule="evenodd"
              d="M24,12.19c-3.5,0-6.38,2.73-6.58,6.22l-.47,8.32v.06c-.02.26-.04.6-.08.9-.05.34-.16.94-.54,1.55-.44.69-1.07,1.09-1.29,1.23h0c-.29.19-.64.38-.98.57l-.06.03c-.6.33-.99.96-.99,1.68,0,1.06.86,1.91,1.91,1.91h18.17c1.06,0,1.91-.86,1.91-1.91,0-.72-.39-1.35-.99-1.68l-.06-.03c-.34-.19-.7-.38-.98-.56h0c-.23-.15-.86-.55-1.29-1.24-.38-.61-.49-1.21-.54-1.55-.04-.3-.06-.64-.07-.9v-.06s-.47-8.32-.47-8.32c-.2-3.49-3.08-6.22-6.58-6.22h0Z"
            />
            <path
              fill="#002d72"
              d="M33.09,35.27H14.91c-1.39,0-2.51-1.13-2.51-2.51,0-.92.5-1.76,1.3-2.2l.41.46-.29-.52c.31-.17.63-.35.89-.51.19-.12.74-.46,1.12-1.05.31-.5.41-.99.46-1.32.04-.27.05-.59.07-.83l.47-8.4c.21-3.81,3.37-6.79,7.18-6.79s6.96,2.98,7.18,6.79l.46,8.32c.02.3.04.63.08.91.04.32.14.81.45,1.31.37.59.92.93,1.1,1.04.29.18.63.37.96.55.87.48,1.37,1.32,1.37,2.24,0,1.39-1.13,2.51-2.51,2.51ZM15.4,30.96l-.04.03c-.28.17-.62.36-.94.54h0l-.12.07c-.42.23-.68.67-.68,1.15,0,.72.59,1.31,1.31,1.31h18.17c.72,0,1.31-.59,1.31-1.31,0-.48-.26-.92-.68-1.15-.41-.23-.78-.43-1.07-.61-.25-.16-.98-.61-1.49-1.43-.49-.77-.59-1.54-.63-1.79-.04-.32-.07-.68-.08-.95l-.47-8.38c-.18-3.17-2.8-5.65-5.98-5.65s-5.8,2.48-5.98,5.65l-.46,8.32c-.02.35-.04.7-.08,1.01-.04.26-.15,1.02-.63,1.79-.49.78-1.17,1.23-1.45,1.4Z"
            />
          </g>
        </g>
      </svg>
    )
  }
}
