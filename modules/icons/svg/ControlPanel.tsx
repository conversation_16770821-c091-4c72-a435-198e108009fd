import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const ControlPanel = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#e5efff" cx="24" cy="24" r="24" />
        <g>
          <g>
            <path
              fill="#fff"
              d="M37.35,33.58c0,.65-.42,1.23-1.03,1.45-7.94,3.05-16.72,3.05-24.65,0-.61-.22-1.02-.8-1.03-1.45V14.32c0-.65.41-1.23,1.03-1.44,3.81-1.45,7.85-2.2,11.93-2.2,4.32.03,8.62.79,12.69,2.23.63.21,1.06.8,1.06,1.46v19.21Z"
            />
            <path
              fill="#002d72"
              d="M24,37.92c-4.25,0-8.5-.78-12.54-2.33-.83-.3-1.4-1.1-1.41-2V14.32c0-.91.58-1.72,1.44-2.01,3.88-1.48,7.97-2.23,12.12-2.23,4.4.03,8.73.79,12.89,2.26.87.29,1.46,1.1,1.46,2.03v19.21c0,.9-.57,1.71-1.43,2.01-4.04,1.55-8.28,2.33-12.53,2.33ZM23.6,11.28c-4,0-7.95.73-11.71,2.16-.39.13-.64.48-.64.88v19.26c0,.39.26.75.63.88,7.82,3.01,16.42,3.01,24.23,0,.39-.14.65-.5.65-.89V14.37c0-.4-.26-.76-.65-.89-4.04-1.43-8.25-2.17-12.5-2.2Z"
            />
          </g>
          <path
            fill="#002d72"
            d="M33.23,27.87H14.78c-.9,0-1.63-.73-1.63-1.63h.58-.59v-8.54c0-.9.73-1.63,1.63-1.63h18.46c.42,0,.83.17,1.14.46.32.32.49.73.49,1.16v8.54c0,.42-.16.83-.46,1.14-.32.32-.73.49-1.17.49ZM14.34,25.93v.31c.01.24.2.43.44.43h18.45c.12,0,.23-.04.31-.13.07-.08.12-.19.12-.3v-8.54c0-.12-.04-.23-.12-.31-.08-.08-.19-.12-.31-.12H14.77c-.24,0-.43.19-.43.43v8.23Z"
          />
          <path
            fill="#002d72"
            d="M21.37,24.81c-.79,0-1.52-.42-1.92-1.1-.4-.68-.4-1.53,0-2.21l1.09-1.88c.33-.58,1.32-.59,1.66,0l1.08,1.88c.4.68.4,1.53,0,2.21-.4.68-1.13,1.1-1.92,1.1ZM21.37,20.57l-.88,1.52c-.18.31-.18.7,0,1.01.37.63,1.39.63,1.76,0,.18-.31.18-.7,0-1.01l-.88-1.52ZM21.58,20.2h0s0,0,0,0Z"
          />
          <path
            fill="#002d72"
            d="M30.5,19.96h-2.89c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.89c.33,0,.6.27.6.6s-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M30.54,22.57h-2.93c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.93c.33,0,.6.27.6.6s-.27.6-.6.6Z"
          />
          <path
            fill="#002d72"
            d="M30.54,25.26h-2.93c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.93c.33,0,.6.27.6.6s-.27.6-.6.6Z"
          />
          <g>
            <path
              fill="#002d72"
              d="M25.44,32.36h-2.89c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.89c.33,0,.6.27.6.6s-.27.6-.6.6Z"
            />
            <path
              fill="#002d72"
              d="M18.79,32.36h-2.93c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.93c.33,0,.6.27.6.6s-.27.6-.6.6Z"
            />
            <path
              fill="#002d72"
              d="M32.14,32.36h-2.93c-.33,0-.6-.27-.6-.6s.27-.6.6-.6h2.93c.33,0,.6.27.6.6s-.27.6-.6.6Z"
            />
          </g>
        </g>
      </svg>
    )
  }
}
