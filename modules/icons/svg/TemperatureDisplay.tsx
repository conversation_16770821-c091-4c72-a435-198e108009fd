import React from 'react'

import { PathProps } from '../../shared/icons/index'

export const TemperatureDisplay = {
  viewBox: '0 0 48 48',
  path(props: PathProps) {
    return (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <circle fill="#e5efff" cx="24" cy="24" r="24" />
        <g>
          <g>
            <g>
              <path
                fill="#fff"
                d="M23.98,17.91c5.39,0,10.23,2.34,13.59,6.04l4.74-4.74c-4.58-4.91-11.08-8-18.32-8s-13.71,3.07-18.28,7.96l4.74,4.74c3.36-3.68,8.18-6,13.55-6Z"
              />
              <path
                fill="#002d72"
                d="M37.57,24.55h-.01c-.16,0-.32-.08-.43-.2-3.37-3.71-8.16-5.84-13.14-5.84s-9.74,2.11-13.1,5.8c-.11.12-.27.19-.43.2-.15,0-.32-.06-.44-.17l-4.74-4.74c-.23-.23-.24-.6-.01-.83,4.92-5.25,11.57-8.15,18.72-8.15s13.84,2.91,18.76,8.19c.22.24.21.6-.02.83l-4.74,4.74c-.11.11-.26.18-.42.18ZM23.98,17.31c5.11,0,10.03,2.1,13.6,5.78l3.88-3.88c-4.65-4.77-10.84-7.39-17.49-7.39s-12.8,2.61-17.44,7.35l3.88,3.88c3.57-3.65,8.47-5.73,13.56-5.73Z"
              />
            </g>
            <g>
              <path
                fill="#002d72"
                d="M33.81,21.39c-.1,0-.21-.03-.3-.08-.29-.17-.38-.54-.21-.82l3.28-5.6c.17-.28.53-.38.82-.21.29.17.38.54.21.82l-3.28,5.6c-.11.19-.31.3-.52.3Z"
              />
              <path
                fill="#002d72"
                d="M29.95,15.26s-.1,0-.15-.02c-.32-.08-.51-.41-.43-.73l.57-2.19c.08-.32.41-.51.73-.43.32.08.51.41.43.73l-.57,2.19c-.07.27-.31.45-.58.45Z"
              />
            </g>
            <g>
              <path
                fill="#002d72"
                d="M18.05,15.26c-.27,0-.51-.18-.58-.45l-.57-2.18c-.08-.32.11-.65.43-.73.32-.08.65.11.73.43l.57,2.18c.08.32-.11.65-.43.73-.05.01-.1.02-.15.02Z"
              />
              <path
                fill="#002d72"
                d="M12.07,17.76c-.21,0-.41-.11-.52-.3l-1.15-1.95c-.17-.29-.07-.65.21-.82.29-.17.65-.07.82.21l1.15,1.95c.17.29.07.65-.21.82-.1.06-.2.08-.3.08Z"
              />
            </g>
            <path
              fill="#002d72"
              d="M23.99,14.63c-.33,0-.6-.27-.6-.6v-2.38c0-.33.26-.6.6-.6h0c.33,0,.6.27.6.6v2.38c0,.33-.26.6-.6.6h0Z"
            />
            <path
              fill="#002d72"
              d="M24.02,36.41c-.33,0-.6-.27-.6-.6l-.03-19.05c0-.33.27-.6.6-.6h0c.33,0,.6.27.6.6l.03,19.05c0,.33-.27.6-.6.6h0Z"
            />
          </g>
          <g>
            <circle fill="#fff" cx="24.02" cy="37.5" r="1.69" />
            <path
              fill="#002d72"
              d="M24.02,39.78c-1.26,0-2.28-1.03-2.28-2.29s1.02-2.29,2.28-2.29,2.29,1.03,2.29,2.29-1.02,2.29-2.29,2.29ZM24.02,36.41c-.6,0-1.08.49-1.08,1.09s.49,1.09,1.08,1.09,1.09-.49,1.09-1.09-.49-1.09-1.09-1.09Z"
            />
          </g>
        </g>
      </svg>
    )
  }
}
