import { fireEvent, screen, waitFor } from '@testing-library/react'

import { renderWithProviders } from '@core/utils/testing'

import { BillingAddressForm } from '../BillingAddressForm'


describe('BillingAddressForm', () => {
  const defaultFormValues = {
    firstName: 'John',
    lastName: 'Doe',
    street: '123 Main St',
    buildingNumber: '1',
    postalCode: '12345',
    city: 'Anytown',
    province: 'as',
    country: {
      id: '80af7950-592c-11ed-9ae8-01c4b0a075cd',
      name: 'Italy'
    },
    additionalInformation: ''
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the form fields', () => {
    renderWithProviders(
      <BillingAddressForm
        defaultFormValues={defaultFormValues}
        asDefaultBilling={true}
      />
    )
    expect(
      screen.getByText(/REGULAR\.FIELDS\.FIRST_NAME\.LABEL/)
    ).toBeInTheDocument()
    expect(screen.getByText(/LAST_NAME/)).toBeInTheDocument()
    expect(screen.getByText(/STREET/)).toBeInTheDocument()
    expect(screen.getByText(/CITY/)).toBeInTheDocument()
    expect(screen.getByText(/POSTAL_CODE/)).toBeInTheDocument()
    expect(screen.getByText(/COUNTRY/)).toBeInTheDocument()
  })

  it('submits the form when the submit button is clicked', async () => {
    // const submitMock = jest.fn()
    const submitMock = jest.fn()
    renderWithProviders(
      <BillingAddressForm
        onSubmit={submitMock}
        defaultFormValues={defaultFormValues}
        asDefaultBilling={true}
        isLoading={false}
      />
    )

    fireEvent.click(
      screen.getByRole('button', {
        name: /CHECKOUT\.ADDRESS_CREATE\.CREATE_BTN/i
      })
    )

    await waitFor(() => {
      expect(submitMock).toHaveBeenCalledTimes(1)
    })
  })

  it('displays validation errors when the form is submitted with invalid data', async () => {
    const handleSubmit = jest.fn()
    renderWithProviders(
      <BillingAddressForm
        defaultFormValues={defaultFormValues}
        asDefaultBilling={true}
      />
    )
    fireEvent.change(screen.getByTestId('baseaddress_firstName'), {
      target: { value: '' }
    })
    fireEvent.submit(screen.getByTestId('base-address-form'))
    expect(
      await screen.findByText('REGULAR.ERROR_MESSAGES.FIRSTNAME.REQUIRED')
    ).toBeInTheDocument()
  })
})
