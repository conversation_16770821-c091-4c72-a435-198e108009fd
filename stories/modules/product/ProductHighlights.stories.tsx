import { ProductHighlights } from '@modules/product/components/product-highlights'
import { ProductIndexMockV2 } from '@modules/product/mocks/product-index/product-index-mock'
import { productDetailsMock } from '@modules/product/services/__test__/fixtures'
import { ProductAttributeDetails } from '@modules/product/types'

import { shippingThresholdProductMock } from './__mocks__'

export default {
  title: 'Modules/Product/ProductHighlights',
  component: ProductHighlights
}

const keyFeaturesMock = [
  'Good feature',
  'Water resistant',
  'High volume',
  'Nice quality'
]

const attributesMock = {
  color: {
    type: 1,
    id: '50dae0e4-f391-4195-bd19-54b2d12b53b7',
    code: 'color',
    frontend: '1',
    label: 'Farbe',
    value: 'braun',
    displayValue: null,
    measure: null
  }
}

// export const _KeyFeaturesHighlights = () => {
//   return (
//     <>
//       <ProductHighlights
//         keyFeatures={keyFeaturesMock}
//         artificialHighlights={[]}
//         attributesSPI={attributesMock}
//       />
//     </>
//   )
// }

// export const _KeyFeaturesWithTitleHighlights = () => {
//   return (
//     <>
//       <ProductHighlights
//         keyFeatures={keyFeaturesMock}
//         artificialHighlights={[]}
//         displayTitle
//         attributesSPI={attributesMock}
//       />
//     </>
//   )
// }

// export const _KeyFeaturesWithBrandHighlights = () => {
//   return (
//     <>
//       <ProductHighlights
//         keyFeatures={keyFeaturesMock}
//         artificialHighlights={[]}
//         attributesSPI={{
//           brand_series: {
//             id: 'a4b68ac2-59c3-423d-b420-bcdfabca5fd7',
//             code: 'brand_series',
//             label: 'Serie',
//             value: 'Elégance Nature',
//             displayValue: null,
//             frontend: '1',
//             measure: null
//           }
//         }}
//         displayBrandInfo
//         brand={{
//           name: 'Volkswagen',
//           id: '1',
//           slug: 'volkswagen'
//         }}
//       />
//     </>
//   )
// }

// export const _NoKeyFeatures = () => {
//   return (
//     <>
//       <ProductHighlights
//         attributesSPI={attributesMock}
//         keyFeatures={[]}
//         artificialHighlights={ProductIndexMockV2.item.artificialHighlights}
//       />
//     </>
//   )
// }

// export const _NoKeyFeaturesWithBrandHighlights = () => {
//   return (
//     <>
//       <ProductHighlights
//         keyFeatures={[]}
//         attributesSPI={attributesMock}
//         artificialHighlights={ProductIndexMockV2.item.artificialHighlights}
//         displayBrandInfo
//         brand={{
//           name: 'Volkswagen',
//           id: '1',
//           slug: 'volkswagen'
//         }}
//       />
//     </>
//   )
// }
