{"name": "mm-react", "version": "0.1.0", "private": true, "scripts": {"gitify": "pnpm lint && pnpm build && pnpm storybook:build && jest --watchAll ", "dev": "next dev", "dev:custom:server": "node server.js", "build": "next build --debug", "analyze": "ANALYZE=true next build --debug", "ci:build": "next build --debug && pnpm install --production --ignore-scripts --prefer-offline", "start": "next start", "start:custom:server": "cross-env NODE_ENV=production node server.js", "start:custom:server:locally": "pnpm build && node customServerForLocalTesting.js && pnpm start:custom:server", "lint": "next lint", "lint:fix": "next lint --fix", "storybook": "storybook dev -p 6006", "storybook:debug": "storybook dev --debug-webpack -p 6006", "storybook:build": "storybook build", "test": "jest --watch", "test:coverage": "jest --coverage --watchAll ", "test:ci": "jest --ci --runInBand --logHeapUsage --detectOpenHandles --coverage", "generate-types": "pnpm exec openapi --exportCore false --exportServices false --exportSchemas false", "generate-types:spi": "pnpm run generate-types -i https://service-product-index.pp-de.metro-marketplace.cloud/api/doc/v2/webapp.yaml -o ./modules/product/types/spi", "generate-types:app-search-2": "pnpm run generate-types -i https://app-search-2.prod.de.metro-marketplace.cloud/api/docs.json -o ./modules/search/types/app-search-2", "prepare": "husky"}, "dependencies": {"@google-cloud/logging-winston": "^6.0.0", "@hookform/resolvers": "^2.9.11", "@imgix/js-core": "^3.8.0", "@metromarkets/message-center-sdk": "^1.9.15", "@next/bundle-analyzer": "^14.1.4", "@next/env": "^14.1.4", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.54.2", "@opentelemetry/auto-instrumentations-node": "^0.53.0", "@opentelemetry/exporter-jaeger": "^1.29.0", "@opentelemetry/exporter-trace-otlp-http": "^0.55.0", "@opentelemetry/instrumentation": "^0.54.2", "@opentelemetry/resources": "^1.28.0", "@opentelemetry/sdk-logs": "^0.54.2", "@opentelemetry/sdk-node": "^0.55.0", "@opentelemetry/sdk-trace-node": "^1.28.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@opentelemetry/winston-transport": "^0.9.0", "@optimizely/optimizely-sdk": "^5.3.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^1.9.0", "@sentry/nextjs": "^8.42.0", "@storyblok/react": "^2.4.1", "@tanstack/react-table": "^8.11.0", "@types/file-saver": "^2.0.7", "@vercel/otel": "^1.10.0", "axios": "^0.26.1", "axios-mock-adapter": "^1.22.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "configcat-common": "^6.0.0", "configcat-js-ssr": "^6.0.1", "cookie": "^0.6.0", "embla-carousel": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "express": "^4.18.1", "express-prometheus-middleware": "^1.2.0", "file-saver": "^2.0.5", "html-react-parser": "^5.1.10", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "i18next-intervalplural-postprocessor": "^3.0.0", "immer": "^10.0.3", "input-otp": "^1.2.3", "iron-session": "^6.1.2", "js-cookie": "^3.0.5", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "lodash.truncate": "^4.4.2", "lucide-react": "^0.363.0", "next": "^15.1.8", "next-i18next": "^15.3.1", "next-redux-wrapper": "^8.1.0", "next-seo": "^5.5.0", "openid-client": "^5.6.5", "path": "^0.12.7", "plyr": "^3.7.8", "postcss": "^8.4.38", "react": "18.3.1", "react-confetti": "^6.1.0", "react-device-detect": "^2.2.2", "react-dom": "18.3.1", "react-hook-form": "^7.43.5", "react-i18next": "^12.0.0", "react-redux": "^8.0.5", "react-swipeable": "^7.0.1", "react-use": "^17.5.0", "react-zoom-pan-pinch": "^3.4.4", "redis": "^4.6.13", "rxjs": "^7.8.1", "sanitize-html": "^2.13.0", "sass": "^1.72.0", "swiper": "6.8.4", "swr": "^1.3.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vaul": "^0.9.0", "winston": "^3.13.0", "yup": "^1.4.0"}, "devDependencies": {"@axe-core/playwright": "4.10.1", "@babel/core": "7.24.3", "@emotion/react": "latest", "@emotion/styled": "latest", "@playwright/test": "^1.49.1", "@storybook/addon-actions": "^8.6.7", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-interactions": "^8.6.7", "@storybook/addon-links": "^8.6.7", "@storybook/addon-viewport": "^8.6.7", "@storybook/cli": "^8.6.7", "@storybook/components": "^8.6.7", "@storybook/core-events": "^8.6.7", "@storybook/nextjs": "^8.6.7", "@storybook/preset-scss": "^1.0.3", "@storybook/react": "^8.6.7", "@storybook/theming": "^8.6.7", "@svgr/webpack": "^6.1.0", "@tailwindcss/typography": "^0.5.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.2.2", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/cookie": "^0.6.0", "@types/ineum": "^216.0.3", "@types/jest": "^27.4.0", "@types/lodash.clonedeep": "^4.5.9", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.12.2", "@types/react": "^17.0.34", "@types/redis": "^2.8.32", "@types/sanitize-html": "^2.11.0", "@types/testing-library__jest-dom": "5.14.6", "autoprefixer": "^10.4.2", "axe-html-reporter": "^2.2.11", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "eslint": "9.27.0", "eslint-config-next": "15.1.8", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-quotes": "^0.0.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-testing-library": "^6.2.0", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "isomorphic-fetch": "^3.0.0", "jest": "^27.5.1", "jest-transform-stub": "^2.0.0", "msw": "^1.3.2", "node-mocks-http": "^1.12.2", "openapi-typescript-codegen": "^0.28.0", "postcss-loader": "^8.1.1", "prettier": "^3.2.5", "prom-client": "^15.1.1", "sass-loader": "14.1.1", "storybook": "^8.6.7", "storybook-react-i18next": "^3.0.1", "style-loader": "3.3.4", "tailwindcss": "^3.4.3", "tailwindcss-hyphens": "^0.1.0", "ts-jest": "^27.1.3", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^3.5.0", "typescript": "^4.9.5", "util": "^0.12.5", "webpack": "^5.91.0"}, "engines": {"node": ">=20.0.0"}, "msw": {"workerDirectory": "public"}}