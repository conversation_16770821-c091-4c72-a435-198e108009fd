import { OHPTestData } from '../olc/types'

export const ohpData: OHPTestData = {
    selectors: {
        orderHistoryLink: 'a[data-testid="orders-history-link"]',
        orderTab: 'button[data-testid="orders-history-tab"]',
        buyAgainTab: 'button[data-testid="buy-again-tab"]',
        orderNumber: '[data-testid="orders-history-order-header-id"]',
        orderTotalPrice: '[data-testid="orders-history-order-header-total"]',
        productName: '[data-testid= "orders-history-order-line-product-name"]',
        orderStatus: '[data-testid="order-line-status-info"]:visible',
        orderHeader: '[data-testid="orders-history-order-header-status"]',
        viewOrderDetailsButton: 'button[data-testid= "orders-history-order-header-go-order-details"]'
    },
    apiEndpoints: {}
}