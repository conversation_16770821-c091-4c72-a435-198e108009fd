/**
 * Collection of API endpoints used in tests
 */

// Environment configuration
export type Environment = 'integration' | 'pp'

// Current environment from Playwright config or default to integration
export const CURRENT_ENV: Environment =
  (process.env.TEST_ENV as Environment) || 'integration'

// Domain configurations by environment
export const ENV_DOMAINS = {
  integration: {
    MARKETPLACE: 'integration.marketplace-pp.metro.de',
    INFRA: 'integration.infra.metro-markets.cloud',
    IDAM: 'idam-pp.makro.es'
  },
  pp: {
    MARKETPLACE: 'marketplace-pp.metro.de',
    INFRA: 'pp-de.metro-marketplace.cloud',
    IDAM: 'idam-pp.makro.es'
  }
}

// Dynamic base URLs based on current environment
export const MARKETPLACE_URL = `https://${ENV_DOMAINS[CURRENT_ENV].MARKETPLACE}`
export const APP_BUYER_ACCOUNT_URL = `https://app-buyer-account.${ENV_DOMAINS[CURRENT_ENV].INFRA}`
export const SPI_URL = `https://service-product-index.${ENV_DOMAINS[CURRENT_ENV].INFRA}`
export const APP_SEARCH_URL = `https://app-search-2.${ENV_DOMAINS[CURRENT_ENV].INFRA}`
export const IDAM_URL = `https://${ENV_DOMAINS[CURRENT_ENV].IDAM}/authorize/api/`
export const SERVICE_BUYER_GATEWAY_URL = `https://service-buyer-gateway.${ENV_DOMAINS[CURRENT_ENV].INFRA}`

// Environment-specific filter values
export const SELLER_FILTER =
  CURRENT_ENV === 'integration'
    ? 'filter[options][seller][0]=Quality%20Store'
    : 'filter[options][seller][0]=DE%20Autotests'

// API endpoints interfaces
export interface IdamEndpoints {
  TOKEN_BUYER: string
  CACHE_ACTIVE_CONTEXT: string
  ACCOUNT_ME_FULL: string
}

export interface ShoppingListEndpoints {
  GET_LISTS: string
  DELETE_LIST: string
}

export interface SearchEndpoints {
  SEARCH_V3: string
  FILTERS: string
  SUGGESTIONS: string
}

export interface CartEndpoints {
  POST_OFFER_INVENTORY: string
}

export interface ServiceBuyerGatewayEndpoints {
  CART: string
  PLACE_ORDER: string
  GET_CART: string
  GET_ORDER: string
}

export interface SPIEndpoints {
  PRODUCT_INDEX_INTERNAL: string
}

// API endpoints implementation
export const API_ENDPOINTS = {
  IDAM: {
    TOKEN_BUYER: `${IDAM_URL}/oauth2/access_token`,
    CACHE_ACTIVE_CONTEXT: `${APP_BUYER_ACCOUNT_URL}/api/v1/account-info/cache`,
    ACCOUNT_ME_FULL: `${APP_BUYER_ACCOUNT_URL}/api/account/v2/account/me/full`
  } as IdamEndpoints,

  SHOPPING_LIST: {
    GET_LISTS: `${MARKETPLACE_URL}/marktplatz/app-api/shopping-list/get-lists`,
    DELETE_LIST: `${MARKETPLACE_URL}/marktplatz/app-api/shopping-list/delete`
  } as ShoppingListEndpoints,

  SEARCH: {
    SEARCH_V3: `${APP_SEARCH_URL}/api/v3/search`,
    FILTERS: `${APP_SEARCH_URL}/api/v3/filters`,
    SUGGESTIONS: `${APP_SEARCH_URL}/api/v3/suggestions`
  } as SearchEndpoints,

  SPI: {
    PRODUCT_INDEX_INTERNAL: `${SPI_URL}/api/internal/products`
  } as SPIEndpoints,

  CART: {
    POST_OFFER_INVENTORY: `${MARKETPLACE_URL}/marktplatz/app-api/cart/post-offer-inventory`
  } as CartEndpoints,

  SERVICE_BUYER_GATEWAY: {
    CART: `${SERVICE_BUYER_GATEWAY_URL}/api/account/proxy/cart/v1/cart`,
    PLACE_ORDER: `${SERVICE_BUYER_GATEWAY_URL}/api/account/proxy/checkout_v2/checkout/place-order`,
    GET_ORDER: `${SERVICE_BUYER_GATEWAY_URL}/api/account/proxy/aom/v2/orders`
  } as ServiceBuyerGatewayEndpoints
}
