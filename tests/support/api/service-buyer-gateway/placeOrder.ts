import { Page } from '@playwright/test'
import { API_ENDPOINTS } from 'tests/support/constants'
import { USER_PAYMENT_INFO } from 'tests/support/olc/payment-scheme'

export async function placeOrder(
  page: Page,
  email: string
): Promise<any> {

  // Step 1: Get access_token from sessionStorage in the browser
  const accessToken = await page.evaluate(() => {
    return sessionStorage.getItem('access_token')
  })
  const context = page.context()
  if (!accessToken) {
    throw new Error('No access_token found in sessionStorage')
  }
  // Step 2: POST request - make the payment
  const url = API_ENDPOINTS.SERVICE_BUYER_GATEWAY.PLACE_ORDER
  const response = await context.request.post(url, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    },
    data: {
      ...USER_PAYMENT_INFO,
      shopperEmail: email
    }
  })
  if (response.ok){
    // Parse the response body
    const responseBody = await response.json()
    // Get orderNumber
    const orderNumber = responseBody.orderNumber
    return orderNumber

  } else {
    throw new Error(
      `Failed to place an order: ${response.status()} - ${response.statusText()}`
    )
  }
}