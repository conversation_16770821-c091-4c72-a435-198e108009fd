import { Page } from '@playwright/test'
import { API_ENDPOINTS } from 'tests/support/constants'

export async function getOrder(
    page: Page,
    orderNumber: string,
    customerTier: string = "T1"
): Promise<any> {

    // Step 1: Get access_token from sessionStorage in the browser
    const accessToken = await page.evaluate(() => {
        return sessionStorage.getItem('access_token')
    })
    const context = page.context()
    if (!accessToken) {
        throw new Error('No access_token found in sessionStorage')
    }
    // Step 2: Determine active context based on customerTier
    const activeContext = customerTier === "T2" ? "parentAccount" : "personal";
    // Step 3: GET request - get order details
    const url = `${API_ENDPOINTS.SERVICE_BUYER_GATEWAY.GET_ORDER}?OrderNumber=${orderNumber}`
    const response = await context.request.get(url, {
        headers: {
            Authorization: `Bearer ${accessToken}`,
            'X-Active-Context': activeContext
        }
    })
    // Step 4: Check for a successful response
    if (!response.ok()) {
        throw new Error(
            `Failed to fetch offers from cart: ${response.status()} - ${response.statusText()}`
        )
    }
    return await response.json()
}