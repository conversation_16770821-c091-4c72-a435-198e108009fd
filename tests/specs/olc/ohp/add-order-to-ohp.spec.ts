import { getProductIdAndOfferId } from 'tests/getProductIdAndOfferId'
import useTestAccount from 'tests/getTestAccount'
import { test, expect } from 'tests/global-setup'
import {
  buyerLogin,
  addOfferToCart,
  placeOrder,
  getOrder
} from 'tests/support/api'
import { SELLER_FILTER } from 'tests/support/constants'
import { ohpData } from 'tests/support/olc/ohpData'
import { acceptCookies, clearCart, navigateAndWait, checkElementsVisibility } from 'tests/support/utils'

const { selectors } = ohpData

let testAccount

test.beforeEach(async ({ }, testInfo) => {
  testAccount = useTestAccount(testInfo)
})

test.describe('OHP: Add order to OHP and validate OHP', () => {
  test.skip('Add product to cart and place an order', async ({ page }) => {

    const url = "marktplatz/account/orders-history"

    // Bypass CORS
    await page.route('**/*', (route) => {
      route.continue({
        headers: {
          ...route.request().headers(),
          'Access-Control-Allow-Origin': '*'
        }
      })
    })

    // Login
    await buyerLogin(page, testAccount.email, testAccount.password)

    //Get an offer with dynamic seller filter
    const { offerId } = await getProductIdAndOfferId('de', SELLER_FILTER)

    // Clean up cart
    await clearCart(page)


    //Add offer to cart (api call)
    await addOfferToCart(page, offerId)

    //Place an order (api call) - invalid dependency
    const orderNumber = await placeOrder(page, testAccount.email)

    //Get order details (api call) - total price, product name, status
    const orderDetails = await getOrder(page, orderNumber)
    const totalGross = orderDetails.items[0].orderFinancialDetails.totalGross.amount
    const productName = orderDetails.items[0].orderLines[0].productDetails.name

    //Navigate to order history page
    await navigateAndWait(page, url)
    await acceptCookies(page)

    //Check Tabs visibility
    await checkElementsVisibility(page, selectors.orderTab)
    await checkElementsVisibility(page, selectors.buyAgainTab)

    //Check 'View order details' button visibility
    await checkElementsVisibility(page, selectors.viewOrderDetailsButton)

    //Check 'order status' visibility
    await checkElementsVisibility(page, selectors.orderStatus)

    //Check 'Order header' visibility and today date
    await checkElementsVisibility(page, selectors.orderHeader)
    const today = new Date();
    const day = today.getDate();
    const month = today.toLocaleString('de-DE', { month: 'long' }); //DE
    const year = today.getFullYear();
    const formatted = `${day} ${month}, ${year}`;
    await expect(page.locator(selectors.orderHeader).first().locator('*')).toHaveText(formatted);

    //Validate Order details
    await expect(page.locator(selectors.orderNumber).first()).toHaveText(orderNumber); // order Number
    await expect(page.locator(selectors.orderTotalPrice).first().locator('*')).toContainText(['Gesamtbetrag:']); //DE
    await expect(page.locator(selectors.orderTotalPrice).first().locator('*')).toContainText([totalGross.replace(".", ",")]); // total Gross
    await expect(page.locator(selectors.productName).first()).toContainText([productName]); // product name

  })
})
